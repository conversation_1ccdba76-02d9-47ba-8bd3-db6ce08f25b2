# Super Admin Subscription Management

## Overview
The super admin subscription management system allows administrators to manage pricing plans, monitor subscriptions, and oversee the entire subscription ecosystem for the Bridge platform.

## Features

### 1. Navigation Access
Super admins can access the subscription management system through the sidebar navigation:
- **Subscriptions > Overview** - Dashboard with statistics and metrics
- **Subscriptions > Pricing Plans** - Manage subscription pricing plans
- **Subscriptions > Company Subscriptions** - View and manage all company subscriptions

### 2. Subscription Overview Dashboard (`/super/subscription`)
Provides a comprehensive view of the subscription system:

#### Statistics Cards
- **Subscription Statistics**
  - Total Subscriptions
  - Active Subscriptions
  - Trial Subscriptions
  - Canceled Subscriptions
  
- **Revenue Metrics**
  - Monthly Recurring Revenue (MRR)
  - Annual Revenue Projection

#### Plan Distribution
Shows how many companies are on each plan type with visual cards displaying:
- Plan name and status
- Number of subscriptions
- Base price
- Job advert limits

#### Recent Subscriptions Table
Lists the most recent 10 subscriptions with:
- Company name
- Plan name
- Status (with color-coded chips)
- Billing period
- Start date
- Quick view actions

### 3. Pricing Plans Management (`/super/subscription/pricing-plans`)
Complete CRUD operations for pricing plans:

#### Features
- **Create New Plans**
  - Set plan name, type, and description
  - Configure base monthly price
  - Set job advert limits (-1 for unlimited)
  - Add feature list
  - Configure pricing for different billing periods (Monthly, Quarterly, Yearly)

- **Edit Existing Plans**
  - Update all plan details
  - Modify pricing tiers
  - Add/remove features
  - Activate/deactivate plans

- **View All Plans**
  - Data table with sorting and pagination
  - Color-coded plan types (Starter, Professional, Enterprise)
  - Status indicators (Active/Inactive)
  - Quick edit and delete actions

### 4. Company Subscriptions Management (`/super/subscription/companies`)
Comprehensive subscription management for all companies:

#### Features
- **Search and Filter**
  - Search by company name or plan name
  - Filter by subscription status (Active, Trial, Past Due, Canceled)
  - Filter by billing period (Monthly, Quarterly, Yearly, One-time)

- **Subscription Table**
  - Company details with ID
  - Plan information
  - Status with color-coded chips
  - Billing period and amount
  - Job advert usage (used/limit)
  - Subscription dates (start and expiry)

- **Actions Menu**
  - View company details
  - Cancel active subscriptions
  - Reactivate canceled subscriptions

- **Pagination**
  - 10 items per page
  - Navigation controls for large datasets

## Component Structure

### Pages
```
src/pages/super/subscription/
├── index.vue           # Overview dashboard
├── pricing-plans.vue   # Pricing plans management
└── companies.vue       # Company subscriptions management
```

### Components
```
src/components/Admin/
└── PricingPlanManagement.vue  # Reusable pricing plan CRUD component
```

### Navigation Updates
The super admin navigation menu (`src/navigation/vertical/index.ts`) has been updated with a dedicated "Subscriptions" section containing all management links.

## Technical Implementation

### GraphQL Integration
The system uses the following GraphQL hooks:
- `usePricingPlanGraph` - For pricing plan operations
- `useCompanySubscriptionGraph` - For subscription management

### State Management
- Reactive state management using Vue's composition API
- Real-time updates after mutations
- Optimistic UI updates with loading states

### Security
- Permission checks using `usePermissions` composable
- Only super admins can access these pages
- Unauthorized users see a "Not Authorized" message

## Future Enhancements

### Backend Requirements
The following features need backend implementation:
1. **GET_ALL_SUBSCRIPTIONS** query for fetching all company subscriptions
2. **REACTIVATE_SUBSCRIPTION** mutation for reactivating canceled subscriptions
3. **GET_SUBSCRIPTION_METRICS** query for aggregate statistics
4. **Webhook integration** for real-time subscription status updates

### Planned Features
1. **Export functionality** - Download subscription data as CSV/Excel
2. **Email notifications** - Automated emails for subscription events
3. **Advanced analytics** - Churn rate, LTV, conversion metrics
4. **Bulk operations** - Update multiple subscriptions at once
5. **Audit log** - Track all subscription-related changes
6. **Custom pricing** - Per-company pricing overrides

## Usage Flow

### Creating a New Pricing Plan
1. Navigate to **Subscriptions > Pricing Plans**
2. Click "Create Plan" button
3. Fill in plan details (name, type, description)
4. Set base monthly price and job advert limits
5. Add features (one per line)
6. Configure pricing for different billing periods
7. Click "Create" to save

### Managing Company Subscriptions
1. Navigate to **Subscriptions > Company Subscriptions**
2. Use search/filters to find specific subscriptions
3. Click the menu icon for available actions:
   - View company details
   - Cancel subscription (with confirmation)
   - Reactivate canceled subscription

### Monitoring Subscription Health
1. Navigate to **Subscriptions > Overview**
2. Review statistics cards for quick insights
3. Check plan distribution to see popular plans
4. Review recent subscriptions for activity

## Testing Recommendations

### Test Scenarios
1. **Create pricing plan** with all billing periods
2. **Edit existing plan** and verify changes
3. **Deactivate plan** and check if new subscriptions are prevented
4. **Search and filter** company subscriptions
5. **Cancel subscription** with confirmation dialog
6. **Navigate between pages** using breadcrumbs and buttons

### Edge Cases
- Plans with unlimited job adverts (-1)
- Plans with $0 price (free tier)
- Subscriptions in PAST_DUE status
- Companies with multiple subscriptions

## Notes for Developers

### Mock Data
Currently, some features use mock data for demonstration:
- `loadAllSubscriptions` in `useCompanySubscriptionGraph` returns sample data
- Backend implementation is needed for full functionality

### Error Handling
- All mutations include error handling with user-friendly messages
- Loading states prevent duplicate submissions
- Confirmation dialogs for destructive actions

### Responsive Design
- All pages are responsive and work on mobile devices
- Tables use horizontal scrolling on small screens
- Cards stack vertically on mobile layouts
