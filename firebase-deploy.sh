#!/bin/bash

# Usage: ./deploy.sh [develop|public]

if [ "$1" = "dev" ]; then
  site="develop-bridge"
  env="dev"
elif [ "$1" = "prod" ]; then
  site="bridge-public"
  env="prod"
else
  echo "Usage: ./firebase-deploy.sh [dev|prod]"
  exit 1
fi

# Build first
echo "Building for $env environment..."
yarn build:$env

# Create temporary firebase config
echo "Creating firebase.json for $site"
cat > firebase.json << EOF
{
  "hosting": {
    "public": "dist",
    "site": "$site",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
EOF

# Deploy
firebase deploy --only hosting:$site
