/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
    "\n  mutation CreateJobAdvert($createJobAdvertInput: CreateJobAdvertInput!) {\n    createJobAdvert(createJobAdvertInput: $createJobAdvertInput) {\n      id\n      title\n      active\n      address\n      applicantCount\n      approved\n      bookmarkCount\n      category {\n        name\n      }\n      city\n      latitude\n      longitude\n      geoLatitude\n      geoLongitude\n      isDeclined\n      isDeleted\n      description\n      declineReason\n      detailDescription\n      district\n      educationDuration\n      headerImageUrl\n      imageUrl\n      holidayDays\n      impressions\n      companyUserId\n      plan\n      startDate\n      endDate\n      status\n      type\n      createdAt\n      updatedAt\n      companyId\n    }\n  }\n": types.CreateJobAdvertDocument,
    "\n  query About {\n    about\n  }\n": types.AboutDocument,
    "\n  query GetJobAdvertsByCompanyId($companyId: Int!) {\n    jobAdvertsByCompanyId(companyId: $companyId) {\n      id\n      title\n      district\n      status\n      createdAt\n    }\n  }\n": types.GetJobAdvertsByCompanyIdDocument,
    "\n  query allJobAdverts {\n    allJobAdverts {\n      id\n      address\n    }\n  }\n": types.AllJobAdvertsDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateJobAdvert($createJobAdvertInput: CreateJobAdvertInput!) {\n    createJobAdvert(createJobAdvertInput: $createJobAdvertInput) {\n      id\n      title\n      active\n      address\n      applicantCount\n      approved\n      bookmarkCount\n      category {\n        name\n      }\n      city\n      latitude\n      longitude\n      geoLatitude\n      geoLongitude\n      isDeclined\n      isDeleted\n      description\n      declineReason\n      detailDescription\n      district\n      educationDuration\n      headerImageUrl\n      imageUrl\n      holidayDays\n      impressions\n      companyUserId\n      plan\n      startDate\n      endDate\n      status\n      type\n      createdAt\n      updatedAt\n      companyId\n    }\n  }\n"): (typeof documents)["\n  mutation CreateJobAdvert($createJobAdvertInput: CreateJobAdvertInput!) {\n    createJobAdvert(createJobAdvertInput: $createJobAdvertInput) {\n      id\n      title\n      active\n      address\n      applicantCount\n      approved\n      bookmarkCount\n      category {\n        name\n      }\n      city\n      latitude\n      longitude\n      geoLatitude\n      geoLongitude\n      isDeclined\n      isDeleted\n      description\n      declineReason\n      detailDescription\n      district\n      educationDuration\n      headerImageUrl\n      imageUrl\n      holidayDays\n      impressions\n      companyUserId\n      plan\n      startDate\n      endDate\n      status\n      type\n      createdAt\n      updatedAt\n      companyId\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query About {\n    about\n  }\n"): (typeof documents)["\n  query About {\n    about\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetJobAdvertsByCompanyId($companyId: Int!) {\n    jobAdvertsByCompanyId(companyId: $companyId) {\n      id\n      title\n      district\n      status\n      createdAt\n    }\n  }\n"): (typeof documents)["\n  query GetJobAdvertsByCompanyId($companyId: Int!) {\n    jobAdvertsByCompanyId(companyId: $companyId) {\n      id\n      title\n      district\n      status\n      createdAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query allJobAdverts {\n    allJobAdverts {\n      id\n      address\n    }\n  }\n"): (typeof documents)["\n  query allJobAdverts {\n    allJobAdverts {\n      id\n      address\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;