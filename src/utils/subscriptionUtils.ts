import { useUnlimitedSubscriptionStatus } from '@/composables/StripeSubscription/useUnlimitedSubscriptionStatus'

/**
 * Check if a company needs to pay for creating a job advert
 * Returns false if they have an active unlimited subscription
 */
export const requiresPaymentForJobAdvert = async (): Promise<boolean> => {
  const { hasActiveUnlimitedSubscription, refetch } = useUnlimitedSubscriptionStatus()
  
  // Refetch to get the latest status
  await refetch()
  
  // If they have an active unlimited subscription, no payment required
  if (hasActiveUnlimitedSubscription.value) {
    return false
  }
  
  // Otherwise, payment is required
  return true
}

/**
 * Get the appropriate checkout route based on subscription status
 */
export const getCheckoutRoute = async (jobAdvertId?: string) => {
  const needsPayment = await requiresPaymentForJobAdvert()
  
  if (!needsPayment) {
    // Has unlimited subscription, go directly to success or job advert creation
    return {
      name: 'company-job-advert-create',
      query: jobAdvertId ? { id: jobAdvertId } : {}
    }
  }
  
  // Needs payment, go to checkout
  return {
    name: 'company-subscription-checkout',
    query: jobAdvertId ? { jobAdvertId } : {}
  }
}

/**
 * Check if company can create job adverts without payment
 */
export const canCreateUnlimitedJobAdverts = (): boolean => {
  const { hasActiveUnlimitedSubscription } = useUnlimitedSubscriptionStatus()
  return hasActiveUnlimitedSubscription.value
}

/**
 * Get subscription display info for UI
 */
export const getSubscriptionDisplayInfo = () => {
  const { 
    hasActiveUnlimitedSubscription, 
    unlimitedSubscriptionDetails,
    remainingDays,
    isExpiringSort 
  } = useUnlimitedSubscriptionStatus()
  
  if (hasActiveUnlimitedSubscription.value) {
    return {
      type: 'unlimited',
      label: 'Unlimited',
      description: 'Unbegrenzte Stellenanzeigen',
      icon: 'mdi-infinity',
      color: 'primary',
      expiresIn: remainingDays.value,
      isExpiring: isExpiringSort.value,
      details: unlimitedSubscriptionDetails.value
    }
  }
  
  return {
    type: 'per-job',
    label: 'Pro Stellenanzeige',
    description: 'Bezahlung pro Stellenanzeige',
    icon: 'mdi-currency-eur',
    color: 'secondary',
    expiresIn: null,
    isExpiring: false,
    details: null
  }
}
