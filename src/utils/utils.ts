import i18n from '@/plugins/i18n'

// @ts-expect-error
export const inlineTranslate = (title: string) => i18n.global.t(title)

export const paginationMeta = <
  T extends { page: number; itemsPerPage: number },
>(
  options: T,
  total: number,
) => {
  const start = (options.page - 1) * options.itemsPerPage + 1
  const end = Math.min(options.page * options.itemsPerPage, total)

  return `${total === 0 ? 0 : start} von ${total} Einträgen angezeigt `
}
export const avatarText = (value: string) => {
  if (!value) return ''
  const nameArray = value.split(' ')

  return nameArray.map(word => word.charAt(0).toUpperCase()).join('')
}

export const getMimeType = (
  file: ArrayBuffer,
  fallback: string | null = null,
) => {
  const byteArray = new Uint8Array(file).subarray(0, 4)
  let header = ''
  for (let i = 0; i < byteArray.length; i++) header += byteArray[i].toString(16)

  switch (header) {
    case '89504e47':
      return 'image/png'
    case '47494638':
      return 'image/gif'
    case 'ffd8ffe0':
    case 'ffd8ffe1':
    case 'ffd8ffe2':
    case 'ffd8ffe3':
    case 'ffd8ffe8':
      return 'image/jpeg'
    default:
      return fallback
  }
}

/**
 * Calculates the difference in days between today and the given timestamp
 * @param {Timestamp} timestamp
 *
 * @returns {Number} Difference in days
 */

// 2023-09-01T00:00:00.000Z
export const calcTimeDifference = (timestamp: any): string | number => {
  // Handle date string
  if (typeof timestamp === 'string') {
    timestamp = new Date(timestamp)
  }

  if (!(timestamp instanceof Date)) {
    return '-'
  }

  const now = new Date()
  return Math.round(
    (timestamp.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
  )
}
/**
 * Calculates the current age in years from the given timestamp
 * @param birthDate Birthdate as Firestore timestamp
 * @returns
 */
export const calcAge = (birthDate: string | Date) => {
  if (!birthDate) {
    return undefined
  }

  if (typeof birthDate === 'string') {
    birthDate = new Date(birthDate)
  }
  const diff_ms = Date.now() - birthDate.getTime()
  const age_dt = new Date(diff_ms)
  return Math.abs(age_dt.getUTCFullYear() - 1970)
}

export const secondsToAge = (seconds: number): number => {
  const milliseconds = seconds * 1000
  const now = Date.now()

  const diff = now - milliseconds
  const ageDate = new Date(diff)

  return Math.abs(ageDate.getUTCFullYear() - 1970)
}

export const timestampToAge = (timestamp: string): number => {
  const date = new Date(timestamp)
  const now = new Date()

  let age = now.getFullYear() - date.getFullYear()
  const monthDiff = now.getMonth() - date.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < date.getDate())) {
    age--
  }

  return age
}

export const arrayBufferToBase64 = (buffer: ArrayBuffer) => {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  const len = bytes.byteLength
  for (let i = 0; i < len; i++) binary += String.fromCharCode(bytes[i])

  return window.btoa(binary)
}

export const dataUrlToBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url)

  const arrayBuffer = await response.arrayBuffer()

  return arrayBufferToBase64(arrayBuffer)

  const blob = await response.blob()

  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) resolve(reader.result as string)
      }
      reader.readAsDataURL(blob)
    } catch (e) {
      reject(e)
    }
  })
}

export const capitalizeFirstLetter = (data: string): string => {
  if (typeof data !== 'string' || data.length === 0) {
    return data
  }
  return data.charAt(0).toUpperCase() + data.slice(1).toLowerCase()
}

export const generateRandomString = () => {
  const length = 10
  const charset =
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let password = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length)
    password += charset[randomIndex]
  }
  return password
}
