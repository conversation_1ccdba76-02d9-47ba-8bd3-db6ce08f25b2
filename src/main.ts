/* eslint-disable import/order */
import '@/@iconify/icons-bundle'
import App from '@/App.vue'
import layoutsPlugin from '@/plugins/layouts'
import vuetify from '@/plugins/vuetify'
import chartsPlugin from '@/plugins/charts'
import { loadFonts } from '@/plugins/webfontloader'
import router from '@/router'
import '@core/scss/template/index.scss'
import '@styles/styles.scss'
import { createApp, h, provide } from 'vue'
import { abilitiesPlugin } from '@casl/vue'
import ability from '@/plugins/casl/ability'
import { useAuthStore } from './stores/authStore'
import { useFilterStore } from './stores/fairs/filterStore'
import store from '@/stores'
import i18n from '@/plugins/i18n'
import { DefaultApolloClient } from '@vue/apollo-composable'
import { apolloClient } from './api/middleware/apolloClient'
import Plugin from '@storipress/apollo-vue-devtool'

loadFonts()

// Create vue app
const app = createApp(App)

// Provide Apollo client to the app
app.provide(DefaultApolloClient, apolloClient) // Add this line

// CASL plugin
app.use(abilitiesPlugin, ability, {
  useGlobalProperties: true,
})

app.use(store)
app.use(i18n)
app.use(layoutsPlugin)
app.use(vuetify)
app.use(chartsPlugin)
app.use(Plugin)

const authStore = useAuthStore()

authStore.init().then(async () => {
  // Hydrate the filter store to restore persisted data
  const filterStore = useFilterStore()
  filterStore.hydrateStore()

  // Router needs to be initialized after Firabase auth, so that user is available if already logged in
  app.use(router)
  app.mount('#app')
})
