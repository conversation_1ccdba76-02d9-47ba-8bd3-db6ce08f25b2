import { inlineTranslate } from '@/utils/utils'

export const companyNav = computed(() => {
  const showPricingPlans = true

  const baseNav = [
    {
      heading: inlineTranslate('Job offer'),
    },
    {
      title: inlineTranslate('Dashboard'),
      to: { name: 'company' },
      icon: { icon: 'material-symbols:dashboard-outline' },
    },
    {
      title: inlineTranslate('New Position'),
      to: { name: 'company-new-job-ad' },
      icon: { icon: 'material-symbols:new-window' },
    },
    {
      heading: inlineTranslate('Business'),
    },
    {
      title: inlineTranslate('Profile'),
      to: { name: 'company-edit-company' },
      icon: { icon: 'mdi-company' },
    },
    {
      title: inlineTranslate('User'),
      to: { name: 'company-users' },
      icon: { icon: 'mdi-users-group' },
    },
    {
      title: inlineTranslate('Personal Link'),
      to: { name: 'company-link' },
      icon: { icon: 'mdi-link-variant' },
    },
    {
      heading: inlineTranslate('Help'),
    },
    {
      title: inlineTranslate('Support'),
      to: { name: 'company-support' },
      icon: { icon: 'material-symbols:contact-support-outline-rounded' },
    },
    {
      heading: inlineTranslate('Legal'),
    },
    {
      title: 'AGBs',
      href: 'https://bridge-app.de/agb/',
      icon: { icon: 'mdi-contract-outline' },
    },
    {
      title: inlineTranslate('Data Protection'),
      href: 'https://bridge-app.de/datenschutz/',
      icon: { icon: 'material-symbols:shield-outline-rounded' },
    },

    {
      heading: inlineTranslate('Billing'),
    },
    {
      title: inlineTranslate('Overview'),
      to: { name: 'company-subscription' },
      icon: { icon: 'tabler-credit-card' },
    },
  ]

  // Only add Pricing Plans if subscription type is COMPANY_UNLIMITED
  if (showPricingPlans) {
    baseNav.push({
      title: inlineTranslate('Pricing Plans'),
      to: { name: 'company-subscription-plans' },
      icon: { icon: 'mdi-cash-multiple' },
    })
  }

  // Add the rest of navigation items
  baseNav.push(
    {
      heading: inlineTranslate('Fairs'),
    },
    {
      title: inlineTranslate('Fair Management'),
      to: { name: 'company-fair-management' },
      icon: { icon: 'tabler-planet' },
    },
    {
      title: inlineTranslate('Appointments'),
      to: { name: 'company-appointments' },
      icon: { icon: 'tabler-calendar' },
    },
  )

  return baseNav
})

export const superCompanyNav = computed(() => {
  return [
    {
      heading: inlineTranslate('Job offer'),
    },
    {
      title: inlineTranslate('Dashboard'),
      to: { name: 'company' },
      icon: { icon: 'material-symbols:dashboard-outline' },
    },
    {
      title: inlineTranslate('New Position'),
      to: { name: 'company-new-job-ad' },
      icon: { icon: 'material-symbols:new-window' },
    },
    {
      heading: inlineTranslate('Business'),
    },
    {
      title: inlineTranslate('Profile'),
      to: { name: 'company-edit-company' },
      icon: { icon: 'mdi-company' },
    },
    {
      title: inlineTranslate('User'),
      to: { name: 'company-users' },
      icon: { icon: 'mdi-users-group' },
    },
    {
      title: inlineTranslate('Personal Link'),
      to: { name: 'company-link' },
      icon: { icon: 'mdi-link-variant' },
    },
    {
      heading: inlineTranslate('Billing'),
    },
    {
      title: inlineTranslate('Overview'),
      to: { name: 'company-subscription' },
      icon: { icon: 'tabler-credit-card' },
    },
    {
      title: inlineTranslate('Pricing Plans'),
      to: { name: 'company-subscription-plans' },
      icon: { icon: 'mdi-cash-multiple' },
    },
    {
      title: inlineTranslate('Manage Subscription'),
      to: { name: 'company-subscription-management' },
      icon: { icon: 'mdi-cog-outline' },
    },
    {
      heading: inlineTranslate('Fairs'),
    },
    {
      title: inlineTranslate('Fair Management'),
      to: { name: 'company-fair-management' },
      icon: { icon: 'tabler-planet' },
    },
    {
      title: inlineTranslate('Appointments'),
      to: { name: 'company-appointments' },
      icon: { icon: 'tabler-calendar' },
    },
  ]
})

export const superNav = computed(() => {
  return [
    {
      heading: inlineTranslate('Job offer'),
    },
    {
      title: inlineTranslate('Dashboard'),
      to: { name: 'super' },
      icon: { icon: 'material-symbols:dashboard-outline' },
    },
    {
      title: inlineTranslate('Business'),
      to: { name: 'super-company' },
      icon: { icon: 'mdi-company' },
    },
    {
      title: inlineTranslate('Applicant'),
      to: { name: 'super-applicant' },
      icon: { icon: 'mdi-users-group' },
    },
    {
      title: inlineTranslate('Job Adverts'),
      to: { name: 'super-job-ads' },
      icon: { icon: 'material-symbols:new-window' },
    },
    {
      heading: inlineTranslate('Subscriptions'),
    },
    {
      title: inlineTranslate('Overview'),
      to: { name: 'super-subscription' },
      icon: { icon: 'mdi-chart-line' },
    },
    {
      title: inlineTranslate('Pricing Plans'),
      to: { name: 'super-subscription-pricing-plans' },
      icon: { icon: 'mdi-cash-multiple' },
    },
    {
      title: inlineTranslate('Subscriptions'),
      to: { name: 'super-subscription-companies' },
      icon: { icon: 'mdi-domain' },
    },
    {
      title: inlineTranslate('Subscription Config'),
      to: { name: 'super-subscription-config' },
      icon: { icon: 'mdi-cog-outline' },
    },
    {
      heading: inlineTranslate('Fairs'),
    },
    {
      title: inlineTranslate('Fair Dashboard'),
      to: { name: 'fair' },
      icon: { icon: 'material-symbols:dashboard-outline' },
    },
    {
      title: inlineTranslate('Fairs'),
      to: { name: 'fair-fairs' },
      icon: { icon: 'tabler-planet' },
    },
    {
      title: inlineTranslate('Appointments'),
      to: { name: 'fair-appointments' },
      icon: { icon: 'tabler-calendar' },
    },
    {
      title: inlineTranslate('Job offers'),
      to: { name: 'fair-jobs' },
      icon: { icon: 'tabler-square-rounded-plus' },
    },
  ]
})

export const fgAdminNav = computed(() => {
  return [
    {
      title: inlineTranslate('Dashboard'),
      to: { name: 'fair' },
      icon: { icon: 'material-symbols:dashboard-outline' },
    },
    {
      heading: inlineTranslate('NAVIGATION'),
    },
    {
      title: inlineTranslate('Fairs'),
      to: { name: 'fair-fairs' },
      icon: { icon: 'tabler-planet' },
    },
    {
      title: inlineTranslate('Business'),
      to: { name: 'fair-company' },
      icon: { icon: 'mdi-company' },
    },
    {
      title: inlineTranslate('Appointments'),
      to: { name: 'fair-appointments' },
      icon: { icon: 'tabler-calendar' },
    },
    {
      title: inlineTranslate('Job offers'),
      to: { name: 'fair-jobs' },
      icon: { icon: 'tabler-square-rounded-plus' },
    },
    {
      heading: inlineTranslate('Help'),
    },
    {
      title: inlineTranslate('Support'),
      to: { name: 'company-support' },
      icon: { icon: 'material-symbols:contact-support-outline-rounded' },
    },
    {
      heading: inlineTranslate('Legal'),
    },
    {
      title: 'AGBs',
      href: 'https://bridge-app.de/agb/',
      icon: { icon: 'mdi-contract-outline' },
    },
    {
      title: inlineTranslate('Data Protection'),
      href: 'https://bridge-app.de/datenschutz/',
      icon: { icon: 'material-symbols:shield-outline-rounded' },
    },
  ]
})
