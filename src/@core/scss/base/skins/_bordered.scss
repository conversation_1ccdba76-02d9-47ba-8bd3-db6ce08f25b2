@use "sass:map";
@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;
@use "../utils";

$header: ".layout-navbar";

@if variables.$layout-vertical-nav-navbar-is-contained {
  $header: ".layout-navbar .navbar-content-container";
}

.skin--bordered {
  @include mixins.bordered-skin(".v-card:not(.v-card .v-card):not(.v-card--flat)");
  @include mixins.bordered-skin(".v-menu .v-overlay__content > .v-card, .v-menu .v-overlay__content > .v-sheet, .v-menu .v-overlay__content > .v-list");
  @include mixins.bordered-skin(".popper-content");

  // Navbar
  // -- Horizontal
  @include mixins.bordered-skin(".layout-navbar-and-nav-container", "border-bottom");

  // -- Vertical
  @if variables.$layout-vertical-nav-navbar-is-contained {
    @include mixins.bordered-skin(".layout-nav-type-vertical.window-scrolled #{$header}");
    .layout-nav-type-vertical.window-scrolled #{$header} {
      border-block-start: none;
    }
  } @else {
    @include mixins.bordered-skin(".layout-nav-type-vertical.window-scrolled #{$header}", "border-bottom");
  }

  // Footer
  // -- Vertical
  @include mixins.bordered-skin(".layout-nav-type-vertical.layout-footer-sticky .layout-footer .footer-content-container");

  .layout-nav-type-vertical.layout-footer-sticky .layout-footer .footer-content-container {
    border-block-end: none;
  }

  // -- Horizontal
  @include mixins.bordered-skin(".layout-nav-type-horizontal.layout-footer-sticky .layout-footer");

  .layout-nav-type-horizontal.layout-footer-sticky .layout-footer {
    border-block-end: none;
  }

  /*
    Missing components:
     - Stepper
  */

  .v-theme--light {
    --skin-theme-background:
      #{utils.map-deep-get(
        variables.$css-vars,
        "bordered",
        "--v-theme-background",
        "light"
      )};
    --skin-theme-surface:
      #{utils.map-deep-get(
        variables.$css-vars,
        "bordered",
        "--v-theme-surface",
        "light"
      )};
  }

  .v-theme--dark {
    --skin-theme-background:
      #{utils.map-deep-get(
        variables.$css-vars,
        "bordered",
        "--v-theme-background",
        "dark"
      )};
    --skin-theme-surface:
      #{utils.map-deep-get(
        variables.$css-vars,
        "bordered",
        "--v-theme-surface",
        "dark"
      )};
  }

  // Vertical Nav
  .layout-vertical-nav {
    border-inline-end: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}
