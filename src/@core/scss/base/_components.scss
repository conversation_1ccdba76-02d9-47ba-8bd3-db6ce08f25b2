@use "vuetify/lib/styles/tools/_elevation" as mixins_elevation;
@use "@layouts/styles/placeholders";
@use "@configured-variables" as variables;

// 👉 Avatar group
.v-avatar-group {
  display: flex;
  align-items: center;

  > * {
    &:not(:first-child) {
      margin-inline-start: -0.8rem;
    }

    transition: transform 0.25s ease, box-shadow 0.15s ease;

    &:hover {
      z-index: 2;
      transform: translateY(-5px) scale(1.05);

      @include mixins_elevation.elevation(3);
    }
  }

  > .v-avatar {
    border: 2px solid rgb(var(--v-theme-surface));
  }
}

// Dialog responsive width
.v-dialog {
  .v-card {
    @extend %style-scroll-bar;
  }
}

@media (min-width: 576px) {
  .v-dialog {
    &.v-dialog-sm,
    &.v-dialog-lg,
    &.v-dialog-xl {
      inline-size: 565px !important;
    }
  }
}

@media (min-width: 992px) {
  .v-dialog {
    &.v-dialog-lg,
    &.v-dialog-xl {
      inline-size: 865px !important;
    }
  }
}

@media (min-width: 1200px) {
  .v-dialog.v-dialog-xl,
  .v-dialog.v-dialog-xl .v-overlay__content > .v-card {
    inline-size: 1165px !important;
  }
}

// v-tab with pill support

.v-tabs.v-tabs-pill {
  .v-tab.v-btn {
    border-radius: 6px !important;
    transition: none;

    .v-tab__slider {
      visibility: hidden;
    }
  }

  .v-slide-group__content {
    transition: none;
  }
}

// loop for all colors bg
@each $color-name in variables.$theme-colors-name {
  .v-tabs.v-tabs-pill {
    .v-slide-group-item--active.v-tab--selected.text-#{$color-name} {
      background-color: rgb(var(--v-theme-#{$color-name}));
      color: rgb(var(--v-theme-on-#{$color-name})) !important;
    }
  }
}

// ℹ️ We are make even width of all v-timeline body
.v-timeline--vertical.v-timeline {
  .v-timeline-item {
    .v-timeline-item__body {
      justify-self: stretch !important;
    }
  }
}
