@use "@configured-variables" as variables;
@use "@styles/variables/vuetify";
@use "vuetify/lib/styles/tools/_elevation" as mixins_elevation;

.v-switch.v-input,
.v-checkbox-btn,
.v-radio-btn,
.v-radio {
  --v-input-control-height: auto;

  flex: unset;
}

// 👉 checkbox size and box shadow
.v-checkbox-btn {
  .v-selection-control__input {
    .v-icon.iconify--custom {
      border-radius: 0.25rem;
      block-size: 1.125rem !important;
      font-size: 1.125rem !important;
      inline-size: 1.125rem !important;
    }
  }

  .v-selection-control--dirty {
    .v-selection-control__input {
      .v-icon.iconify--custom {
        @include mixins_elevation.elevation(2);
      }
    }
  }
}

// 👉 radio size and box shadow
.v-radio,
.v-radio-btn {
  .v-selection-control__input {
    .iconify--custom {
      border-radius: 5rem;
      block-size: 1.125rem !important;
      font-size: 1.125rem !important;
      inline-size: 1.125rem !important;
    }
  }

  &.v-selection-control--dirty {
    .v-selection-control__input {
      .iconify--custom {
        @include mixins_elevation.elevation(2);
      }
    }
  }
}

// 👉 Icon stroke width
svg.iconify * {
  stroke-width: 1.5;
}

// 👉 Alert
// ℹ️ custom icon styling

$alert-prepend-icon-font-size: 1.125rem !important;

.v-alert:not(.v-alert--prominent) {
  .v-alert__prepend {
    z-index: 1;
    padding: 0.25rem;
    border-radius: 0.375rem;
    background-color: #fff;

    .v-icon {
      block-size: $alert-prepend-icon-font-size;
      font-size: $alert-prepend-icon-font-size;
      inline-size: $alert-prepend-icon-font-size;
    }
  }
}

@each $color-name in variables.$theme-colors-name {
  .v-alert:not(.v-alert--prominent).bg-#{$color-name},
  .v-alert:not(.v-alert--prominent).text-#{$color-name} {
    .v-alert__prepend {
      color: rgb(var(--v-theme-#{$color-name})) !important;
    }
  }
}

// 👉 badge
.v-badge__badge .v-icon {
  margin-block-end: 0.125rem !important;
}

// 👉 VSliderThumb
.v-slider-thumb {
  border: 3px solid rgb(var(--v-theme-surface));
  border-radius: 5rem;

  @include mixins_elevation.elevation(3);
}

.v-slider-thumb__label {
  background: vuetify.$tooltip-background-color !important;
  color: rgb(var(--v-theme-on-primary)) !important;
}

.v-slider.v-input--horizontal .v-slider-thumb__label::before {
  border-block-start-color: vuetify.$tooltip-background-color !important;
}

.v-slider.v-input--vertical .v-slider-thumb__label::before {
  border-inline-end-color: vuetify.$tooltip-background-color !important;
}
