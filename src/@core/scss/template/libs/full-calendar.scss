@use "vuetify/lib/styles/tools/elevation" as elevation;

.fc {
  --fc-today-bg-color: rgba(var(--v-theme-on-surface), 0.04);
  --fc-border-color: rgba(var(--v-border-color), var(--v-border-opacity));
  --fc-neutral-bg-color: rgb(var(--v-theme-background));
  --fc-list-event-hover-bg-color: rgba(var(--v-theme-on-surface), 0.02);
  --fc-page-bg-color: rgb(var(--v-theme-surface));
  --fc-event-border-color: currentcolor;

  a {
    color: inherit;
  }

  .fc-timegrid-divider {
    padding: 0;
  }

  .fc-col-header-cell-cushion {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
    font-size: 0.875rem;
    font-weight: 600;
  }

  .fc-toolbar .fc-toolbar-title {
    margin-inline-start: 0.25rem;
  }

  .fc-event-time {
    font-size: 0.75rem;
  }

  .fc-timegrid-event {
    .fc-event-title {
      font-size: 0.875rem;
    }
  }

  .fc-prev-button {
    padding-inline-start: 0;
  }

  .fc-prev-button,
  .fc-next-button {
    padding: 0.25rem;
  }

  .fc-col-header .fc-col-header-cell .fc-col-header-cell-cushion {
    padding: 0.5rem;
    text-decoration: none !important;
  }

  .fc-timegrid .fc-timegrid-slots .fc-timegrid-slot {
    block-size: 3rem;
  }

  // Removed double border on left in list view
  .fc-list {
    border-inline-start-color: transparent;
    font-size: 0.875rem;

    .fc-list-day-cushion.fc-cell-shaded {
      color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      font-weight: 600;
    }

    .fc-list-event-time,
    .fc-list-event-title {
      color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
    }

    .fc-list-day .fc-list-day-text,
    .fc-list-day .fc-list-day-side-text {
      text-decoration: none;
    }
  }

  .fc-timegrid-axis {
    color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity));
    font-size: 0.75rem;
    text-transform: capitalize;
  }

  .fc-timegrid-slot-label-frame {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
    font-size: 0.75rem;
    text-align: center;
    text-transform: uppercase;
  }

  .fc-header-toolbar {
    flex-wrap: wrap;
    margin: 1.5rem;
    column-gap: 0.5rem;
    row-gap: 1rem;
  }

  .fc-toolbar-chunk {
    display: flex;
    align-items: center;

    .fc-button-group {
      .fc-button-primary {
        &,
        &:hover,
        &:not(.disabled):active {
          border-color: transparent;
          background-color: transparent;
          color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
        }

        &:focus {
          box-shadow: none !important;
        }
      }
    }

    &:last-child {
      .fc-button-group {
        border-radius: 0.375rem;

        .fc-button,
        .fc-button:active {
          background-color: rgba(var(--v-theme-primary), 0.16);
          color: rgb(var(--v-theme-primary));
          font-size: 0.9rem;
          letter-spacing: 0.0187rem;
          padding-inline: 1rem;
          text-transform: uppercase;

          &:not(:last-child) {
            border-inline-end: 0.0625rem solid rgba(var(--v-border-color), var(--v-border-opacity));
          }

          &.fc-button-active {
            background-color: rgba(var(--v-theme-primary), 0.24);
            color: rgb(var(--v-theme-primary));
          }
        }
      }
    }
  }

  .fc-toolbar-title {
    display: inline-block;
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
    font-size: 1.25rem;
    font-weight: 500;
  }

  // Calendar content container
  .fc-view-harness {
    min-block-size: 40.625rem;
  }

  .fc-event {
    border-color: transparent;
    margin-block-end: 0.3rem;
    padding-block: 0.1875rem;
    padding-inline: 0.3125rem;
  }

  .fc-event-main {
    color: inherit;
    font-size: 0.75rem;
    font-weight: 500;
    padding-inline: 0.25rem;
  }

  tbody[role="rowgroup"] {
    > tr > td[role="presentation"] {
      border: none;
    }
  }

  .fc-scrollgrid {
    border-inline-start: none;
  }

  .fc-daygrid-day {
    padding: 0.3125rem;
  }

  .fc-daygrid-day-number {
    padding-block: 0.5rem;
    padding-inline: 0.75rem;
  }

  .fc-daygrid-day {
    padding: 0.3125rem;
  }

  .fc-scrollgrid-section > * {
    border-inline-end-width: 0;
    border-inline-start-width: 0;
  }

  .fc-list-event-dot {
    color: inherit;

    --fc-event-border-color: currentcolor;
  }

  .fc-list-event {
    background-color: transparent !important;
  }

  .fc-popover {
    @include elevation.elevation(3);

    border-radius: 6px;

    .fc-popover-header,
    .fc-popover-body {
      padding: 0.5rem;
    }

    .fc-popover-title {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  // 👉 sidebar toggler
  .fc-toolbar-chunk {
    .fc-button-group {
      align-items: center;

      .fc-button .fc-icon {
        vertical-align: bottom;
      }

      // ℹ️ Below two `background-image` styles contains static color due to browser limitation of not parsing the css var inside CSS url()
      .fc-drawerToggler-button {
        display: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='rgba(94,86,105,0.68)' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M3 12h18M3 6h18M3 18h18'/%3E%3C/svg%3E");
        background-position: 50%;
        background-repeat: no-repeat;
        block-size: 1.5625rem;
        font-size: 0;
        inline-size: 1.5625rem;
        margin-inline-end: 0.25rem;

        @media (max-width: 1279px) {
          display: block !important;
        }

        .v-theme--dark & {
          background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='rgba(232,232,241,0.68)' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M3 12h18M3 6h18M3 18h18'/%3E%3C/svg%3E");
        }
      }
    }
  }
}
