@use "sass:map";
@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;

$header: ".layout-navbar";

@if variables.$layout-vertical-nav-navbar-is-contained {
  $header: ".layout-navbar .navbar-content-container";
}

.skin--bordered {
  // Navbar

  // -- Vertical
  @if variables.$layout-vertical-nav-navbar-is-contained {
    @include mixins.bordered-skin(".layout-nav-type-vertical #{$header}");
    .layout-nav-type-vertical.window-scrolled #{$header} {
      border-block-start: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    }
  } @else {
    @include mixins.bordered-skin(".layout-nav-type-vertical #{$header}", "border-bottom");
  }
}
