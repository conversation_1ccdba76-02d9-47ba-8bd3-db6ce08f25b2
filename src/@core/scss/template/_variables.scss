@forward "@core/scss/base/variables" with (
  $css-vars: (
    "default": (
      "--v-theme-background": (
        "light": (248,247,250),
        "dark": (37,41,60),
      ),
      "--v-theme-surface": (
        "light": (255, 255, 255),
        "dark": (47,51,73),
      ),
    ),
    "bordered": (
      "--v-theme-background": (
        "light": (255 ,255, 255),
        "dark": (47,51,73),
      ),
      "--v-theme-surface": (
        "light": (255, 255, 255),
        "dark": (47,51,73),
      ),
    ),
  ) !default,

  $default-layout-with-vertical-nav-navbar-footer-roundness: 6px !default,

  $vertical-nav-navbar-style: "floating" !default, // options: elevated, floating

  // 👉 Vertical nav
  $vertical-nav-background-color-rgb: var(--v-theme-surface) !default,
  // ℹ️ This is used to keep consistency between nav items and nav header left & right margin
  // This is used by nav items & nav header
  $vertical-nav-horizontal-spacing: 0.875rem !default,

  // Section title margin top (when its not first child)
  $vertical-nav-section-title-mt: 1.25rem !default,

  // Section title margin bottom
  $vertical-nav-section-title-mb: 0.375rem !default,

  // Vertical nav icons
  $vertical-nav-items-icon-size: 1.375rem !default,
  $vertical-nav-items-nested-icon-size: 0.425rem !default,
);
