@use "vuetify/lib/styles/tools/_elevation" as mixins_elevation;
@use "@layouts/styles/mixins" as layoutsMixins;

// 👉 dialog custom close btn
.v-dialog {
  // dialog custom close btn
  .v-dialog-close-btn {
    position: absolute;
    z-index: 1;
    border-radius: 0.375rem;
    background-color: rgb(var(--v-theme-surface)) !important;
    block-size: 2rem;
    color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity)) !important;
    inline-size: 2rem;
    inset-block-start: 0;
    inset-inline-end: 0;
    transform: translate(0.5rem, -0.5rem);

    @include layoutsMixins.rtl {
      transform: translate(-0.5rem, -0.5rem);
    }

    &:hover {
      transform: translate(0.3125rem, -0.3125rem);

      @include layoutsMixins.rtl {
        transform: translate(-0.3125rem, -0.3125rem);
      }
    }
  }

  // 👉 Btn custom style to scale on active state
  .v-btn {
    &:active {
      transform: scale(0.98);
    }
  }
}

@media (min-width: 1200px) {
  .v-dialog.v-dialog-xl,
  .v-dialog.v-dialog-xl .v-overlay__content > .v-card,
  .v-dialog.v-dialog-xl .v-overlay__content {
    inline-size: 1165px !important;
    max-inline-size: unset !important;
  }
}

// 👉 v-tab with pill support

.v-tabs {
  .v-btn {
    &:active {
      transform: none;
    }
  }
}

// 👉 expansion panel arrow font size
.v-expansion-panel-title {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  font-weight: 600;

  .v-expansion-panel-title__icon {
    font-size: 0.8125rem;
  }
}

// 👉 Expansion panels
.v-expansion-panel-title,
.v-expansion-panel-title--active,
.v-expansion-panel-title:hover,
.v-expansion-panel-title:focus,
.v-expansion-panel-title:focus-visible,
.v-expansion-panel-title--active:focus,
.v-expansion-panel-title--active:hover {
  .v-expansion-panel-title__overlay {
    opacity: 0 !important;
  }
}

// 👉 Set Elevation when panel open
.v-expansion-panels:not(.v-expansion-panels--variant-accordion) {
  .v-expansion-panel.v-expansion-panel--active {
    .v-expansion-panel__shadow {
      @include mixins_elevation.elevation(3);
    }
  }
}

// 👉 VMenu custom style
.v-menu.v-overlay {
  .v-overlay__content {
    .v-list {
      .v-list-item {
        border-radius: 0.375rem;
        margin-block: 0.25rem;
        margin-inline: 0.5rem;
        min-block-size: 2.25rem;

        &:first-child {
          margin-block-start: 0;
        }

        &:last-child {
          margin-block-end: 0;
        }
      }
    }
  }
}

// 👉 VPagination prev and next icon size
.v-pagination {
  .v-pagination__prev,
  .v-pagination__next {
    .v-btn--icon {
      font-size: 0.85rem;
    }
  }
}
