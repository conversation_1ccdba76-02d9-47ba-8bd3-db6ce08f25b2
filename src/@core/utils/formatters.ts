import { isToday } from './index'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

// Extend dayjs with UTC plugin
dayjs.extend(utc)

export const avatarText = (value: string) => {
  if (!value) return ''
  const nameArray = value.split(' ')

  return nameArray.map(word => word.charAt(0).toUpperCase()).join('')
}

// TODO: Try to implement this: https://twitter.com/fireship_dev/status/1565424801216311297
export const kFormatter = (num: number) => {
  const regex = /\B(?=(\d{3})+(?!\d))/g

  return Math.abs(num) > 9999
    ? `${Math.sign(num) * +(Math.abs(num) / 1000).toFixed(1)}k`
    : Math.abs(num).toFixed(0).replace(regex, ',')
}

/**
 * Format and return date in Humanize format
 * Intl docs: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/format
 * Intl Constructor: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat
 * @param {String} value date to format
 * @param {Intl.DateTimeFormatOptions} formatting Intl object to format with
 */
export const formatDate = (
  value: string,
  formatting: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: 'Europe/Berlin',
  },
) => {
  if (!value) return value
  // Use dayjs.utc to ensure consistent UTC handling
  const utcDate = dayjs.utc(value).toDate()
  return new Intl.DateTimeFormat('de-DE', formatting).format(utcDate)
}

/**
 * Return short human friendly month representation of date
 * Can also convert date to only time if date is of today (Better UX)
 * @param {String} value date to format
 * @param {Boolean} toTimeForCurrentDay Shall convert to time if day is today/current
 */
export const formatDateToMonthShort = (
  value: string,
  toTimeForCurrentDay = true,
) => {
  if (!value) return value
  // Use dayjs.utc to ensure consistent UTC handling
  const date = dayjs.utc(value).toDate()
  let formatting: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
  }

  if (toTimeForCurrentDay && isToday(date)) {
    formatting = { hour: '2-digit', minute: '2-digit', hour12: false }
  }

  return new Intl.DateTimeFormat('en-US', formatting).format(date)
}

export const formatDateToString = (date: Date, type = 'en') => {
  if (!date) return ''
  const formattedDate = dayjs.utc(date).format('YYYY-MM-DD')
  const [y, m, d] = formattedDate.split('-')
  return type === 'en' ? `${y}-${m}-${d}` : `${y}.${m}.${d}`
}

export const convertFairDaysToString = (fairDays: any) => {
  return fairDays?.map((day: any) => ({
    day: dayjs(day.day).format('YYYY-MM-DD'),
    // Convert UTC times to local timezone for display
    startTime: dayjs(day.startTime).local().format('HH:mm'),
    endTime: dayjs(day.endTime).local().format('HH:mm'),
  }))
}

export const convertFairDaysToDateTime = (fairDays: any) => {
  return fairDays?.map((day: any) => {
    const dayDate = new Date(day.day)
    const startTimeLocal = new Date(`${day.day}T${day.startTime}`)
    const endTimeLocal = new Date(`${day.day}T${day.endTime}`)

    return {
      day: dayDate.toISOString(),
      startTime: startTimeLocal.toISOString(),
      endTime: endTimeLocal.toISOString(),
    }
  })
}

export const timeAgo = (input: string): string => {
  if (!input) return ''
  const inputDate = dayjs.utc(input)
  const now = dayjs.utc()
  const diffInMilliseconds = now.valueOf() - inputDate.valueOf()

  const seconds = Math.floor(diffInMilliseconds / 1000)
  if (seconds < 60) {
    return `vor ${seconds} Sek`
  }

  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) {
    return `vor ${minutes} Min`
  }

  const hours = Math.floor(minutes / 60)
  if (hours < 24) {
    return `vor ${hours} Stun`
  }

  const days = Math.floor(hours / 24)
  return `vor ${days} Tag`
}
