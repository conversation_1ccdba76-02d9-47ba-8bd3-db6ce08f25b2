<script lang="ts" setup>
  import { useNotifications } from '@/composables/Notifications/useNotifications'
  import { usePusher } from '@/composables/usePusher'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import {
    avatarText,
    formatDateToString,
    timeAgo,
  } from '@core/utils/formatters'
  import type { Notification } from '@layouts/types'
  import likes from '@images/svg/likes.svg'
  import { useRouter } from 'vue-router'

  const {
    state: { userNotificationsList },
    actions: {
      initiateNotificationsListen,
      stopNotificationsListen,
      loadUserNotifications,
      handleMarkAsRead,
    },
  } = useNotifications()

  const authStore = useAuthStore()

  const router = useRouter()

  const appStore = useAppStore()

  const companyUserId = computed(() => authStore.claims.companyUserId)

  const isSuper = computed(
    () => authStore.claims.isSuperUser || authStore.claims.isFGAdmin,
  )

  const navToNotificationsTable = () => {
    router.push('/company/notifications')
  }

  onMounted(async () => {
    if (companyUserId && !isSuper.value) {
      await loadUserNotifications()
    }
    await initiateNotificationsListen()
  })

  onUnmounted(() => {
    stopNotificationsListen()
  })

  watchEffect(async () => {
    if (userNotificationsList.value) {
      await appStore.updateNotifications(userNotificationsList.value)
    }
  })

  const newNotifications = computed(
    () =>
      appStore?.notifications?.filter(
        (notification: Notification) => notification.isNew,
      ) || [],
  )
</script>

<template>
  <VBtn icon variant="text" color="default" size="small">
    <VBadge
      :model-value="!!newNotifications.length"
      color="error"
      :content="newNotifications.length"
    >
      <VIcon icon="tabler-bell" size="24" />
    </VBadge>

    <VMenu activator="parent" width="380px" location="bottom" offset="14px">
      <VList class="py-0">
        <!-- 👉 Header -->
        <VListItem
          :title="$t('Notifications')"
          class="notification-section"
          height="48px"
        >
          <template #append>
            <VChip v-if="newNotifications.length" color="primary" size="small">
              {{ newNotifications.length }} Neue
            </VChip>
          </template>
        </VListItem>

        <VDivider />

        <!-- 👉 Notifications list -->
        <template
          v-for="notification in newNotifications.slice(0, 5)"
          :key="notification.emailBody"
        >
          <VListItem
            :title="notification.emailSubject"
            :subtitle="notification.emailBody"
            link
            lines="one"
            min-height="66px"
            @click="navToNotificationsTable"
          >
            <!-- Slot: Prepend -->
            <!-- Handles Avatar: Image, Icon, Text -->
            <template #prepend>
              <VListItemAction start>
                <VAvatar
                  color="primary"
                  :image="likes"
                  :icon="'tabler-bell'"
                  size="40"
                  variant="tonal"
                >
                  <span>{{ avatarText(notification.emailSubject) }}</span>
                </VAvatar>
              </VListItemAction>
            </template>
            <template #append>
              <small class="whitespace-no-wrap text-medium-emphasis">
                {{ timeAgo(notification.time) }}
              </small>
            </template>
          </VListItem>
          <VDivider />
        </template>

        <!-- 👉 Footer -->
        <VListItem class="notification-section">
          <VBtn block @click="$router.push('/company/notifications')">
            LESEN SIE ALLE MITTEILUNGEN
          </VBtn>
        </VListItem>
      </VList>
    </VMenu>
  </VBtn>
</template>

<style lang="scss">
  .notification-section {
    padding: 14px !important;
  }
</style>
