const API_KEY = 'AIzaSyBM3rXF3Igx8Pr2dqkq_AxHJEL87zCTKhg'
const CALLBACK_NAME = 'placesAutocompleteCallback'
const PLACES_SCRIPT_ID = 'places-autocomplete-script'

let initialized = !!window.google
let resolveInitPromise: (value: unknown) => void

const initPromise = new Promise((resolve, _reject) => {
  resolveInitPromise = resolve
})

/**
 * <PERSON>ads Google places api script and sets the callback function
 * @returns {Promise} Returns initPromise
 */
export default async function init(): Promise<unknown> {
  if (initialized)
    return initPromise

  initialized = true;

  (window as Record<string, any>)[CALLBACK_NAME] = () => resolveInitPromise(window.google)

  if (document.getElementById(PLACES_SCRIPT_ID) === null) {
    const placesScript = document.createElement('script')

    placesScript.async = true
    placesScript.defer = true
    placesScript.setAttribute('src', `https://maps.googleapis.com/maps/api/js?key=${API_KEY}&libraries=places&callback=${CALLBACK_NAME}`)
    placesScript.setAttribute('id', PLACES_SCRIPT_ID)
    document.head.appendChild(placesScript)
  }

  return initPromise
}
