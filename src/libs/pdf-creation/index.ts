/* eslint-disable padding-line-between-statements */
import { useDynamicLinks } from '@/composables/useDynamicLinks'
import { useAppStore } from '@/stores/appStore'
import { jsPDF } from 'jspdf'
import { createQrCode } from '../qr-creation'
import template from './template'
import { dataUrlToBase64 } from '@/utils/utils'

const {
  actions: { getDynamicLinkForActiveCompany, getDynamicLinkForJobAd },
} = useDynamicLinks()

const LEFT_MARGIN = 60
const TITLE_POSITION = 490
const LINE_HEIGHT = 1.2
const FONT_SIZE_TITLE = 24
const FONT_SIZE_DESCRIPTION = 12

const generatePdf = async (
  title: string,
  description: string,
  qrImageBase64: string,
) => {
  const doc = new jsPDF({ unit: 'px', hotfixes: ['px_scaling'] })

  const fontBoldBase64 = await dataUrlToBase64(
    'https://fonts.cdnfonts.com/s/14883/Montserrat-Bold.ttf',
  )
  const fontLightBase64 = await dataUrlToBase64(
    'https://fonts.cdnfonts.com/s/14883/Montserrat-Light.ttf',
  )

  doc.addFileToVFS('Montserrat.ttf', fontBoldBase64)
  doc.addFileToVFS('Montserrat-Light.ttf', fontLightBase64)
  doc.addFont('Montserrat.ttf', 'Montserrat', 'normal', 'bold')
  doc.addFont('Montserrat-Light.ttf', 'Montserrat', 'normal', 'light')
  doc.setLineHeightFactor(LINE_HEIGHT)

  // Background image for pdf
  await doc.addSvgAsImage(
    template,
    0,
    0,
    doc.internal.pageSize.getWidth(),
    doc.internal.pageSize.getHeight(),
  )

  // QR Code
  doc.addImage(qrImageBase64, 155, 783, 155, 155)

  // Title
  const wrappedTitle = doc.splitTextToSize(title, 470) as string[]
  doc.setFontSize(FONT_SIZE_TITLE)
  doc.setFont('Montserrat', 'normal', 'bold')
  doc.text(wrappedTitle, LEFT_MARGIN, TITLE_POSITION)

  // Description
  const wrappedDescription = doc.splitTextToSize(description, 1000) as string[]
  doc.setFontSize(FONT_SIZE_DESCRIPTION)
  doc.setFont('Montserrat', 'normal', 'light')
  doc.text(
    wrappedDescription,
    LEFT_MARGIN,
    TITLE_POSITION + 10 + wrappedTitle.length * FONT_SIZE_TITLE * LINE_HEIGHT,
  )

  return doc
}

export const downloadCompanyPdf = async (
  title: string,
  description: string,
) => {
  const appStore = useAppStore()
  const dynamicLink = await getDynamicLinkForActiveCompany()
  const qrImageBase64 = await createQrCode(dynamicLink)
  const pdf = await generatePdf(title, description, qrImageBase64)

  pdf.save('Bridge-Aushang.pdf')
  appStore.showSnack('Company PDF successfully downloaded!')
}

export const downloadJobAdPdf = async (
  jobAdId: string,
  title: string,
  description: string,
) => {
  const appStore = useAppStore()
  const dynamicLink = await getDynamicLinkForJobAd(jobAdId)
  const qrImageBase64 = await createQrCode(dynamicLink)
  const pdf = await generatePdf(title, description, qrImageBase64)
  appStore.showSnack('JobAdvert PDF successfully downloaded!')

  pdf.save('jobAd.pdf')
}
