import type { SweetAlertIcon } from 'sweetalert2'
import Swal from 'sweetalert2'
import { useTheme } from 'vuetify'

const showPopupWithSingleConfirm = async (titleText: string, text: string, icon: SweetAlertIcon, confirmButtonText: string) => {
  const colors = useTheme().current.value.colors
  const isDarkLayout = useTheme().global.current

  await Swal.fire({
    titleText,
    text,
    icon,
    confirmButtonText,
    confirmButtonColor: colors.success,
    customClass: isDarkLayout.value.dark ? 'dark' : 'light',
  })
}

export {
  showPopupWithSingleConfirm,
}
