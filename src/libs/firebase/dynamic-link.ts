import { useDynamicLinkGraph } from '@/api/graphHooks/useDynamicLinkGraph'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
/**
 * Returns the dynamic link of the currently signed in users company.
 * Checks first, if link is already created and stored in DB, otherwise creates one on the fly.
 */

export const DynamicLink = () => {
  const {
    state: { companyLinkLoading, jobAdLinkLoading },
    actions: { getCompanyDynamicLink, getJobAdDynamicLink },
  } = useDynamicLinkGraph()

  const authStore = useAuthStore()
  const companyStore = useCompanyStore()

  const getDynamicLinkForActiveCompany = async (): Promise<string> => {
    const companyId = authStore.companyId
    const dynamicLink = await getCompanyDynamicLink({
      createDynamicLinkInput: {
        companyId: companyId,
      },
    })

    companyStore.updateDynamicLink(
      dynamicLink?.data?.createCompanyDynamicLink?.url,
    )

    return dynamicLink?.data?.createCompanyDynamicLink?.url
  }

  const getDynamicLinkForJobAd = async (
    jobAdId: string | undefined,
  ): Promise<string> => {
    if (!jobAdId) {
      return ''
    }
    const dynamicLink = await getJobAdDynamicLink({
      createDynamicLinkInput: {
        jobAdvertId: jobAdId,
      },
    })

    return dynamicLink?.data?.createJobAdDynamicLink?.url
  }

  return {
    state: {
      companyLinkLoading,
      jobAdLinkLoading,
    },
    actions: {
      getDynamicLinkForActiveCompany,
      getDynamicLinkForJobAd,
    },
  }
}
