import { getDownloadURL, ref, uploadBytes } from '@firebase/storage'
import { storage } from './config'

// @ts-ignore
/**
 * Uploads a file to the given path in the default storage bucket
 * @param {Blob} file File to upload
 * @param {String} path Path in storage default bucket
 * @returns {Promise<String|Boolean>} Returns the download url for the uploaded file, or false if an error occured
 */
export const uploadFile = async (file, path) => {
  const storageRef = ref(storage, path)
  await uploadBytes(storageRef, file)
  return await getDownloadURL(storageRef)
}
