import {
  APIKEY,
  APPID,
  AUTHDOMAIN,
  ENVIRONMENT,
  MEASUREMENTID,
  MESSAGINGSENDERID,
  PROJECTID,
  STORAGEBUCKET,
} from '@/constants/app'
import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'
import { getFunctions } from 'firebase/functions'

const firebaseConfig = {
  apiKey: APIKEY,
  authDomain: AUTHDOMAIN,
  projectId: PROJECTID,
  storageBucket: STORAGEBUCKET,
  messagingSenderId: MESSAGINGSENDERID,
  appId: APPID,
  measurementId: MEASUREMENTID,
}

const bucketUrl: string =
  ENVIRONMENT === 'production'
    ? 'gs://bridge-public-v2'
    : 'gs://develop-bridge.appspot.com'

const app = initializeApp(firebaseConfig)

const db = getFirestore(app)

const auth = getAuth(app)

const storage = getStorage(app, bucketUrl)

const functions = getFunctions(app, 'europe-west3')

export { app, db, auth, storage, functions }
