import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  serverTimestamp,
  where,
} from 'firebase/firestore'
import { deleteObject, ref } from 'firebase/storage'
import uniqid from 'uniqid'
import { db, storage } from './config'
import { uploadFile } from './upload-file'
import { useAuthStore } from '@/stores/authStore'
import { useStaleImage } from '@/composables/StaleImage/useStaleImage'

/**
 * Uplaods a given image to Firebase storage, saves the downloadUrl in DB
 * and returns the downloadUrl for this image
 * @param image image to upload
 */
export const uploadTextImage = async (image: File) => {
  // return 'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/companies%2FOy7VlRXofNVMc8yZSaYrOXTNGk62%2FtextImages%2F8a4db04c-2def-4224-9a48-4190cee1fdf1-jupiter-2021-4.jpg?alt=media&token=a246adf1-d304-4c0f-b5c0-687fc7d2e104'
  try {
    const authStore = useAuthStore()
    const fileName = uniqid('', image.name)
    const path = `companies/${authStore.companyId}/textImages/${fileName}`
    const downloadUrl = await uploadFile(image, path)
    return downloadUrl
  } catch (error) {
    console.log('adsadada', error)

    throw error
  }
}

/**
 * Deletes the image with given Firebase URL from storage and also from textImages collection of company if present.
 * @param url Firebase URL of image
 */
export const deleteTextImage = async (url: string) => {
  try {
    await deleteObject(ref(storage, url))
    console.log('delete success ', url)
  } catch (error) {
    console.log(error)
  }
}
