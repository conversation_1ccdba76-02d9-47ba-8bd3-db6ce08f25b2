<script setup lang="ts">
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'

  const {
    state: { loadingJobAds },
    actions: { loadJobAdsByCompany },
  } = useJobAdverts()

  onMounted(async () => {
    try {
      await loadJobAdsByCompany()
    } catch (error) {
      console.error('Error loading statistics:', error)
    }
  })

  const props = defineProps({
    title: {
      type: String,
      default: 'Statistics',
    },
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })
</script>

<template>
  <VCard :title="title">
    <template #append>
      <span class="text-sm text-disabled">{{ $t('Real Time Data') }}</span>
    </template>

    <VCardText>
      <VRow>
        <VCol v-for="item in props.data" :key="item.title" cols="6" md="3">
          <div class="d-flex">
            <VAvatar size="70" class="me-3" :image="item.icon" />

            <div class="d-flex flex-column justify-center">
              <span v-if="loadingJobAds" class="text-h6 font-weight-medium">
                <v-progress-circular
                  :width="3"
                  color="primary"
                  indeterminate
                ></v-progress-circular>
              </span>
              <span v-else class="text-h6 font-weight-medium">{{
                item.stats
              }}</span>
              <span class="text-caption">
                {{ item.title }}
              </span>
            </div>
          </div>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>
