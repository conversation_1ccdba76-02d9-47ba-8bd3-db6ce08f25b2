<script setup lang="ts">
import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'vue-router'
import { useTheme } from 'vuetify'
import { calcAge, paginationMeta } from '@/utils/utils'
import { useDebounceFn } from '@vueuse/core'
import avatar from '@images/avatars/avatar-0.png'

const router = useRouter()
const authStore = useAuthStore()
const searchQuery = ref('')
const itemsPerPage = ref(10)
const page = ref(1)

const {
    state: {
        paginatedApplicantsList,
        paginatedApplicants,
        loadingPaginatedApplicants,
    },
    actions: {
        handleApplicantClicked,
        loadPaginatedApplicants,
        updatePaginationParams,
    },
} = useApplicantProfile()

const { dark } = useTheme().global.current

const totalApplicants = computed(() =>
    paginatedApplicants.value?.meta?.totalItems || 0
)

const applicantHeaders = [
    {
        title: 'Profile',
        key: 'profileImageUrl',
        sortable: false,
    },
    {
        title: 'Name',
        key: 'name',
        sortable: true,
    },
    {
        title: 'Age',
        key: 'age',
        sortable: true,
    },
    {
        title: 'City',
        key: 'city',
        sortable: true,
    },
    {
        title: 'Graduation',
        key: 'graduation',
        sortable: true,
    },
]

const handlePaginationChange = async (newPage: number) => {
    page.value = newPage
    await updatePaginationParams(newPage, itemsPerPage.value, searchQuery.value)
}

const handleItemsPerPageChange = async (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage
    await updatePaginationParams(page.value, newItemsPerPage, searchQuery.value)
}

const handleSearch = async () => {
    page.value = 1 // Reset to first page when searching
    await updatePaginationParams(1, itemsPerPage.value, searchQuery.value)
}

// Add debounce for search
const debouncedSearch = useDebounceFn(handleSearch, 300)

onMounted(async () => {
    await loadPaginatedApplicants()
})

watch(searchQuery, () => {
    debouncedSearch()
})
</script>

<template>
    <v-skeleton-loader v-if="loadingPaginatedApplicants" class="mx-auto border py-6" type="table"></v-skeleton-loader>
    <VCard v-else elevation="20" :title="$t('All Applicants')">
        <VCardText>
            <div class="d-flex align-end flex-wrap gap-3">
                <AppTextField v-model="searchQuery" placeholder="Find Applicants" density="compact"
                    prepend-inner-icon="mdi-magnify" class="me-3" @keyup.enter="handleSearch" />
            </div>
        </VCardText>

        <VDataTable :headers="applicantHeaders" :items="paginatedApplicantsList" :loading="loadingPaginatedApplicants"
            class="px-2" hover @click:row="(event, value) => handleApplicantClicked(value.item)">
            <template #[`item.profileImageUrl`]="{ item }">
                <div class="ma-4">
                    <v-avatar :image="item.image || avatar" size="80"></v-avatar>
                </div>
            </template>

            <template #[`item.age`]="{ item }">
                {{ calcAge(item.birthday) }}
            </template>

            <template #bottom>
                <VDivider />
                <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3">
                    <p class="text-sm text-disabled mb-0">
                        {{ paginationMeta({ page, itemsPerPage }, totalApplicants) }}
                    </p>

                    <VPagination v-model="page" :length="paginatedApplicants?.meta?.totalPages || 1"
                        :total-visible="$vuetify.display.xs ? 1 : Math.min(5, paginatedApplicants?.meta?.totalPages || 1)"
                        @update:model-value="handlePaginationChange">
                        <template #prev="slotProps">
                            <VBtn variant="tonal" color="default" v-bind="slotProps" :icon="false">
                                Zurück
                            </VBtn>
                        </template>

                        <template #next="slotProps">
                            <VBtn variant="tonal" color="default" v-bind="slotProps" :icon="false">
                                Weiter
                            </VBtn>
                        </template>
                    </VPagination>
                </div>
            </template>
        </VDataTable>
        <VDivider />
    </VCard>
</template>

<style lang="scss">
.applicant-list {
    .applicant-list-actions {
        inline-size: 8rem;
    }

    .applicant-list-search {
        inline-size: 12rem;
    }
}
</style>