<script setup lang="ts">
  import { uuidv4 } from '@firebase/util'
  import { onUnmounted, reactive, ref } from 'vue'
  import { Cropper } from 'vue-advanced-cropper'
  import { getMimeType } from '@/utils/utils'
  import 'vue-advanced-cropper/dist/style.css'
  import type ImageCropperResult from '@/types/image-cropper-result'

  interface ImageInputCropProps {
    imageUrl?: string | null
    aspectRatio?: number | null
    minAspectRatio?: number | null
    maxAspectRatio?: number | null
    title?: string
    previewHeight?: string
  }

  const props = withDefaults(defineProps<ImageInputCropProps>(), {
    imageUrl: null,
    aspectRatio: null,
    minAspectRatio: null,
    maxAspectRatio: null,
    title: 'Bild',
    previewHeight: '136px',
  })

  const emit = defineEmits<{
    (e: 'imageCropped', result: ImageCropperResult): void
  }>()

  const componentId = uuidv4()

  const fileInput = ref<HTMLInputElement>()
  const cropperRef = ref<InstanceType<typeof Cropper>>()

  const image = reactive({
    src: null as string | null,
    type: null as string | null,
  })

  const result = ref<ImageCropperResult>()

  onUnmounted(() => {
    if (image.src != null) URL.revokeObjectURL(image.src)

    image.src = null
    image.type = null
  })

  const modalVisible = ref(false)

  const clickFileInput = () => {
    if (fileInput.value) fileInput.value.click()
  }

  /**
   * Reads selected file as blob, stores it in the image ref and sets modelVisible to true
   *
   * @param {Event} event Input event
   */
  const loadImage = (event: InputEvent) => {
    const { files } = event.target as HTMLInputElement

    if (files && files[0]) {
      if (image.src) URL.revokeObjectURL(image.src)

      const blob = URL.createObjectURL(files[0])

      const reader = new FileReader()

      reader.onload = e => {
        image.src = blob
        image.type = getMimeType(e.target?.result as ArrayBuffer, files[0].type)

        modalVisible.value = true

        const target = event.target as HTMLInputElement

        target.value = ''
      }

      reader.readAsArrayBuffer(files[0])
    }
  }

  /**
   * Gets the cropped image as blob and emits it. Closes the modal.
   * If image is png, alpha channel are changed to white. Image type will be jpeg.
   */
  const getCroppedImage = () => {
    console.log(1)
    if (!cropperRef.value) return
    console.log(2)

    const { canvas } = cropperRef.value.getResult()
    if (!canvas) return
    console.log(3)
    let noAlphaCanvas = null

    // Make background white and remove transparency if image is a png
    if (image.type === 'image/png') {
      noAlphaCanvas = canvas?.cloneNode(true) as HTMLCanvasElement

      const ctx = noAlphaCanvas?.getContext('2d') as CanvasRenderingContext2D

      ctx.fillStyle = '#FFF'
      ctx.fillRect(0, 0, noAlphaCanvas.width, noAlphaCanvas.height)
      ctx.drawImage(canvas, 0, 0)
    }
    console.log(4)
    if (result.value?.imageUrl) URL.revokeObjectURL(result.value.imageUrl)

    const resultCanvas = noAlphaCanvas ?? canvas

    resultCanvas.toBlob(
      blob => {
        if (blob) {
          result.value = {
            imageBlob: blob,
            imageUrl: URL.createObjectURL(blob),
          }

          emit('imageCropped', result.value)
          modalVisible.value = false
        }
      },
      'image/jpeg',
      0.85,
    )
  }

  /**
   * Closes the modal without clearing anything. Cropped image will not be saved.
   */
  const close = () => {
    modalVisible.value = false
  }

  /**
   * Input rule to ensure that image is already available (via prop) or cropped image is saved
   */
  const requireImage = () => {
    return props.imageUrl != null || result.value?.imageBlob != null
      ? true
      : 'Bitte wählen Sie ein Bild aus'
  }

  const cssTitle = ref(`"${props.title} bearbeiten"`)

  const previewHeight = ref(props.previewHeight)
</script>

<template>
  <div>
    <div
      v-if="result?.imageUrl || props.imageUrl"
      class="cropper-result-image"
      @click="clickFileInput"
    >
      <img
        :key="result?.imageUrl ?? props.imageUrl ?? `${componentId}label`"
        :src="(result?.imageUrl ?? props.imageUrl)!"
      />
    </div>

    <VFileInput
      ref="fileInput"
      class="file-input"
      :class="{ 'file-input-hidden': result?.imageUrl || props.imageUrl }"
      :label="`${title} auswählen`"
      accept="image/jpg, image/jpeg, image/png"
      prepend-icon="mdi-camera"
      @change="loadImage"
    />
    <!--    :rules="[requireImage]" Images Not required -->

    <VOverlay
      v-model="modalVisible"
      persistent
      class="image-cropper-overlay"
      opacity="0.8"
    >
      <div class="d-flex justify-center cropper-modal-button-wrapper flex-wrap">
        <p class="w-100 text-center">
          Bitte wählen Sie den Bildausschnitt und klicken dann auf Foto benutzen
        </p>

        <VBtn color="primary" class="mr-3" @click="close"> Zurück </VBtn>
        <VBtn color="success" @click="getCroppedImage"> Foto benutzen </VBtn>
      </div>
      <div class="scroll-modal-wrapper">
        <VCard v-if="image.src" width="850">
          <Cropper
            ref="cropperRef"
            class="image-cropper"
            :src="image.src"
            :canvas="{
              width: 900,
              fillStyle: '#ffffff',
            }"
            :stencil-props="{
              ...(props.aspectRatio && { aspectRatio: props.aspectRatio }),
              ...(props.minAspectRatio && {
                minAspectRatio: props.minAspectRatio,
              }),
              ...(props.maxAspectRatio && {
                maxAspectRatio: props.maxAspectRatio,
              }),
            }"
            :min-width="50"
            :min-height="50"
          />
        </VCard>
      </div>
    </VOverlay>
  </div>
</template>

<style lang="scss">
  div.file-input {
    min-inline-size: 300px;
  }

  div.file-input-hidden {
    position: absolute;
    inset-block-start: 0;
    inset-inline-start: 0;
    opacity: 0;
  }

  .image-cropper-overlay .v-overlay__content {
    overflow: auto !important;
    block-size: 100%;
    inline-size: 100%;
    padding-block: 130px 30px;
  }

  .scroll-modal-wrapper {
    inline-size: fit-content;
    margin-block: 0;
    margin-inline: auto;
  }

  .cropper-modal-button-wrapper {
    position: fixed;
    z-index: 9;
    inline-size: 100%;
    inset-block-start: 30px;
  }

  .image-cropper-button {
    block-size: fit-content !important;
    min-block-size: 35px;
  }

  .cropper-result-image img {
    block-size: 100%;
    inline-size: auto;
  }

  .cropper-result-image {
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    block-size: v-bind('previewHeight');
    inline-size: fit-content;
  }

  .cropper-result-image::after {
    position: absolute;
    display: flex;
    align-items: end;
    justify-content: center;
    background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 60%) 0%,
      rgba(0, 0, 0, 30%) 20%,
      transparent 40%
    );
    block-size: 100%;
    color: white;
    content: v-bind('cssTitle');
    cursor: pointer;
    inline-size: 100%;
    inset-block-end: 0;
    inset-inline-start: 0;
    line-height: 1.2em;
    padding-block-end: 7px;
    text-align: center;
  }

  .cropper-result-image:hover::after {
    // color: rgb(var(--v-theme-primary));
    background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 60%) 0%,
      rgba(0, 0, 0, 30%) 60%,
      transparent 100%
    );
  }

  .image-cropper {
    min-block-size: 300px;
  }

  .vue-advanced-cropper__background,
  .vue-advanced-cropper__foreground {
    background: white;
  }

  .vue-simple-handler {
    background: #26293d;
  }

  .vue-simple-line {
    border-color: #26293d;
  }
</style>
