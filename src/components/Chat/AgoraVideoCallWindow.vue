<template>
  <div>
    <div class="btn-wrapper mt-10">
      <v-card
        flat
        v-if="!joined"
        class="d-flex h-100 align-center justify-center flex-column"
      >
        <VAvatar size="109" class="elevation-3 my-4 bg-surface">
          <VIcon
            size="50"
            class="rounded-0 text-high-emphasis"
            icon="tabler-video"
          />
        </VAvatar>
        <p class="mx-6 text-secondary">
          Wenn <PERSON>e dazu aufgefordert werden, geben Sie bitte die Benutzung der
          Kamera und des Mikrofons frei. Ansonsten kann der Videocall nicht
          gestartet werden. Die besten Ergebnisse werden mit Google Chrome
          erreicht.
        </p>
        <v-btn
          :loading="joining"
          :disabled="joining"
          class="mb-6 px-6 py-1 font-weight-medium text-lg elevation-3 rounded-xl text-high-emphasis bg-primary"
          :class="[{ 'cursor-pointer': $vuetify.display.smAndDown }]"
          @click="join"
        >
          Videocall starten
        </v-btn>
      </v-card>
      <!--      <AdvancedSetting-->
      <!--        :style="{ marginLeft: '10px' }"-->
      <!--        @profileChange="profileChange"-->
      <!--        @codecChange="codecChange"-->
      <!--        :audioTrack="audioTrack"-->
      <!--        :videoTrack="videoTrack"-->
      <!--      >-->
      <!--      </AdvancedSetting>-->
    </div>
  </div>

  <!--  MY CARD VIEWS-->
  <v-card class="mx-auto" flat>
    <v-container fluid>
      <v-row dense>
        <v-col v-for="card in cards" :key="card.title" :cols="card.flex">
          <v-card flat v-if="Object.keys(remoteUsers).length && card.id === 2">
            <v-card-item
              class="align-end pa-0"
              gradient="to bottom, rgba(0,0,0,.1), rgba(0,0,0,.5)"
            >
              <AgoraVideoPlayer
                v-for="item in remoteUsers"
                :key="item.uid"
                :videoTrack="item.videoTrack"
                :audioTrack="item.audioTrack"
              >
              </AgoraVideoPlayer>
            </v-card-item>

            <v-card-title class="text-white" v-text="card.title"></v-card-title>
            <v-card-actions>
              <v-spacer></v-spacer>

              <v-btn
                color="primary"
                icon="mdi-heart"
                size="small"
                :disabled="joined"
                @click="join"
                >Join</v-btn
              >
            </v-card-actions>
          </v-card>
          <v-card flat v-if="joined && card.id === 1">
            <v-card-item
              class="align-end local-user pa-0"
              gradient="to bottom, rgba(0,0,0,.1), rgba(0,0,0,.5)"
            >
              <AgoraVideoPlayer
                :audioTrack="audioTrack"
                :videoTrack="videoTrack"
                :isLocal="true"
              ></AgoraVideoPlayer>
            </v-card-item>

            <v-card-title class="text-white" v-text="card.title"></v-card-title>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                :color="joined ? `primary` : `secondary`"
                :disabled="joined"
                @click="join"
                >Join</v-btn
              >
              <v-btn
                :color="joined ? `primary` : `secondary`"
                :disabled="!joined"
                @click="leave"
                >Leave</v-btn
              >
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </v-card>
</template>

<script setup>
  import { useJobActions } from '@/composables/JobActions/useJobActions'
  import { useAgora } from '@/composables/useAgora'
  import { AGORAID } from '@/constants/app'
  import { useUsersStore } from '@/stores/usersStore'
  import AgoraRTC from 'agora-rtc-sdk-ng'
  import { onUnmounted, ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRoute } from 'vue-router'
  import { showJoinedMessage } from './Agora/utils/utils.js'

  const userStore = useUsersStore()

  const companyUser = userStore.activeUser.name

  let client = null
  let codec = 'vp8'
  const route = useRoute()
  const { query } = route

  const {
    state: { jobAdLike },
  } = useJobActions()

  const jobActionId = jobAdLike.value?.id

  const applicantId = route.params.applicantId

  const props = defineProps({
    localUser: String,
    joinTrigger: Function,
    leaveTrigger: Function,
  })

  const {
    state: { token: agoraToken, uid: agoraUid },
    actions: { refetchAgoraToken },
  } = useAgora()

  const joining = ref(false)

  const cards = [
    {
      id: 1,
      title: companyUser,
      flex: 12,
    },
    {
      id: 2,
      title: 'Remote',
      flex: 12,
    },
  ]

  const joined = ref(false)
  const remoteUsers = ref({})
  const audioTrack = ref(null)
  const videoTrack = ref(null)
  const formRef = ref()

  const appID = ref(AGORAID)
  const token = ref(agoraToken)
  const channel = ref(jobActionId)
  const uid = ref(agoraUid)

  // onMounted(async () => {
  //   await refetchAgoraToken()
  //   // await initTracks()
  //   // if (query.appId && query.channel) {
  //   //   formRef.value.setValue(query)
  //   //   await join()
  //   // }
  // })

  onUnmounted(() => {
    if (joined.value) {
      leave()
    }
  })

  const profileChange = async val => {
    await videoTrack.value?.setEncoderConfiguration(val)
  }

  const codecChange = val => {
    codec = val
  }

  const initTracks = async () => {
    if (audioTrack.value && videoTrack.value) {
      return
    }
    const tracks = await Promise.all([
      AgoraRTC.createMicrophoneAudioTrack(),
      AgoraRTC.createCameraVideoTrack(),
    ])
    audioTrack.value = tracks[0]
    videoTrack.value = tracks[1]
  }

  const handleUserPublished = async (user, mediaType) => {
    await client.subscribe(user, mediaType)
    delete remoteUsers.value[user.uid]
    remoteUsers.value[user.uid] = user
  }

  const handleUserUnpublished = (user, mediaType) => {
    if (mediaType === 'video') {
      delete remoteUsers.value[user.uid]
    }
  }

  const join = async () => {
    joining.value = true
    try {
      if (!client) {
        client = AgoraRTC.createClient({
          mode: 'live',
          codec: codec,
          areaCode: [AgoraRTC.AREAS.EUROPE],
          role: 'host',
        })
      }

      // Add event listeners to the client.
      client.on('user-published', handleUserPublished)
      client.on('user-unpublished', handleUserUnpublished)
      const options = {
        appId: appID.value,
        channel: channel.value,
        token: token.value,
        uid: uid.value, // optional
      }
      // Join a channel
      options.uid = await client.join(
        options.appId,
        options.channel,
        options.token || null,
        options.uid || null,
      )

      await initTracks()
      const tracks = [audioTrack.value, videoTrack.value]
      await client.publish(tracks)
      showJoinedMessage(options)
      joined.value = true
      joining.value = false
    } catch (error) {
      console.error('agoraError::', error)
      ElMessage.error('agoraErrorMessage::', error.message)
    }
  }

  const leave = async () => {
    if (audioTrack.value) {
      audioTrack.value.close()
      audioTrack.value = null
    }
    if (videoTrack.value) {
      videoTrack.value.close()
      videoTrack.value = null
    }
    remoteUsers.value = {}
    await client.leave()
    joined.value = false
    ElMessage.success('leave channel success!')
  }
</script>

<!--<style lang="scss">-->
<!--  .local-user {-->
<!--    width: 120px;-->
<!--    height: 120px;-->
<!--  }-->
<!--</style>-->
