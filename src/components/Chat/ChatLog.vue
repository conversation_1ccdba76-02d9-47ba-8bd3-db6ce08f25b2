<script lang="ts" setup>
  import { useAuthStore } from '@/stores/authStore'
  import { formatDateToMonthShort, timeAgo } from '@core/utils/formatters'
  import type { ChatOut } from './helpers/types'
  import { useChatStore } from '@/stores/apiStores/useChatStore'

  const store = useChatStore()
  const authStore = useAuthStore()

  interface MessageGroup {
    senderId: ChatOut['messages'][number]['senderId']
    isCompany: ChatOut['messages'][number]['isCompany']
    messages: Omit<ChatOut['messages'][number], 'senderId'>[]
  }

  const contact = computed(() => ({
    id: store.activeChat?.contact.id,
    avatar: store.activeChat?.contact.avatar,
  }))

  const companyUserId = authStore.claims.companyUserId as string

  const resolveFeedbackIcon = (isDelivered: boolean) => {
    if (isDelivered) return { icon: 'tabler-checks', color: 'success' }
    else return { icon: 'tabler-checks', color: undefined }
  }

  const msgGroups = computed(() => {
    let messages: ChatOut['messages'] = []
    const _msgGroups: MessageGroup[] = []

    if (store.activeChat?.chat) {
      // Get messages and sort them by time (oldest first, newest last)
      messages = [...store.activeChat?.chat.messages] as ChatOut['messages']
      messages.sort((a, b) => {
        const timeA = new Date(a.time).getTime()
        const timeB = new Date(b.time).getTime()
        return timeA - timeB // Ascending order (oldest first)
      })

      // Initialize with empty groups
      let currentGroup: MessageGroup | null = null

      messages?.forEach(msg => {
        // Create message object to add to group
        const messageObj = {
          message: msg.message,
          time: msg.time,
          isSent: msg.isSent,
          authorName: msg.authorName,
          isApplicant: msg.isApplicant,
          isCompany: msg.isCompany,
          isDelivered: msg.isDelivered,
          isSeen: msg.isSeen,
        }

        // Check if we need to create a new group
        const needNewGroup =
          !currentGroup ||
          currentGroup.isCompany !== msg.isCompany ||
          currentGroup.senderId !== msg.senderId

        if (needNewGroup) {
          // Add the current group to the result if it exists
          if (currentGroup) {
            _msgGroups.push(currentGroup)
          }

          // Create a new group
          currentGroup = {
            senderId: msg.senderId,
            isCompany: msg.isCompany,
            messages: [messageObj],
          }
        } else {
          // Add to existing group
          currentGroup?.messages.push(messageObj)
        }
      })

      // Add the last group if it exists
      if (currentGroup) {
        _msgGroups.push(currentGroup)
      }
    }

    return _msgGroups
  })
</script>

<template>
  <div class="chat-log pa-5">
    <div
      v-for="(msgGrp, index) in msgGroups"
      :key="msgGrp?.senderId + String(index)"
      class="chat-group d-flex align-start"
      :class="[
        {
          'flex-row-reverse': msgGrp.isCompany,
          'mb-4': msgGroups.length - 1 !== index,
        },
      ]"
    >
      <div class="chat-avatar mr-2" :class="msgGrp.isCompany ? 'ms-4' : 'me-4'">
        <VAvatar size="32" v-if="msgGrp.messages.length">
          <VImg
            :src="msgGrp.isCompany ? store.profileUser?.avatar : contact.avatar"
          />
        </VAvatar>
      </div>
      <div
        class="chat-body d-inline-flex flex-column w-50"
        :class="msgGrp.isCompany ? 'align-end' : 'align-start'"
      >
        <p
          v-for="(msgData, msgIndex) in msgGrp.messages"
          :key="msgData.time"
          class="chat-content py-2 px-4 elevation-1"
          style="background-color: rgb(var(--v-theme-surface))"
          :class="[
            msgGrp.isCompany
              ? 'text-white chat-right'
              : 'bg-primary chat-left text-primary-dark',
            msgGrp.messages.length - 1 !== msgIndex ? 'mb-3' : 'mb-1',
          ]"
        >
          <i class="text-sm text-disabled">{{ msgData?.authorName || '' }}</i>
          <br />
          {{ msgData.message }} <br />
          <!--          <span v-if="msgData.time" class="text-xs ms-1 text-disabled">{{-->
          <!--            formatDateToMonthShort(msgData.time) + ' | ' + timeAgo(msgData.time)-->
          <!--          }}</span>-->
          <span :class="{ 'text-right': msgGrp.isCompany }">
            <VIcon
              v-if="msgGrp.isCompany"
              size="18"
              :color="resolveFeedbackIcon(msgData.isDelivered).color"
            >
              {{ resolveFeedbackIcon(msgData?.isDelivered).icon }}
            </VIcon>
            <span v-if="msgData.time" class="text-xs ms-1 text-disabled">{{
              formatDateToMonthShort(msgData.time) +
              ' | ' +
              timeAgo(msgData.time)
            }}</span>
          </span>
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
  .chat-log {
    height: 440px;
    .chat-content {
      border-end-end-radius: 8px;
      border-end-start-radius: 8px;

      &.chat-left {
        border-start-end-radius: 8px;
      }

      &.chat-right {
        border-start-start-radius: 8px;
      }
    }
  }
</style>
