<script setup lang="ts">
  import { useAppStore } from '@/stores/appStore'
  import { onBeforeUnmount, onMounted } from 'vue'
  import { useAuthStore } from '@/stores/authStore'

  const authStore = useAuthStore()
  let reloadInterval: string | number | NodeJS.Timeout | undefined
  onMounted(() => {
    reloadInterval = setInterval(() => {
      authStore.refreshUser().then(() => {})
    }, 3000)
  })

  const appStore = useAppStore()

  const loading = ref(false)
  const emailSent = ref(false)

  onBeforeUnmount(() => clearInterval(reloadInterval))

  const sendVerificationAgain = async () => {
    loading.value = true
    await authStore.sendEmailVerification()
    appStore.showSnack('Email wurde erneut gesendet')
    setTimeout(() => {
      emailSent.value = true
      loading.value = false
    }, 1000)
  }
</script>

<template>
  <VCard title="Email nicht verifiziert">
    <VCardText>
      <p>
        Um eine Stellenanzeige zu erstellen, muss Ihre Email Adresse verifiziert
        sein.<br />
        Klicken Sie dafür bitte auf den Link in der Mail, die Sie erhalten
        haben.
      </p>
      <VBtn
        color="success"
        v-if="emailSent"
        :loading="loading"
        @click="sendVerificationAgain"
      >
        Email erneut senden
      </VBtn>
      <VBtn v-else :loading="loading" @click="sendVerificationAgain">
        Email erneut senden
      </VBtn>
    </VCardText>
  </VCard>
</template>
