<script setup lang="ts">
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import avatar from '@images/avatars/avatar-0.png'

  const {
    state: {
      loadingSetupCheckout,
      stripeCustomer,
      loadingCustomer,
      loadingCreateStripeCustomer,
    },
    actions: { loadCustomer },
  } = useStripeSubscription()

  const companyStore = useCompanyStore()

  const useSubStore = useSubscriptionStore()

  const customerFormStore = useCustomerFormStore()

  const openDialog = ref(false)

  const selectedAddress = computed(() => {
    const pMethod = useSubStore.getSelectedPaymentMethod
    return pMethod?.billing_details?.address
  })

  const showCustomerDialog = () => {
    customerFormStore.updateName(stripeCustomer.value?.name)
    customerFormStore.updateEmail(stripeCustomer.value?.email)
    openDialog.value = true
  }

  onMounted(async () => {
    await companyStore.init()
    await loadCustomer()
  })
</script>

<template>
  <StripeCustomerDialog
    :open="openDialog"
    @onClose="() => (openDialog = false)"
  />
  <VCard
    :loading="loadingSetupCheckout"
    v-if="stripeCustomer?.id && selectedAddress?.country"
  >
    <VCardText>
      <div class="d-flex align-center justify-space-between">
        <div class="text-body-1 text-high-emphasis font-weight-bold mb-4">
          Rechnungsadresse
        </div>
      </div>
      <div>
        {{ selectedAddress?.line1 }} <br />
        {{ selectedAddress?.line2 }} <br />
        {{ selectedAddress?.city }} ,{{ selectedAddress?.country }}<br />
      </div>
    </VCardText>
  </VCard>
  <VCard class="mt-6" :loading="loadingCustomer">
    <VCardText class="d-flex flex-column gap-y-6">
      <div class="d-flex align-center justify-space-between">
        <div class="text-body-1 text-high-emphasis font-weight-bold">
          Rechnungsnutzer
        </div>
        <VBtn
          v-if="stripeCustomer?.id"
          :loading="loadingCustomer"
          variant="text"
          density="compact"
          @click="showCustomerDialog"
        >
          Bearbeiten
        </VBtn>
        <VBtn
          v-else
          :loading="loadingCreateStripeCustomer"
          variant="text"
          density="compact"
          @click="showCustomerDialog"
        >
          Create new
        </VBtn>
      </div>

      <div class="d-flex align-center" v-if="stripeCustomer?.id">
        <VAvatar :image="avatar" class="me-3" />
        <div>
          <div class="text-body-1 font-weight-medium">
            {{ stripeCustomer?.name }}
          </div>
          <span class="text-sm text-disabled">
            {{ stripeCustomer?.email }}</span
          >
        </div>
      </div>
    </VCardText>
  </VCard>
</template>
0248416930
