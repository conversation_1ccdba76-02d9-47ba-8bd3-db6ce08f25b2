<script setup lang="ts">
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
  import { PaymentCardContent } from '@/types/checkout'
  import paypalDark from '@images/payments/img/paypal-dark.png'
  import paypalLight from '@images/payments/img/paypal-light.png'
  import visaDark from '@images/payments/img/visa-dark.png'
  import visaLight from '@images/payments/img/visa-light.png'
  import { useCompanyStore } from '@/stores/companyStore'

  const {
    state: { paymentMethods, loadingPaymentMethods, stripeCustomer },
    actions: { loadPaymentMethods },
  } = useStripeSubscription()

  const useSubStore = useSubscriptionStore()
  const companyStore = useCompanyStore()

  const {
    state: { loadingSetupCheckout },
    actions: { getSetupCheckoutUrl },
  } = useStripeSubscription()

  const stripeCustomerId = computed(() => {
    return (
      companyStore?.getCompany?.stripeCustomerId || stripeCustomer.value?.id
    )
  })

  const visa = useGenerateImageVariant(visaLight, visaDark)
  const paypal = useGenerateImageVariant(paypalLight, paypalDark)

  const selectedCard = ref('')

  const paymentCards: Ref<PaymentCardContent[]> = computed(() => {
    return paymentMethods.value?.data?.map(dataObj => {
      return {
        name: dataObj.billing_details.name,
        id: dataObj.id,
        brand: dataObj.card.brand,
        last4: dataObj.card.last4,
        value: dataObj.id,
        country: dataObj.card.country,
        exp_month: dataObj.card.exp_month,
        exp_year: dataObj.card.exp_year,
        isDefault: true,
      }
    })
  })
  const setSelectedPm = (pmId: string) => {
    useSubStore.updateSelectedMethod(pmId)
  }

  const updateBillingDetails = async () => {
    window.location.href = (await getSetupCheckoutUrl('payment')) || ''
  }

  onMounted(async () => {
    if (stripeCustomerId.value) {
      await loadPaymentMethods()
    }
  })
</script>

<template>
  <VCardText>
    <div class="mb-1">
      <VRow>
        <VCol cols="8">
          <h4 class="text-h6 mb-1">Zahlungsmethoden</h4>
        </VCol>
        <VCol cols="4">
          <VBtn
            :loading="loadingSetupCheckout"
            variant="text"
            density="compact"
            prepend-icon="mdi-plus"
            @click="updateBillingDetails"
          >
            Hinzufügen
          </VBtn>
        </VCol>
      </VRow>
    </div>
    <!-- Credit card info -->
    <v-skeleton-loader
      v-if="loadingPaymentMethods"
      class="mx-auto border py-6"
      type="list-item-avatar-three-line"
    ></v-skeleton-loader>
    <div v-else class="mb-8">
      <VRow>
        <VCol cols="12" v-if="stripeCustomerId">
          <div>
            <CustomRadios
              @update:selectedRadio="
                val => {
                  setSelectedPm(val)
                }
              "
              v-model:selected-radio="selectedCard"
              :radio-content="paymentCards"
            >
              <template #default="{ item }">
                <div
                  class="w-100 pa-2"
                  style="border: 1px solid dimgray; border-radius: 5px"
                >
                  <div class="d-flex justify-space-between mb-3">
                    <h6 class="text-base font-weight-medium">
                      {{ item.name }}
                    </h6>
                    <img :src="visa" height="35" />
                  </div>
                  <div class="d-flex justify-space-between mb-3">
                    <h6 class="text-base font-weight-medium">
                      ***********{{ item.last4 }}
                    </h6>

                    <VChip label class="text-capitalize">
                      {{ item.country }}
                    </VChip>
                  </div>
                  <p class="text-sm">
                    <v-icon>tabler-clock</v-icon>
                    Expiring: {{ item.exp_month }} /
                    {{ item.exp_year }}
                  </p>
                </div>
              </template>
            </CustomRadios>
          </div>
        </VCol>

        <VCol cols="12">
          <BillingCustomerDetails />
        </VCol>
      </VRow>
    </div>
  </VCardText>
</template>

<style lang="scss" scoped>
  .footer {
    position: static !important;
    inline-size: 100%;
    inset-block-end: 0;
  }

  .payment-card {
    margin-block: 6.25rem;
  }

  .payment-page {
    @media (min-width: 600px) and (max-width: 960px) {
      .v-container {
        padding-inline: 2rem !important;
      }
    }
  }

  .order-price {
    font-size: 3rem;
  }
</style>

<style lang="scss">
  .payment-card {
    .custom-radio {
      .v-radio {
        margin-block-start: 0 !important;
      }
    }
  }
</style>
