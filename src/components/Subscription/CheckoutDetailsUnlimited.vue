<script setup lang="ts">
  import BillingCustomerForm from '@/components/Subscription/BillingCustomerForm.vue'
  import { useUnlimitedSubscription } from '@/composables/StripeSubscription/useUnlimitedSubscription'
  import { useAppStore } from '@/stores/appStore'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
  import { PaymentCardContent } from '@/types/checkout'
  import visaDark from '@images/payments/img/visa-dark.png'
  import visaLight from '@images/payments/img/visa-light.png'
  import sepaDebit from '@images/payments/sepa-direct.webp'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { useAuthStore } from '@/stores/authStore'

  const useSubStore = useSubscriptionStore()
  const customerFormStore = useCustomerFormStore()
  const companyStore = useCompanyStore()
  const appStore = useAppStore()
  const authStore = useAuthStore()
  const route = useRoute()

  const companyGraph = useCompanyGraph()

  // State for the active pricing plan
  const activePricingPlan = ref<any>(null)
  const loadingPricingPlan = ref(true)

  const companySubscriptionPlans = computed(
    () =>
      companyGraph?.state?.companySubscriptionPlans || {
        availablePlans: [],
        activeSubscriptions: [],
      },
  )

  const appLoader = computed(() => appStore.showLoader)

  const {
    state: {
      stripeCustomer,
      paymentMethods,
      loadingCreateUnlimited,
      loadingGenerateInvoice,
      loadingPaymentMethods,
    },
    actions: {
      handleSubscribeToUnlimited,
      handleGenerateUnlimitedInvoice,
      loadCustomer,
      loadPaymentMethods,
      handleFetchPromoCode,
    },
  } = useUnlimitedSubscription()

  const stripeCustomerId = computed(() => {
    return (
      companyStore?.getCompany?.stripeCustomerId ||
      companyStore?.company?.stripeCustomerId ||
      stripeCustomer.value?.id
    )
  })

  onMounted(async () => {
    // Load the company's subscription configuration to get the active pricing plan
    try {
      loadingPricingPlan.value = true
      await useSubStore.fetchSubscriptionConfig()

      if (useSubStore.availablePricingPlans?.length > 0) {
        // Check if a specific plan ID is passed in the query
        const planId = route.query.planId as string

        if (planId) {
          // Find the specific plan
          activePricingPlan.value =
            useSubStore.availablePricingPlans.find(
              (plan: any) => plan.id === planId,
            ) || useSubStore.availablePricingPlans[0]
        } else {
          // Use the first available plan as default
          activePricingPlan.value = useSubStore.availablePricingPlans[0]
        }
      }
    } catch (error) {
      console.error('Error loading pricing plan:', error)
      appStore.showSnack('Failed to load pricing plan details', 'error')
    } finally {
      loadingPricingPlan.value = false
    }

    await loadCustomer()
    if (stripeCustomerId.value) {
      await loadPaymentMethods()
    }
  })

  const subStore = useSubscriptionStore()
  const visa = useGenerateImageVariant(visaLight, visaDark)

  const discountPercentage = computed(
    () => subStore.getCouponDetails?.percent_off || 0,
  )

  type PaymentFlow = 'card' | 'invoice' | 'unknown'

  const monthlyPrice = computed(() => {
    if (activePricingPlan.value?.price) {
      return activePricingPlan.value.price
    }
    return 79
  })

  const subscriptionPeriodMonths = computed(() => {
    if (activePricingPlan.value?.durationDays) {
      return Math.round(activePricingPlan.value.durationDays / 30)
    }
    return 6
  })

  const paymentFlow: Ref<PaymentFlow> = ref('unknown')

  const selectedCard = ref('')

  const taxPercentage = 0.19

  const totalSubscriptionPrice = computed(() => {
    if (!activePricingPlan.value) return 0

    // Price is per month, multiply by the number of months in the subscription period
    const monthlyPrice = activePricingPlan.value.price || 0
    const months = subscriptionPeriodMonths.value
    return monthlyPrice * months
  })

  // Calculate prices - for unlimited subscription, not per job ad
  const subTotal = computed(() => {
    return totalSubscriptionPrice.value.toFixed(2)
  })

  const tax = computed(() => {
    return (totalSubscriptionPrice.value * taxPercentage).toFixed(2)
  })

  const totalDue = computed(() => {
    return (totalSubscriptionPrice.value * (1 + taxPercentage)).toFixed(2)
  })

  const paymentCards: Ref<PaymentCardContent[]> = computed(() => {
    return paymentMethods.value?.data?.map(
      (dataObj: {
        card: {
          brand: any
          last4: any
          country: any
          exp_month: any
          exp_year: any
        }
        billing_details: { name: any }
        id: any
        sepa_debit: {
          bank_code: any
          last4: any
          country: string
          branch_code: any
          fingerprint: any
        }
      }) => {
        if (dataObj.card) {
          return {
            name: dataObj.billing_details.name,
            id: dataObj.id,
            brand: dataObj.card.brand,
            last4: dataObj.card.last4,
            value: dataObj.id,
            country: dataObj.card.country,
            exp_month: dataObj.card.exp_month,
            exp_year: dataObj.card.exp_year,
            isDefault: true,
            type: 'card',
          }
        } else if (dataObj.sepa_debit) {
          return {
            name: dataObj.billing_details.name,
            id: dataObj.id,
            value: dataObj.id,
            bank_code: dataObj.sepa_debit.bank_code,
            country: dataObj.sepa_debit.country,
            last4: dataObj.sepa_debit.last4,
            branch_code: dataObj.sepa_debit.branch_code,
            fingerprint: dataObj.sepa_debit.fingerprint,
            isDefault: true,
            type: 'sepa_debit',
          }
        }
      },
    )
  })
  // Computed properties for discounted prices
  const discountedSubTotal = computed(() => {
    const discount = discountPercentage.value / 100
    return (totalSubscriptionPrice.value * (1 - discount)).toFixed(2)
  })

  const discountedTax = computed(() => {
    const discountedPrice =
      totalSubscriptionPrice.value * (1 - discountPercentage.value / 100)
    return (discountedPrice * taxPercentage).toFixed(2)
  })

  const discountedTotalDue = computed(() => {
    const discountedPrice =
      totalSubscriptionPrice.value * (1 - discountPercentage.value / 100)
    return (discountedPrice * (1 + taxPercentage)).toFixed(2)
  })

  // Get subscription period display text
  const subscriptionPeriodText = computed(() => {
    const months = subscriptionPeriodMonths.value
    if (months === 1) return '1 Monat'
    if (months === 3) return '3 Monate'
    if (months === 6) return '6 Monate'
    if (months === 12) return '1 Jahr'
    return `${months} Monate`
  })

  const selectedPm = computed(() => {
    return paymentMethods.value?.data?.find(
      (card: { id: string }) => card.id === selectedCard.value,
    )
  })

  const setSelectedPm = (pmId: string) => {
    useSubStore.updateSelectedMethod(pmId)
  }

  const updateBillingDetails = async (customerId: string) => {
    appStore.showSnack('Please update billing details in the customer portal')
  }

  const isPricingPlanDialogVisible = ref(false)

  const openDialog = ref(false)

  const handleNoCustomerFlow = () => {
    if (paymentFlow.value === 'invoice') {
      handleGenerateUnlimitedInvoice()
    } else {
      openDialog.value = true
    }
  }

  const setJobAdCheckout = async (
    jobAdId: string | null,
    jobAdvertTitle: string | null,
  ) => {
    const jobAdWithTitle = `${jobAdId}|${jobAdvertTitle}`
    const checkoutObj = {
      jobAdId: jobAdId,
      jobAdTitle: jobAdvertTitle,
      jobAdText: jobAdWithTitle,
    }
    const objString = JSON.stringify(checkoutObj)
    localStorage.setItem('checkout', objString)
    await subStore.updateJobAdverts([jobAdWithTitle])
  }

  const handlePayWithCard = () => {
    paymentFlow.value = 'card'
  }

  const handlePayWithInvoice = () => {
    paymentFlow.value = 'invoice'
  }

  const handleResetPaymentFlow = () => {
    paymentFlow.value = 'unknown'
  }

  const enablePayButton = computed(() => {
    return paymentFlow.value === 'card'
      ? selectedCard.value !== ''
      : customerFormStore.isCustomerFormValid
  })

  const onSubscribeClick = async () => {
    if (paymentFlow.value === 'card' && !!stripeCustomer.value?.id) {
      await handleSubscribeToUnlimited()
    } else if (paymentFlow.value === 'invoice') {
      await handleGenerateUnlimitedInvoice()
    } else {
      handleNoCustomerFlow()
    }
  }

  const applyCoupon = async () => {
    if (useSubStore.promoCode) {
      // await handleFetchCoupon()
      await handleFetchPromoCode()
    }
  }

  const removePromo = async () => {
    useSubStore.removePromoCode()
  }

  onMounted(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const sessionId = urlParams.get('session_id')
    const success = urlParams.get('success')
    const cId = urlParams.get('cId')
    const jobAdId = urlParams.get('ad')
    const jobAdTitle = urlParams.get('tit')
    if (!subStore.getJobAdverts && jobAdId) {
      setJobAdCheckout(jobAdId, jobAdTitle)
    }
  })
</script>

<template>
  <StripeCustomerDialog
    :open="openDialog"
    @onClose="() => (openDialog = false)"
    @onComplete="customer => updateBillingDetails(String(customer))"
  />
  <!-- eslint-disable vue/attribute-hyphenation -->
  <div class="payment-page">
    <VContainer>
      <div class="d-flex justify-center align-center payment-card">
        <VCard
          width="100%"
          :loading="loadingPaymentMethods"
          :disabled="appLoader"
        >
          <VRow>
            <VCol
              v-if="paymentFlow === 'unknown'"
              cols="12"
              md="7"
              :class="$vuetify.display.mdAndUp ? 'border-e' : 'border-b'"
            >
              <v-card class="ma-8 pa-4 align-content-center" height="90%" flat>
                <v-card
                  class="pa-2 payCard border"
                  elevation="4"
                  @click="handlePayWithInvoice"
                >
                  <v-card-title class="d-flex align-center">
                    <v-icon
                      icon="mdi-invoice-text-check-outline"
                      class="mr-2"
                      color="primary"
                    ></v-icon>
                    Per Rechnung bezahlen
                  </v-card-title>
                  <v-card-subtitle
                    >Sie erhalten die Rechnung per Mail.
                  </v-card-subtitle>
                </v-card>
                <v-card
                  class="pa-2 mt-4 payCard border"
                  elevation="2"
                  @click="handlePayWithCard"
                >
                  <v-card-title class="d-flex align-center">
                    <v-icon
                      icon="mdi-credit-card-check-outline"
                      class="mr-2"
                      color="primary"
                    ></v-icon>
                    Per Kreditkarte / SEPA bezahlen
                  </v-card-title>
                  <v-card-subtitle
                    >Sie erhalten die Rechnung per Mail.
                  </v-card-subtitle>
                </v-card>
              </v-card>
            </VCol>

            <!-- INVOICE DETAILS -->
            <VCol
              v-if="paymentFlow === 'invoice'"
              cols="12"
              md="7"
              :class="$vuetify.display.mdAndUp ? 'border-e' : 'border-b'"
            >
              <VCardText>
                <div class="mb-1">
                  <VRow>
                    <VCol cols="10">
                      <h4 class="text-h5 mb-6">Rechnungsdetails</h4>
                    </VCol>
                    <VCol cols="2">
                      <VBtn
                        :loading="loadingSetupCheckout"
                        variant="text"
                        prepend-icon="mdi-refresh"
                        density="compact"
                        @click="handleResetPaymentFlow"
                      >
                        Wechseln
                      </VBtn>
                    </VCol>
                  </VRow>
                </div>
                <div class="mb-1 border-1">
                  <VRow>
                    <VCol cols="12">
                      <BillingCustomerForm />
                    </VCol>
                  </VRow>
                </div>
              </VCardText>
            </VCol>

            <!-- CARD DETAILS -->
            <VCol
              v-if="stripeCustomer?.id && paymentFlow === 'card'"
              cols="12"
              md="7"
              :class="$vuetify.display.mdAndUp ? 'border-e' : 'border-b'"
            >
              <VCardText>
                <div class="mb-1">
                  <VRow>
                    <VCol cols="6">
                      <h4 class="text-h5 mb-6">Zahlungsdetails</h4>
                    </VCol>
                    <VCol cols="3">
                      <VBtn
                        v-if="stripeCustomer?.id"
                        :loading="loadingSetupCheckout"
                        variant="text"
                        density="compact"
                        prepend-icon="mdi-plus"
                        @click="updateBillingDetails"
                      >
                        Hinzufügen
                      </VBtn>
                    </VCol>
                    <VCol cols="3">
                      <VBtn
                        v-if="stripeCustomer?.id"
                        :loading="loadingSetupCheckout"
                        variant="text"
                        prepend-icon="mdi-refresh"
                        density="compact"
                        @click="handleResetPaymentFlow"
                      >
                        Wechseln
                      </VBtn>
                    </VCol>
                  </VRow>
                </div>
                <!-- Credit card info -->
                <div class="mb-1 border-1">
                  <VRow>
                    <VCol cols="12">
                      <div>
                        <CustomRadios
                          @update:selectedRadio="
                            val => {
                              setSelectedPm(val)
                            }
                          "
                          v-model:selected-radio="selectedCard"
                          :radio-content="paymentCards"
                          :grid-column="{ cols: '12' }"
                        >
                          <template #default="{ item }">
                            <div
                              class="w-100 pa-2"
                              style="
                                border: 1px solid dimgray;
                                border-radius: 5px;
                              "
                            >
                              <div class="d-flex justify-space-between mb-3">
                                <h6 class="text-base font-weight-medium">
                                  {{ item.name }}
                                </h6>
                                <img
                                  :src="item.type === 'card' ? visa : sepaDebit"
                                  height="35"
                                />
                              </div>
                              <div class="d-flex justify-space-between mb-3">
                                <h6 class="text-base font-weight-medium">
                                  ***********{{ item.last4 }}
                                </h6>

                                <VChip label class="text-capitalize">
                                  {{ item?.country }}
                                </VChip>
                              </div>
                              <p class="text-sm" v-if="item.type === 'card'">
                                <v-icon>tabler-clock</v-icon>
                                Ablaufdatum: {{ item.exp_month }} /
                                {{ item.exp_year }}
                              </p>
                              <p class="text-sm" v-else>
                                <v-icon>tabler-building-bank</v-icon>
                                SEPA Direct Debit: {{ item.last_4 }} /
                                {{ item?.bank_code }}
                              </p>
                            </div>
                          </template>
                        </CustomRadios>
                      </div>
                    </VCol>

                    <VCol cols="12">
                      <BillingCustomerDetails />
                    </VCol>
                  </VRow>
                </div>
              </VCardText>
            </VCol>

            <VCol
              cols="12"
              :md="!stripeCustomer?.id && paymentFlow === 'card' ? 12 : 5"
            >
              <VCardText>
                <!-- order summary -->
                <div class="mb-8">
                  <v-row>
                    <VCol cols="10">
                      <h4 class="text-h5 mb-2">Bestellübersicht</h4>
                    </VCol>
                    <VCol cols="2">
                      <VBtn
                        v-if="!stripeCustomer?.id && paymentFlow === 'card'"
                        variant="text"
                        prepend-icon="mdi-refresh"
                        density="compact"
                        @click="handleResetPaymentFlow"
                      >
                        Wechseln
                      </VBtn>
                    </VCol>
                  </v-row>

                  <!-- Loading state for pricing plan -->
                  <div v-if="loadingPricingPlan" class="mb-4">
                    <VCard flat>
                      <VCardText class="text-center">
                        <VProgressCircular indeterminate color="primary" />
                        <p class="mt-2">Loading pricing plan details...</p>
                      </VCardText>
                    </VCard>
                  </div>

                  <!-- Show active pricing plan if available -->
                  <div v-else-if="activePricingPlan" class="mb-4">
                    <VCard flat variant="tonal" color="primary">
                      <VCardText>
                        <div class="d-flex justify-space-between align-center">
                          <div>
                            <h6 class="text-h6 font-weight-medium">
                              {{
                                activePricingPlan.displayName ||
                                activePricingPlan.name
                              }}
                            </h6>
                            <p class="text-body-2 mb-0">
                              <VIcon size="small" class="mr-1"
                                >mdi-infinity</VIcon
                              >
                              Unlimited Job Adverts
                            </p>
                            <p class="text-caption mb-0">
                              {{ subscriptionPeriodText }} Minimum Subscription
                            </p>
                          </div>
                          <div class="text-right">
                            <div>
                              <span class="text-h5 font-weight-bold">
                                {{ '€' + monthlyPrice.toFixed(2) }}
                              </span>
                              <span class="text-body-2"> /month</span>
                            </div>
                            <p class="text-caption mb-0 text-medium-emphasis">
                              {{ activePricingPlan.currency || '€'
                              }}{{ totalSubscriptionPrice.toFixed(2) }} total
                            </p>
                          </div>
                        </div>
                      </VCardText>
                    </VCard>
                  </div>

                  <!-- Error state when no pricing plan available -->
                  <div v-else class="mb-4">
                    <VAlert type="warning" variant="tonal">
                      <strong>No pricing plan available</strong>
                      <p class="mb-0">
                        Please contact support to configure your subscription
                        plan.
                      </p>
                    </VAlert>
                  </div>
                </div>

                <!-- Removed per-month price card since we show total price below -->

                <div class="my-5">
                  <div class="d-flex justify-space-between mb-2">
                    <span class="font-weight-medium"
                      >{{ subscriptionPeriodText }} Subscription</span
                    >
                    <h6 class="text-h6">
                      €{{ totalSubscriptionPrice.toFixed(2) }}
                    </h6>
                  </div>
                  <div
                    class="d-flex justify-space-between mb-1 text-caption text-medium-emphasis"
                  >
                    <span
                      >(€{{ monthlyPrice }} x
                      {{ subscriptionPeriodMonths }} months)</span
                    >
                  </div>
                  <div class="d-flex justify-space-between mb-2">
                    <span>Stellenanzeigen</span>
                    <h6 class="text-h6">Unlimitiert</h6>
                  </div>
                  <div class="d-flex justify-space-between">
                    <span>Zwischensumme </span>
                    <h6 class="text-h6">
                      <template v-if="discountPercentage > 0">
                        <span
                          style="
                            text-decoration: line-through;
                            color: rgba(200, 200, 200, 0.7);
                            font-size: 0.8em;
                          "
                        >
                          €{{ subTotal }}
                        </span>
                        <span> €{{ discountedSubTotal }}</span>
                      </template>
                      <template v-else>
                        <span>€{{ subTotal }}</span>
                      </template>
                    </h6>
                  </div>
                  <div class="d-flex justify-space-between">
                    <span>MwSt.(19%)</span>
                    <h6 class="text-h6">
                      <template v-if="discountPercentage > 0">
                        <span
                          style="
                            text-decoration: line-through;
                            color: rgba(200, 200, 200, 0.7);
                            font-size: 0.8em;
                          "
                        >
                          €{{ tax }}
                        </span>
                        <span> €{{ discountedTax }}</span>
                      </template>
                      <template v-else>
                        <span>€{{ tax }}</span>
                      </template>
                    </h6>
                  </div>
                  <VDivider class="my-4" />
                  <div class="d-flex justify-space-between">
                    <span>Summe</span>
                    <h6 class="text-h6">
                      <template v-if="discountPercentage > 0">
                        <span
                          style="
                            text-decoration: line-through;
                            color: rgba(200, 200, 200, 0.7);
                            font-size: 0.8em;
                          "
                        >
                          €{{ totalDue }}
                        </span>
                        <span> €{{ discountedTotalDue }}</span>
                      </template>
                      <template v-else>
                        <span>€{{ totalDue }}</span>
                      </template>
                    </h6>
                  </div>
                  <VDivider class="my-4" />
                  <div class="text-caption" v-if="!discountPercentage">
                    Gutscheincode eingeben
                  </div>

                  <div class="d-flex justify-end mt-3">
                    <v-row>
                      <template v-if="!discountPercentage">
                        <v-col cols="6">
                          <v-text-field
                            density="compact"
                            label="Gutscheincode"
                            @change="useSubStore.removePromoCode"
                            v-model="useSubStore.promoCode"
                            outlined
                            :hint="
                              useSubStore.getCouponDetails?.redeemed
                                ? 'Gutschein bereits eingelöst'
                                : ''
                            "
                            persistent-hint
                            dense
                          ></v-text-field>
                        </v-col>
                        <v-col cols="6" class="d-flex justify-end">
                          <v-btn
                            :loading="loadingFetchCoupon"
                            :disabled="
                              !useSubStore.promoCode || loadingFetchCoupon
                            "
                            @click="applyCoupon"
                          >
                            Anwenden
                          </v-btn>
                        </v-col>
                      </template>
                      <template v-else>
                        <v-col cols="12">
                          <v-alert
                            variant="tonal"
                            color="success"
                            class="d-flex align-center"
                            border="start"
                            border-color="secondary"
                          >
                            <v-icon
                              icon="mdi-check-circle"
                              class="mr-2"
                            ></v-icon>
                            <b> {{ discountPercentage }}</b
                            >% Rabatt wird verwendet.
                            <v-btn
                              class="ml-8"
                              variant="outlined"
                              @click="removePromo"
                              size="small"
                              density="compact"
                              icon="tabler-trash"
                            ></v-btn>
                          </v-alert>
                        </v-col>
                      </template>
                    </v-row>
                  </div>
                </div>

                <VBtn
                  v-if="stripeCustomer?.id"
                  append-icon="tabler-lock"
                  :disabled="
                    !enablePayButton ||
                    loadingCreateUnlimited ||
                    loadingGenerateInvoice ||
                    appLoader ||
                    !activePricingPlan
                  "
                  :loading="
                    loadingCreateUnlimited ||
                    loadingGenerateInvoice ||
                    appLoader
                  "
                  @click="onSubscribeClick"
                  block
                  color="primary"
                  class="mb-8 flip-in-rtl"
                >
                  Unlimited Abo für {{ activePricingPlan?.currency || '€'
                  }}{{ totalDue }} abschließen
                </VBtn>
                <v-btn
                  v-else
                  block
                  color="primary"
                  class="mb-8 flip-in-rtl"
                  :loading="
                    loadingCreateUnlimited ||
                    loadingGenerateInvoice ||
                    appLoader
                  "
                  @click="handleNoCustomerFlow"
                  append-icon="tabler-lock"
                  :disabled="
                    !activePricingPlan ||
                    loadingCreateUnlimited ||
                    loadingGenerateInvoice ||
                    (paymentFlow !== 'card' &&
                      !customerFormStore.isCustomerFormValid)
                  "
                >
                  Unlimited Abo für {{ activePricingPlan?.currency || '€'
                  }}{{ totalDue }} abschließen
                </v-btn>

                <div class="text-caption">
                  Mit der Bestätigung Ihres Unlimited-Abonnements gestatten Sie
                  Berufe4you UG, Ihnen künftige Zahlungen gemäß Ihren
                  Bedingungen in Rechnung zu stellen. Sie erhalten unbegrenzte
                  Stellenanzeigen für mindestens {{ subscriptionPeriodText }}.
                  Sie können Ihr Abonnement jederzeit nach Ablauf der
                  Mindestlaufzeit kündigen.
                </div>
              </VCardText>
            </VCol>
          </VRow>
        </VCard>
      </div>
    </VContainer>
    <!-- 👉 Footer -->
    <Footer />

    <PricingPlanDialog v-model:is-dialog-visible="isPricingPlanDialogVisible" />
  </div>
</template>

<style lang="scss" scoped>
  .footer {
    position: static !important;
    inline-size: 100%;
    inset-block-end: 0;
  }

  .payment-card {
    margin-block: 6.25rem;
  }

  .payment-page {
    @media (min-width: 600px) and (max-width: 960px) {
      .v-container {
        padding-inline: 2rem !important;
      }
    }
  }

  .order-price {
    font-size: 3rem;
  }

  .payment-card {
    .custom-radio {
      .v-radio {
        margin-block-start: 0 !important;
      }
    }
  }

  .payCard {
    transition: transform 0.2s;
  }

  .payCard:hover {
    transform: translateY(-5px);
  }
</style>

<style lang="scss" scoped></style>
