<script setup lang="ts">
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
  import { requiredValidator } from '@validators'

  const {
    state: {
      loadingUpdateStripeCustomer,
      stripeCustomer,
      loadingCreateStripeCustomer,
    },
    actions: { handleUpdateStripeCustomer, handleCreateStripeCustomer },
  } = useStripeSubscription()

  interface Emit {
    (e: 'onClose', close: boolean): void
    (e: 'onComplete', customerId: string): void
  }

  const props = defineProps({
    open: <PERSON><PERSON><PERSON>,
  })

  const customerFormStore = useCustomerFormStore()

  onMounted(() => {
    customerFormStore.updateName(stripeCustomer.value?.name)
    customerFormStore.updateEmail(stripeCustomer.value?.email)
  })

  const close = false

  const emit = defineEmits<Emit>()

  const handleSaveCustomer = async () => {
    if (!stripeCustomer?.value?.id) {
      const newCustomer = await handleCreateStripeCustomer({
        name: customerFormStore.getCustomerName,
        email: customerFormStore.getCustomerEmail,
      })
      emit('onComplete', newCustomer.id)
    } else {
      await handleUpdateStripeCustomer({
        name: customerFormStore.getCustomerName,
        email: customerFormStore.getCustomerEmail,
        id: stripeCustomer?.value?.id,
      })
    }
    emit('onClose', close)
  }
</script>

<template>
  <v-row justify="center">
    <v-col cols="auto">
      <v-dialog
        transition="dialog-top-transition"
        width="500"
        :persistent="false"
        v-model="props.open"
      >
        <template v-slot:default="{ isActive }">
          <v-card>
            <v-toolbar density="compact" color="primary" class="px-2">
              <v-icon icon="tabler-device-floppy" />
              <v-toolbar-title>
                {{
                  stripeCustomer?.id
                    ? 'Benutzer für die Abrechnung aktualisieren'
                    : 'Benutzer für Abrechnung erstellen'
                }}</v-toolbar-title
              >
            </v-toolbar>
            <v-card-text>
              <VForm ref="formRef">
                <v-row>
                  <VCol cols="12">
                    <VTextField
                      v-model="customerFormStore.name"
                      :value="customerFormStore.name"
                      label="Rechnungsempfänger"
                      :rules="[requiredValidator]"
                    />
                  </VCol>
                  <VCol cols="12">
                    <VTextField
                      v-model="customerFormStore.email"
                      :value="customerFormStore.email"
                      label="E-Mail Adresse für Rechnungen"
                      :rules="[requiredValidator]"
                    />
                  </VCol>
                </v-row>
              </VForm>
            </v-card-text>
            <v-card-actions class="justify-end">
              <v-btn
                variant="text"
                size="small"
                @click="() => emit('onClose', close)"
                >Abbrechen</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                :loading="
                  loadingUpdateStripeCustomer || loadingCreateStripeCustomer
                "
                :disabled="
                  loadingUpdateStripeCustomer || loadingCreateStripeCustomer
                "
                color="primary"
                variant="flat"
                @click="() => handleSaveCustomer()"
                prepend-icon="tabler-device-floppy"
              >
                {{
                  stripeCustomer?.id
                    ? 'Benutzer aktualisieren'
                    : 'Benutzer erstellen'
                }}</v-btn
              >
            </v-card-actions>
          </v-card>
        </template>
      </v-dialog>
    </v-col>
  </v-row>
</template>

<style scoped lang="scss"></style>
