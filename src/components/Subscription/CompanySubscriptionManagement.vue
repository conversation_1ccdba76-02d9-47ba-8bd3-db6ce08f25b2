<script setup lang="ts">
import { useCompanySubscriptionGraph } from '@/api/graphHooks/useCompanySubscriptionGraph'
import { useAppStore } from '@/stores/appStore'
import { useRouter } from 'vue-router'
import { useTheme } from 'vuetify'

const router = useRouter()
const appStore = useAppStore()
const theme = useTheme()

const {
  state: {
    subscriptionStatus,
    subscriptionMetrics,
    loadingSubscriptionStatus,
    loadingSubscriptionMetrics,
    cancelingSubscription,
    hasActiveSubscription,
    isUnlimitedPlan,
    remainingJobAdverts,
    usagePercentage
  },
  actions: {
    loadSubscriptionMetrics,
    cancelSubscription,
    refetchSubscriptionStatus
  }
} = useCompanySubscriptionGraph()

const showCancelDialog = ref(false)
const cancellationReason = ref('')

// Format date
const formatDate = (date: string | Date) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('de', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

// Handle subscription cancellation
const handleCancelSubscription = async () => {
  if (!cancellationReason.value.trim()) {
    appStore.showSnack('Please provide a reason for cancellation')
    return
  }

  try {
    const result = await cancelSubscription({
      reason: cancellationReason.value
    })

    if (result?.data?.cancelCompanySubscription?.success) {
      appStore.showSnack('Subscription cancelled successfully')
      showCancelDialog.value = false
      cancellationReason.value = ''
      await refetchSubscriptionStatus()
    } else {
      throw new Error('Failed to cancel subscription')
    }
  } catch (error) {
    console.error('Cancellation error:', error)
    appStore.showSnack('Failed to cancel subscription. Please try again.')
  }
}

// Navigate to plans page
const navigateToPlans = () => {
  router.push('/company/subscription/plans')
}

// Navigate to billing portal
const navigateToBillingPortal = () => {
  // This would integrate with your existing Stripe portal logic
  router.push('/company/subscription/billing')
}

// Get status color
const getStatusColor = (status: string) => {
  switch(status?.toLowerCase()) {
    case 'active': return 'success'
    case 'trialing': return 'info'
    case 'past_due': return 'warning'
    case 'canceled': return 'error'
    default: return 'grey'
  }
}

// Get usage color based on percentage with null safety
const getUsageColor = () => {
  const percentage = usagePercentage.value || 0
  if (percentage < 50) return 'success'
  if (percentage < 80) return 'warning'
  return 'error'
}

onMounted(async () => {
  if (hasActiveSubscription.value) {
    await loadSubscriptionMetrics()
  }
})
</script>

<template>
  <VContainer>
    <!-- Header -->
    <VRow class="mb-6">
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h2 class="text-h4 mb-2">Subscription Management</h2>
            <p class="text-body-2 text-medium-emphasis">
              Manage your company's subscription and billing
            </p>
          </div>
          <div class="d-flex gap-2">
            <VBtn
              color="primary"
              variant="tonal"
              prepend-icon="mdi-credit-card"
              @click="navigateToBillingPortal"
            >
              Billing Portal
            </VBtn>
            <VBtn
              color="primary"
              prepend-icon="mdi-rocket-launch"
              @click="navigateToPlans"
            >
              View Plans
            </VBtn>
          </div>
        </div>
      </VCol>
    </VRow>

    <!-- Loading State -->
    <div v-if="loadingSubscriptionStatus" class="text-center py-12">
      <VProgressCircular indeterminate color="primary" :size="60" />
      <p class="mt-4">Loading subscription details...</p>
    </div>

    <!-- Subscription Content -->
    <template v-else>
      <!-- No Active Subscription -->
      <VCard v-if="!hasActiveSubscription" elevation="2">
        <VCardText class="text-center py-12">
          <VIcon 
            icon="mdi-credit-card-off" 
            size="80" 
            color="grey"
            class="mb-4"
          />
          <h3 class="text-h5 mb-2">No Active Subscription</h3>
          <p class="text-body-2 text-medium-emphasis mb-6">
            Choose a subscription plan to unlock premium features
          </p>
          <VBtn
            color="primary"
            size="large"
            @click="navigateToPlans"
          >
            Browse Plans
          </VBtn>
        </VCardText>
      </VCard>

      <!-- Active Subscription Details -->
      <VRow v-else>
        <!-- Current Plan Card -->
        <VCol cols="12" md="6">
          <VCard elevation="2" class="h-100">
            <VCardText>
              <div class="d-flex justify-space-between align-start mb-4">
                <div>
                  <h3 class="text-h6 mb-1">Current Plan</h3>
                  <h2 class="text-h4 font-weight-bold">
                    {{ subscriptionStatus?.planName || 'Unknown' }}
                  </h2>
                </div>
                <VChip
                  :color="getStatusColor(subscriptionStatus?.status)"
                  label
                >
                  {{ subscriptionStatus?.status }}
                </VChip>
              </div>

              <VDivider class="mb-4" />

              <VList density="compact" class="pa-0">
                <VListItem class="px-0">
                  <VListItemTitle class="text-body-2 text-medium-emphasis">
                    Billing Period
                  </VListItemTitle>
                  <template #append>
                    <span class="text-body-2 font-weight-medium">
                      {{ subscriptionStatus?.billingPeriod }}
                    </span>
                  </template>
                </VListItem>

                <VListItem class="px-0">
                  <VListItemTitle class="text-body-2 text-medium-emphasis">
                    Price
                  </VListItemTitle>
                  <template #append>
                    <span class="text-body-2 font-weight-medium">
                      {{ formatCurrency(subscriptionStatus?.amount || 0) }}
                    </span>
                  </template>
                </VListItem>

                <VListItem class="px-0">
                  <VListItemTitle class="text-body-2 text-medium-emphasis">
                    Started
                  </VListItemTitle>
                  <template #append>
                    <span class="text-body-2 font-weight-medium">
                      {{ formatDate(subscriptionStatus?.startDate) }}
                    </span>
                  </template>
                </VListItem>

                <VListItem class="px-0" v-if="subscriptionStatus?.currentPeriodEnd">
                  <VListItemTitle class="text-body-2 text-medium-emphasis">
                    {{ subscriptionStatus?.cancelAtPeriodEnd ? 'Expires' : 'Renews' }}
                  </VListItemTitle>
                  <template #append>
                    <span class="text-body-2 font-weight-medium">
                      {{ formatDate(subscriptionStatus?.currentPeriodEnd) }}
                    </span>
                  </template>
                </VListItem>
              </VList>

              <template v-if="!subscriptionStatus?.cancelAtPeriodEnd">
                <VDivider class="my-4" />
                <VBtn
                  color="error"
                  variant="outlined"
                  block
                  @click="showCancelDialog = true"
                >
                  Cancel Subscription
                </VBtn>
              </template>
              <VAlert
                v-else
                type="warning"
                variant="tonal"
                density="compact"
                class="mt-4"
              >
                Subscription will expire on {{ formatDate(subscriptionStatus?.currentPeriodEnd) }}
              </VAlert>
            </VCardText>
          </VCard>
        </VCol>

        <!-- Usage Statistics Card -->
        <VCol cols="12" md="6">
          <VCard elevation="2" class="h-100">
            <VCardText>
              <h3 class="text-h6 mb-4">Job Advert Usage</h3>

              <!-- Unlimited Plan -->
              <template v-if="isUnlimitedPlan">
                <div class="text-center py-8">
                  <VIcon 
                    icon="mdi-infinity" 
                    size="60" 
                    color="success"
                    class="mb-3"
                  />
                  <h4 class="text-h5 mb-2">Unlimited Job Adverts</h4>
                  <p class="text-body-2 text-medium-emphasis">
                    Create as many premium job adverts as you need
                  </p>
                  <VChip color="success" class="mt-3">
                    {{ subscriptionStatus?.jobAdvertsUsed || 0 }} created this period
                  </VChip>
                </div>
              </template>

              <!-- Limited Plan -->
              <template v-else>
                <div class="mb-4">
                  <div class="d-flex justify-space-between mb-2">
                    <span class="text-body-2">Used</span>
                    <span class="text-body-2 font-weight-medium">
                      {{ subscriptionStatus?.jobAdvertsUsed || 0 }} / 
                      {{ subscriptionStatus?.jobAdvertLimit || 0 }}
                    </span>
                  </div>
                  <VProgressLinear
                    :model-value="usagePercentage"
                    :color="getUsageColor()"
                    height="8"
                    rounded
                  />
                </div>

                <VAlert
                  v-if="remainingJobAdverts != null && remainingJobAdverts <= 5 && remainingJobAdverts > 0"
                  type="warning"
                  variant="tonal"
                  density="compact"
                  class="mb-4"
                >
                  Only {{ remainingJobAdverts }} job adverts remaining
                </VAlert>

                <VAlert
                  v-else-if="remainingJobAdverts != null && remainingJobAdverts === 0"
                  type="error"
                  variant="tonal"
                  density="compact"
                  class="mb-4"
                >
                  You've reached your job advert limit
                </VAlert>

                <VList density="compact" class="pa-0">
                  <VListItem class="px-0">
                    <VListItemTitle class="text-body-2 text-medium-emphasis">
                      Total Allowed
                    </VListItemTitle>
                    <template #append>
                      <span class="text-body-2 font-weight-medium">
                        {{ subscriptionStatus?.jobAdvertLimit || 0 }}
                      </span>
                    </template>
                  </VListItem>

                  <VListItem class="px-0">
                    <VListItemTitle class="text-body-2 text-medium-emphasis">
                      Used This Period
                    </VListItemTitle>
                    <template #append>
                      <span class="text-body-2 font-weight-medium">
                        {{ subscriptionStatus?.jobAdvertsUsed || 0 }}
                      </span>
                    </template>
                  </VListItem>

                  <VListItem class="px-0">
                    <VListItemTitle class="text-body-2 text-medium-emphasis">
                      Remaining
                    </VListItemTitle>
                    <template #append>
                      <VChip
                        :color="(remainingJobAdverts || 0) > 5 ? 'success' : 'warning'"
                        size="small"
                        label
                      >
                        {{ remainingJobAdverts || 0 }}
                      </VChip>
                    </template>
                  </VListItem>
                </VList>

                <VBtn
                  v-if="remainingJobAdverts != null && remainingJobAdverts < 5"
                  color="primary"
                  variant="tonal"
                  block
                  class="mt-4"
                  @click="navigateToPlans"
                >
                  Upgrade Plan
                </VBtn>
              </template>
            </VCardText>
          </VCard>
        </VCol>

        <!-- Subscription Metrics -->
        <VCol cols="12" v-if="subscriptionMetrics">
          <VCard elevation="2">
            <VCardText>
              <h3 class="text-h6 mb-4">Subscription Analytics</h3>
              
              <VRow>
                <VCol cols="12" sm="6" md="3">
                  <div class="text-center">
                    <VIcon icon="mdi-briefcase" size="40" color="primary" class="mb-2" />
                    <h4 class="text-h4 font-weight-bold">
                      {{ subscriptionMetrics.totalJobAdvertsCreated || 0 }}
                    </h4>
                    <p class="text-body-2 text-medium-emphasis">
                      Total Job Adverts
                    </p>
                  </div>
                </VCol>

                <VCol cols="12" sm="6" md="3">
                  <div class="text-center">
                    <VIcon icon="mdi-calendar-clock" size="40" color="info" class="mb-2" />
                    <h4 class="text-h4 font-weight-bold">
                      {{ subscriptionMetrics.currentPeriodJobAdverts || 0 }}
                    </h4>
                    <p class="text-body-2 text-medium-emphasis">
                      This Period
                    </p>
                  </div>
                </VCol>

                <VCol cols="12" sm="6" md="3">
                  <div class="text-center">
                    <VIcon icon="mdi-trending-up" size="40" color="success" class="mb-2" />
                    <h4 class="text-h4 font-weight-bold">
                      {{ subscriptionMetrics.averageMonthlyUsage || 0 }}
                    </h4>
                    <p class="text-body-2 text-medium-emphasis">
                      Avg. Monthly
                    </p>
                  </div>
                </VCol>

                <VCol cols="12" sm="6" md="3">
                  <div class="text-center">
                    <VIcon icon="mdi-cash" size="40" color="warning" class="mb-2" />
                    <h4 class="text-h4 font-weight-bold">
                      {{ formatCurrency(subscriptionMetrics.totalSpent || 0) }}
                    </h4>
                    <p class="text-body-2 text-medium-emphasis">
                      Total Spent
                    </p>
                  </div>
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </template>

    <!-- Cancel Subscription Dialog -->
    <VDialog
      v-model="showCancelDialog"
      max-width="500"
    >
      <VCard>
        <VCardTitle>
          <span class="text-h5">Cancel Subscription</span>
        </VCardTitle>
        
        <VCardText>
          <VAlert
            type="warning"
            variant="tonal"
            class="mb-4"
          >
            Your subscription will remain active until the end of the current billing period.
          </VAlert>

          <p class="mb-4">
            We're sorry to see you go. Please let us know why you're cancelling:
          </p>

          <VTextarea
            v-model="cancellationReason"
            label="Cancellation reason"
            placeholder="Your feedback helps us improve..."
            rows="3"
            variant="outlined"
            required
          />
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn
            variant="text"
            @click="showCancelDialog = false"
          >
            Keep Subscription
          </VBtn>
          <VBtn
            color="error"
            variant="flat"
            :loading="cancelingSubscription"
            :disabled="!cancellationReason.trim() || cancelingSubscription"
            @click="handleCancelSubscription"
          >
            Confirm Cancellation
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VContainer>
</template>

<style scoped>
:deep(.v-progress-linear) {
  transition: all 0.3s ease;
}
</style>
