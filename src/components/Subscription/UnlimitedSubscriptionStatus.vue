<template>
  <div v-if="hasActiveUnlimitedSubscription" class="unlimited-status-card">
    <VCard>
      <VCardTitle class="d-flex align-center">
        <VIcon icon="mdi-infinity" color="primary" class="mr-2" />
        Unlimited Subscription Active
      </VCardTitle>
      
      <VCardText>
        <VRow>
          <VCol cols="12" md="6">
            <div class="mb-2">
              <span class="text-caption">Status</span>
              <VChip 
                color="success" 
                label 
                small
                class="ml-2"
              >
                AKTIV
              </VChip>
            </div>
            
            <div class="mb-2">
              <span class="text-caption">Stellenanzeigen</span>
              <div class="text-h6">Unbegrenzt</div>
            </div>
          </VCol>
          
          <VCol cols="12" md="6">
            <div class="mb-2">
              <span class="text-caption">Gültig bis</span>
              <div class="text-h6">
                {{ formatDate(unlimitedSubscriptionDetails?.expiresAt) }}
              </div>
            </div>
            
            <div v-if="remainingDays > 0" class="mb-2">
              <span class="text-caption">Verbleibende Tage</span>
              <div class="text-h6">
                {{ remainingDays }}
                <VChip 
                  v-if="isExpiringSort"
                  color="warning" 
                  label 
                  x-small
                  class="ml-2"
                >
                  Läuft bald ab
                </VChip>
              </div>
            </div>
          </VCol>
        </VRow>
        
        <VDivider class="my-4" />
        
        <div class="text-caption mb-2">
          <VIcon icon="mdi-information-outline" size="small" />
          Mit Ihrem Unlimited-Abonnement können Sie unbegrenzt Stellenanzeigen erstellen und veröffentlichen.
        </div>
        
        <div v-if="isExpiringSort" class="mt-3">
          <VAlert
            type="warning"
            variant="tonal"
            density="compact"
          >
            <template #prepend>
              <VIcon icon="mdi-alert" />
            </template>
            Ihr Abonnement läuft in {{ remainingDays }} Tagen ab. 
            <router-link :to="{ name: 'company-subscription-renew' }">
              Jetzt verlängern
            </router-link>
          </VAlert>
        </div>
      </VCardText>
      
      <VCardActions>
        <VSpacer />
        <VBtn
          variant="text"
          color="primary"
          :to="{ name: 'company-subscription-manage' }"
        >
          Abonnement verwalten
        </VBtn>
      </VCardActions>
    </VCard>
  </div>
  
  <div v-else-if="loading" class="unlimited-status-loading">
    <VSkeletonLoader type="card" />
  </div>
</template>

<script setup lang="ts">
import { useUnlimitedSubscriptionStatus } from '@/composables/StripeSubscription/useUnlimitedSubscriptionStatus'

const {
  hasActiveUnlimitedSubscription,
  unlimitedSubscriptionDetails,
  remainingDays,
  isExpiringSort,
  loading
} = useUnlimitedSubscriptionStatus()

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  return date.toLocaleDateString('de-DE', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<style scoped>
.unlimited-status-card {
  margin-bottom: 1.5rem;
}

.unlimited-status-loading {
  margin-bottom: 1.5rem;
}
</style>
