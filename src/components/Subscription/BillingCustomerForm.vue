<script setup lang="ts">
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { requiredValidator } from '@validators'

  const companyStore = useCompanyStore()

  const useSubStore = useSubscriptionStore()

  const customerFormStore = useCustomerFormStore()

  const {
    state: { paymentMethods: activePaymentMethods },
    actions: { refetchPaymentMethods },
  } = useStripeSubscription()

  const paymentMethods = useSubStore.paymentMethod?.data

  onMounted(async () => {
    await companyStore.init()
    await refetchPaymentMethods()
    customerFormStore.updateCustomerForm(
      activePaymentMethods.value.data?.[0]?.billing_details,
    )
  })
</script>

<template>
  <v-card-text>
    <div v-if="activePaymentMethods.data?.length" class="my-2">
      <v-chip
        prepend-icon="mdi-account"
        v-for="payMethod in activePaymentMethods.data"
        @click="
          customerFormStore.updateCustomerForm(payMethod?.billing_details)
        "
        class="mx-1 mb-4"
      >
        {{ payMethod.billing_details?.name }}
      </v-chip>
    </div>
    <VForm ref="formRef">
      <v-row>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.name"
            label="Rechnungsempfänger"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.email"
            label="E-Mail Adresse für Rechnungen"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.country"
            label="Land"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.addressLine1"
            label="Straße & Hausnummer"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <VTextField v-model="customerFormStore.addressLine2" label="Zusatz" />
        </VCol>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.postalCode"
            label="PLZ"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <VTextField
            v-model="customerFormStore.city"
            label="Stadt"
            :rules="[requiredValidator]"
          />
        </VCol>
      </v-row>
    </VForm>
  </v-card-text>
</template>
