<template>
  <div class="unlimited-subscription-table">
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <VIcon icon="mdi-infinity" color="primary" class="mr-2" />
          <span>Subscription Details</span>
        </div>
        <VChip v-if="hasActiveSubscription" color="success" label>
          <VIcon icon="mdi-check-circle" size="small" class="mr-1" />
          AKTIV
        </VChip>
      </VCardTitle>

      <VCardText>
        <!-- Search and Filters -->
        <div class="d-flex align-end flex-wrap gap-3 mb-4">
          <AppTextField
            v-model="searchQuery"
            placeholder="Subscription suchen"
            density="compact"
            prepend-inner-icon="mdi-magnify"
            class="me-3"
            style="max-width: 300px"
          />
        </div>

        <VDivider class="mb-4" />

        <!-- Data Table -->
        <VDataTable
          :headers="headers"
          :items="unlimitedSubscriptions"
          :loading="loading"
          :search="searchQuery"
          item-value="id"
          hover
          class="unlimited-data-table"
        >
          <!-- Loading -->
          <template #loading>
            <VSkeletonLoader type="table-row@3" />
          </template>

          <!-- No Data -->
          <template #no-data>
            <div class="text-center py-8">
              <VIcon icon="mdi-infinity-off" size="48" color="grey" />
              <div class="text-h6 mt-2">
                Kein Unlimited-Abonnement vorhanden
              </div>
              <div class="text-caption">
                Upgraden Sie auf ein Unlimited-Abonnement für unbegrenzte
                Stellenanzeigen
              </div>
              <VBtn
                color="primary"
                class="mt-4"
                :to="{ name: 'company-subscription-plans' }"
              >
                Pläne anzeigen
              </VBtn>
            </div>
          </template>

          <!-- Status Column -->
          <template #[`item.status`]="{ item }">
            <VChip :color="getStatusColor(item.status)" label size="small">
              {{ getStatusLabel(item.status) }}
            </VChip>
          </template>

          <!-- Price Column -->
          <template #[`item.price`]="{ item }">
            <div>
              <div class="font-weight-medium">
                {{ formatCurrency(getMonthlyPrice(item)) }}
              </div>
              <div v-if="getPriceBreakdown(item).hasBreakdown" class="text-caption text-medium-emphasis">
                €{{ getPriceBreakdown(item).basePrice }}/mo × {{ getPriceBreakdown(item).months }}
              </div>
            </div>
          </template>

          <!-- Duration Column -->
          <template #[`item.duration`]="{ item }">
            <div class="d-flex align-center">
              <VIcon icon="mdi-calendar-range" size="small" class="mr-1" />
              {{ getSubscriptionPeriod(item) }}
            </div>
          </template>

          <!-- Created At Column -->
          <template #[`item.createdAt`]="{ item }">
            <div class="text-caption">
              {{ formatDate(item.createdAt) }}
            </div>
          </template>

          <!-- Expires At Column -->
          <template #[`item.expiresAt`]="{ item }">
            <div class="d-flex flex-column">
              <span class="text-caption">
                {{ formatDate(item.expiresAt || item.currentPeriodEnd) }}
              </span>
              <VChip
                v-if="isExpiringSoon(item)"
                color="warning"
                label
                size="x-small"
                class="mt-1"
              >
                {{ getRemainingDays(item) }} Tage
              </VChip>
            </div>
          </template>

          <!-- Actions Column -->
          <template #[`item.actions`]="{ item }">
            <div class="d-flex gap-1">
              <VTooltip text="Details anzeigen">
                <template #activator="{ props }">
                  <VBtn
                    v-bind="props"
                    icon="mdi-eye"
                    size="small"
                    variant="text"
                    @click.stop="viewDetails(item)"
                  />
                </template>
              </VTooltip>

              <VTooltip
                text="Rechnung herunterladen"
                v-if="item.hostedInvoiceUrl || item.invoicePdf"
              >
                <template #activator="{ props }">
                  <VBtn
                    v-bind="props"
                    icon="mdi-download"
                    size="small"
                    variant="text"
                    @click.stop="downloadInvoice(item)"
                  />
                </template>
              </VTooltip>

              <VTooltip text="Abonnement verwalten">
                <template #activator="{ props }">
                  <VBtn
                    v-bind="props"
                    icon="mdi-cog"
                    size="small"
                    variant="text"
                    @click.stop="manageSubscription(item)"
                  />
                </template>
              </VTooltip>

              <VTooltip
                text="Kündigen"
                v-if="item.isActive && !item.cancelAtPeriodEnd"
              >
                <template #activator="{ props }">
                  <VBtn
                    v-bind="props"
                    icon="mdi-cancel"
                    size="small"
                    variant="text"
                    color="error"
                    @click.stop="cancelSubscription(item)"
                  />
                </template>
              </VTooltip>
            </div>
          </template>
        </VDataTable>
      </VCardText>
    </VCard>

    <!-- Details Dialog -->
    <VDialog v-model="detailsDialog" max-width="600">
      <VCard v-if="selectedSubscription">
        <VCardTitle>
          Abonnement Details
          <VBtn
            icon="mdi-close"
            variant="text"
            class="float-right"
            @click="detailsDialog = false"
          />
        </VCardTitle>

        <VCardText>
          <VList>
            <VListItem>
              <template #prepend>
                <VIcon icon="mdi-identifier" />
              </template>
              <VListItemTitle>Subscription ID</VListItemTitle>
              <VListItemSubtitle>{{
                selectedSubscription.stripeSubscriptionId
              }}</VListItemSubtitle>
            </VListItem>

            <VListItem>
              <template #prepend>
                <VIcon icon="mdi-calendar" />
              </template>
              <VListItemTitle>Laufzeit</VListItemTitle>
              <VListItemSubtitle>{{
                getSubscriptionPeriod(selectedSubscription)
              }}</VListItemSubtitle>
            </VListItem>

            <VListItem>
              <template #prepend>
                <VIcon icon="mdi-currency-eur" />
              </template>
              <VListItemTitle>Preis</VListItemTitle>
              <VListItemSubtitle>
                {{
                  formatCurrency(getMonthlyPrice(selectedSubscription))
                }}/Monat
              </VListItemSubtitle>
            </VListItem>

            <VListItem v-if="selectedSubscription.percent_off">
              <template #prepend>
                <VIcon icon="mdi-sale" />
              </template>
              <VListItemTitle>Rabatt</VListItemTitle>
              <VListItemSubtitle
                >{{ selectedSubscription.percent_off }}%</VListItemSubtitle
              >
            </VListItem>

            <VListItem>
              <template #prepend>
                <VIcon icon="mdi-clock-start" />
              </template>
              <VListItemTitle>Startdatum</VListItemTitle>
              <VListItemSubtitle>{{
                formatDate(selectedSubscription.createdAt)
              }}</VListItemSubtitle>
            </VListItem>

            <VListItem>
              <template #prepend>
                <VIcon icon="mdi-clock-end" />
              </template>
              <VListItemTitle>Ablaufdatum</VListItemTitle>
              <VListItemSubtitle>
                {{
                  formatDate(
                    selectedSubscription.expiresAt ||
                      selectedSubscription.currentPeriodEnd,
                  )
                }}
              </VListItemSubtitle>
            </VListItem>
          </VList>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn variant="text" @click="detailsDialog = false">Schließen</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useQuery, useMutation } from '@vue/apollo-composable'
  import { apolloClient } from '@/api/middleware/apolloClient'
  import { provideApolloClient } from '@vue/apollo-composable'
  import { useAuthStore } from '@/stores/authStore'
  import { useAppStore } from '@/stores/appStore'
  import { useRouter } from 'vue-router'
  import { useTheme } from 'vuetify'
  import { confirmUnlimitedSubscriptionCancel } from '@/composables/useSweetAlert'
  import { GET_UNLIMITED_SUBSCRIPTIONS } from '@/api/graphql/queries/unlimitedSubscriptionQueries'
  import { CANCEL_SUBSCRIPTION } from '@/api/graphql/mutations/unlimitedSubscriptionMutations'

  provideApolloClient(apolloClient)

  const authStore = useAuthStore()
  const appStore = useAppStore()
  const router = useRouter()
  const theme = useTheme()

  const detailsDialog = ref(false)
  const selectedSubscription = ref<any>(null)
  const searchQuery = ref('')

  // Props
  const props = defineProps<{
    activeJobAdsCount?: number
  }>()

  // Table headers
  const headers = computed(() => [
    { title: 'Status', key: 'status', sortable: true },
    { title: 'Preis/Monat', key: 'price', sortable: true },
    { title: 'Laufzeit', key: 'duration', sortable: false },
    { title: 'Erstellt am', key: 'createdAt', sortable: true },
    { title: 'Läuft ab am', key: 'expiresAt', sortable: true },
    { title: 'Aktionen', key: 'actions', sortable: false, align: 'center' },
  ])

  // GraphQL Queries
  const GET_UNLIMITED_SUBSCRIPTIONS = gql`
    query GetUnlimitedSubscriptions($companyId: String!) {
      subscriptionsByCompanyId(companyId: $companyId) {
        id
        subscriptionType
        isActive
        status
        stripeSubscriptionId
        stripePriceId
        amountTotal
        currency
        percent_off
        invoiceId
        createdAt
        expiresAt
        currentPeriodStart
        currentPeriodEnd
        cancelAtPeriodEnd
        metadata
      }
    }
  `

  const CANCEL_SUBSCRIPTION = gql`
    mutation CancelSubscription($subscriptionId: String!) {
      cancelSubscription(
        subscriptionIdInput: { subscriptionId: $subscriptionId }
      ) {
        id
        status
        cancel_at_period_end
      }
    }
  `

  // Query for unlimited subscriptions
  const { result, loading, refetch } = useQuery(
    GET_UNLIMITED_SUBSCRIPTIONS,
    () => ({
      companyId: authStore.companyId,
    }),
    {
      fetchPolicy: 'cache-and-network',
    },
  )

  // Computed to filter only unlimited subscriptions
  const unlimitedSubscriptions = computed(() => {
    if (!result.value?.subscriptionsByCompanyId) return []

    return result.value.subscriptionsByCompanyId.filter(
      (sub: any) =>
        sub.subscriptionType === 'COMPANY_UNLIMITED' && sub.isActive,
    )
  })

  // Check if there's any active subscription
  const hasActiveSubscription = computed(() => {
    return unlimitedSubscriptions.value.some((sub: any) => sub.isActive)
  })

  // Helper functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount / 100) // Convert from cents
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '-'

    const date = new Date(dateString)
    return date.toLocaleDateString('de-DE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'past_due':
        return 'warning'
      case 'canceled':
        return 'error'
      case 'trialing':
        return 'info'
      default:
        return 'grey'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktiv'
      case 'past_due':
        return 'Überfällig'
      case 'canceled':
        return 'Gekündigt'
      case 'trialing':
        return 'Testphase'
      default:
        return status
    }
  }

  const getMonthlyPrice = (subscription: any) => {
    try {
      const metadata = subscription.metadata
      if (metadata && typeof metadata === 'object') {
        // Check for the new pricing structure
        if (metadata.baseMonthlyPrice && metadata.minimumMonths) {
          const basePrice = parseFloat(metadata.baseMonthlyPrice)
          const months = parseInt(metadata.minimumMonths)
          return (basePrice * months) * 100 // Total price in cents
        }
        // Legacy check
        if (metadata.monthlyPrice) {
          return metadata.monthlyPrice * 100 // Convert to cents
        }
      }
    } catch (e) {
      // Fallback to amountTotal
    }

    // Fallback: use amountTotal directly
    return subscription.amountTotal || 0
  }

  const getSubscriptionPeriod = (subscription: any) => {
    try {
      const metadata = subscription.metadata
      if (metadata && typeof metadata === 'object') {
        // Check for minimumMonths first (new structure)
        if (metadata.minimumMonths) {
          const months = parseInt(metadata.minimumMonths)
          if (months === 1) return '1 Monat'
          if (months === 3) return '3 Monate (Vierteljährlich)'
          if (months === 6) return '6 Monate (Halbjährlich)'
          if (months === 12) return '1 Jahr (Jährlich)'
          return `${months} Monate`
        }
        // Legacy check with durationDays
        if (metadata.durationDays) {
          const months = Math.round(metadata.durationDays / 30)
          if (months === 1) return '1 Monat'
          if (months === 3) return '3 Monate (Vierteljährlich)'
          if (months === 6) return '6 Monate (Halbjährlich)'
          if (months === 12) return '1 Jahr (Jährlich)'
          return `${months} Monate`
        }
      }
    } catch (e) {
      // Fallback
    }

    return '6 Monate' // Default
  }

  const getPriceBreakdown = (subscription: any) => {
    try {
      const metadata = subscription.metadata
      if (metadata && typeof metadata === 'object') {
        if (metadata.baseMonthlyPrice && metadata.minimumMonths) {
          const basePrice = parseFloat(metadata.baseMonthlyPrice)
          const months = parseInt(metadata.minimumMonths)
          return {
            basePrice,
            months,
            total: basePrice * months,
            hasBreakdown: true
          }
        }
      }
    } catch (e) {
      // Fallback
    }
    
    return {
      basePrice: 0,
      months: 1,
      total: subscription.amountTotal / 100 || 0,
      hasBreakdown: false
    }
  }

  const getRemainingDays = (subscription: any) => {
    const expiresAt = new Date(
      subscription.expiresAt || subscription.currentPeriodEnd,
    )
    const now = new Date()
    const diffTime = Math.abs(expiresAt.getTime() - now.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const isExpiringSoon = (subscription: any) => {
    const days = getRemainingDays(subscription)
    return days > 0 && days <= 30
  }

  const getTotalMonthlyPrice = () => {
    return unlimitedSubscriptions.value.reduce((total: number, sub: any) => {
      return total + getMonthlyPrice(sub) / 100
    }, 0)
  }

  const getNextPaymentDate = () => {
    const nextPayment = unlimitedSubscriptions.value[0]?.currentPeriodEnd
    return nextPayment ? formatDate(nextPayment) : '-'
  }

  // Actions
  const viewDetails = (subscription: any) => {
    selectedSubscription.value = subscription
    detailsDialog.value = true
  }

  const downloadInvoice = async (subscription: any) => {
    try {
      // Check if we have invoice URLs from the subscription
      if (subscription.hostedInvoiceUrl) {
        // Open the hosted invoice URL in a new tab
        appStore.showSnack('Rechnung wird in einem neuen Tab geöffnet...')
      } else if (subscription.invoicePdf) {
        // If we have a direct PDF URL, open it
        window.open(subscription.invoicePdf, '_blank')
        appStore.showSnack('PDF wird heruntergeladen...')
      } else if (subscription.invoiceId) {
        // Fallback: Try to fetch invoice from Stripe via backend
        appStore.showSnack('Rechnung wird abgerufen...')
        // You would need to implement a GraphQL query to fetch the invoice URL
        // For now, show an error
        appStore.showSnack(
          'Rechnung konnte nicht heruntergeladen werden. Bitte versuchen Sie es später erneut.',
          'error',
        )
      } else {
        appStore.showSnack('Keine Rechnung verfügbar', 'warning')
      }
    } catch (error: any) {
      console.error('Error downloading invoice:', error)
      appStore.showSnack(
        `Fehler beim Herunterladen der Rechnung: ${error.message}`,
        'error',
      )
    }
  }

  const manageSubscription = (subscription: any) => {
    router.push({
      name: 'company-subscription-manage',
      query: { subscriptionId: subscription.id },
    })
  }

  const { mutate: cancelSubscriptionMutation } =
    useMutation(CANCEL_SUBSCRIPTION)

  const cancelSubscription = async (subscription: any) => {
    const isDarkLayout = theme.global.current.value.dark
    const result = await confirmUnlimitedSubscriptionCancel(isDarkLayout)

    if (!result.isConfirmed) {
      return
    }

    try {
      await cancelSubscriptionMutation({
        subscriptionId: subscription.stripeSubscriptionId,
      })

      appStore.showSnack(
        'Abonnement wurde gekündigt und läuft am Ende der Periode aus.',
        'success',
      )
      await refetch()
    } catch (error: any) {
      appStore.showSnack(`Fehler beim Kündigen: ${error.message}`, 'error')
    }
  }

  onMounted(() => {
    refetch()
  })
</script>

<style scoped>
  .unlimited-subscription-table {
    margin-top: 1rem;
  }

  .gap-1 {
    gap: 0.25rem;
  }

  .unlimited-data-table :deep(.v-data-table__td) {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  /* SweetAlert Dark Theme Support */
  :global(.swal2-popup.dark) {
    background-color: rgb(var(--v-theme-surface));
    color: rgb(var(--v-theme-on-surface));
  }

  :global(.swal2-popup.dark .swal2-title),
  :global(.swal2-popup.dark .swal2-content) {
    color: rgb(var(--v-theme-on-surface));
  }

  :global(.swal2-popup.dark .swal2-input),
  :global(.swal2-popup.dark .swal2-textarea) {
    background-color: rgb(var(--v-theme-surface-variant));
    color: rgb(var(--v-theme-on-surface));
    border-color: rgba(var(--v-theme-on-surface), 0.12);
  }
</style>
