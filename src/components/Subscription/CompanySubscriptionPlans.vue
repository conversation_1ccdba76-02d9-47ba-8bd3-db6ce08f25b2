<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue'
  import { useCompanySubscriptionGraph } from '@/api/graphHooks/useCompanySubscriptionGraph'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useRouter, useRoute } from 'vue-router'
  import spaceRocket from '@images/misc/3d-space-rocket-with-smoke.png'
  import dollarCoinPiggyBank from '@images/misc/dollar-coins-flying-pink-piggy-bank.png'
  import crown from '@images/misc/3d-safe-box-with-golden-dollar-coins.png'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useUnlimitedSubscriptionStatus } from '@/composables/StripeSubscription/useUnlimitedSubscriptionStatus'

  const route = useRoute()
  const router = useRouter()
  const appStore = useAppStore()
  const authStore = useAuthStore()
  const companyGraph = useCompanyGraph()
  const subscriptionStore = useSubscriptionStore()

  // Check for existing unlimited subscription
  const {
    hasActiveUnlimitedSubscription,
    unlimitedSubscriptionDetails,
    remainingDays,
    isExpiringSort,
  } = useUnlimitedSubscriptionStatus()
  const companySubscriptionPlans = computed(
    () =>
      companyGraph?.state?.companySubscriptionPlans || {
        availablePlans: [],
        activeSubscriptions: [],
      },
  )

  const loadCompanySubscriptionPlans =
    companyGraph?.actions?.loadCompanySubscriptionPlans ||
    (() => Promise.resolve())
  
  // Get pricing plans from subscription store's config
  const activePlans = computed(() => {
    return subscriptionStore.availablePricingPlans || []
  })
  
  const loadingActivePlans = computed(() => {
    return subscriptionStore.subscriptionConfigLoading || false
  })

  const recommendedPlan = computed(() => {
    const plans = activePlans.value || []
    return plans.find((plan: any) => plan?.isPopular) || null
  })

  const companySubscriptionGraph = useCompanySubscriptionGraph()

  const createCheckoutSession =
    companySubscriptionGraph?.actions?.createCheckoutSession ||
    (() => Promise.resolve())

  const selectedPlanId = ref<string | null>(null)
  const loadingCheckout = ref(false)
  const companyPlansLoadAttempted = ref(false)
  const forceShowPlans = ref(false)

  const getPlanLogo = (planType: string) => {
    switch (planType) {
      case 'STANDARD':
        return dollarCoinPiggyBank
      case 'PROMOTIONAL':
        return spaceRocket
      case 'ENTERPRISE':
        return crown
      case 'CUSTOM':
        return crown
      default:
        return dollarCoinPiggyBank
    }
  }

  const getDisplayPrice = (plan: any) => {
    if (!plan) return 0
    
    // Calculate total price based on duration
    const months = plan.durationDays ? Math.max(1, Math.round(plan.durationDays / 30)) : 1
    const basePrice = plan.price || 0
    
    // Return total price (base * months)
    return basePrice * months
  }

  const getSavingsText = (plan: any) => {
    if (!plan || plan.billingPeriod === 'MONTHLY') return ''

    switch (plan.billingPeriod) {
      case 'ANNUAL':
        return 'Save 17%'
      case 'QUARTERLY':
        return 'Save 8%'
      default:
        return ''
    }
  }

  const getBillingPeriodText = (plan: any) => {
    if (!plan) return 'period'
    
    // Calculate billing period from durationDays
    const months = plan.durationDays ? Math.max(1, Math.round(plan.durationDays / 30)) : 1
    
    if (months === 1) return 'month'
    if (months === 3) return '3 months'
    if (months === 6) return '6 months'
    if (months === 12) return 'year'
    return `${months} months`
  }

  const getBaseMonthlyPrice = (plan: any) => {
    if (!plan) return 0
    return plan.price || 0
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount)
  }

  const handleSelectPlan = async (plan: any) => {
    if (!plan || !plan.id) {
      appStore.showSnack('Invalid plan selected')
      return
    }

    if (isCurrentPlan(plan)) {
      appStore.showSnack('You are already subscribed to this plan')
      return
    }

    // Check if company already has an active unlimited subscription
    if (hasUnlimitedSubscription.value) {
      appStore.showSnack(
        'You already have an active unlimited subscription. Please cancel your current subscription before selecting a new plan.',
      )
      return
    }

    const planPrice = plan.price || 0
    if (planPrice === 0) {
      appStore.showSnack(
        'Free plan is automatically active without subscription',
      )
      return
    }

    selectedPlanId.value = plan.id
    loadingCheckout.value = true

    try {
      await subscriptionStore.fetchSubscriptionConfig()

      if (subscriptionStore.isCompanyUnlimited) {
        await router.push({
          name: 'company-subscription-checkout-unlimited',
          query: {
            planId: plan.id,
            ...route.query,
          },
        })
      } else {
        const successUrl = new URL(
          `${window.location.origin}/company/subscription/success`,
        )
        const cancelUrl = new URL(
          `${window.location.origin}/company/subscription/plans`,
        )

        if (route.query.returnTo) {
          successUrl.searchParams.append(
            'returnTo',
            route.query.returnTo as string,
          )
          cancelUrl.searchParams.append(
            'returnTo',
            route.query.returnTo as string,
          )
        }
        if (route.query.isNewCompany) {
          successUrl.searchParams.append(
            'isNewCompany',
            route.query.isNewCompany as string,
          )
          cancelUrl.searchParams.append(
            'isNewCompany',
            route.query.isNewCompany as string,
          )
        }
        if (route.query.companyId) {
          successUrl.searchParams.append(
            'companyId',
            route.query.companyId as string,
          )
          cancelUrl.searchParams.append(
            'companyId',
            route.query.companyId as string,
          )
        }

        // Use the plan's billing period instead of selected billing period
        const result = await createCheckoutSession({
          input: {
            planId: plan.id,
            billingPeriod: plan.billingPeriod || 'MONTHLY',
            successUrl: successUrl.toString(),
            cancelUrl: cancelUrl.toString(),
          },
        })

        if (result?.data?.createCompanyCheckoutSession?.url) {
          window.location.href = result.data.createCompanyCheckoutSession.url
        } else {
          throw new Error('Failed to create checkout session')
        }
      }
    } catch (error) {
      console.error('Checkout error:', error)
      appStore.showSnack('Failed to start checkout. Please try again.')
    } finally {
      loadingCheckout.value = false
      selectedPlanId.value = null
    }
  }

  const isCurrentPlan = (plan: any) => {
    try {
      if (!plan?.id) return false

      // Check if any active subscription matches this plan
      const activeSubscriptions =
        companySubscriptionPlans.value?.activeSubscriptions || []
      return activeSubscriptions.some(
        (sub: any) => sub?.pricingPlanId === plan.id && sub?.isActive,
      )
    } catch (error) {
      console.error('Error in isCurrentPlan:', error)
      return false
    }
  }

  const hasUnlimitedSubscription = computed(() => {
    return hasActiveUnlimitedSubscription.value
  })

  const isUpgrade = (plan: any) => {
    if (!plan) return false

    const activeSubscriptions =
      companySubscriptionPlans.value?.activeSubscriptions || []
    if (activeSubscriptions.length === 0) return true

    // Find current active subscription
    const currentSub = activeSubscriptions.find((sub: any) => sub?.isActive)
    if (!currentSub?.pricingPlan) return true

    const planPrice = plan.price || 0
    const currentPrice = currentSub.pricingPlan.price || 0

    return planPrice > currentPrice
  }

  const currentSubscriptionInfo = computed(() => {
    try {
      const activeSubscriptions =
        companySubscriptionPlans.value?.activeSubscriptions || []
      const activeSub = activeSubscriptions.find((sub: any) => sub?.isActive)

      if (!activeSub) return null

      return {
        planName:
          activeSub.pricingPlan?.displayName ||
          activeSub.pricingPlan?.name ||
          'Unknown Plan',
        isActive: activeSub.isActive,
        currentPeriodEnd: activeSub.currentPeriodEnd,
        cancelAtPeriodEnd: activeSub.cancelAtPeriodEnd,
        status: activeSub.status,
      }
    } catch (error) {
      console.error('Error in currentSubscriptionInfo:', error)
      return null
    }
  })

  onMounted(async () => {
    try {
      // Ensure subscription config is loaded
      if (authStore?.companyId) {
        await subscriptionStore.fetchSubscriptionConfig()
      }

      // Load company-specific subscription plans (non-blocking)
      if (authStore?.companyId && loadCompanySubscriptionPlans) {
        loadCompanySubscriptionPlans().catch(error => {
          console.warn(
            'Company subscription plans failed to load:',
            error,
          )
        })
        companyPlansLoadAttempted.value = true
      }

      // Fallback: if still loading after 2 seconds, force show plans
      setTimeout(() => {
        if (loadingPlans.value && !forceShowPlans.value) {
          forceShowPlans.value = true
        }
      }, 2000)
    } catch (error) {
      console.error('Error loading subscription config:', error)
      appStore.showSnack('Failed to load subscription configuration')
    }
  })

  const availablePlansToShow = computed(() => {
    try {
      // Use pricing plans from subscription config only
      const configPlans = subscriptionStore.availablePricingPlans || []
      
      // If we have company subscription plans loaded, prefer those for additional metadata
      const companyPlans = companySubscriptionPlans.value?.availablePlans || []
      
      // If company plans are loaded and match config plans, use company plans (they might have more metadata)
      if (companyPlans.length > 0) {
        const configPlanIds = new Set(configPlans.map((p: any) => p.id))
        const filteredCompanyPlans = companyPlans.filter((p: any) => configPlanIds.has(p.id))
        if (filteredCompanyPlans.length > 0) {
          return filteredCompanyPlans
        }
      }
      
      // Otherwise use the plans from subscription config
      return configPlans
    } catch (error) {
      console.error('Error in availablePlansToShow:', error)
      return []
    }
  })

  const loadingPlans = computed(() => {
    try {
      const plansLoading = loadingActivePlans.value || false

      const hasActivePlans = (activePlans.value?.length || 0) > 0
      const hasCompanyPlans =
        (companySubscriptionPlans.value?.availablePlans?.length || 0) > 0
      const hasAnyPlans = hasActivePlans || hasCompanyPlans

      const shouldShowLoading =
        plansLoading && !hasAnyPlans && !forceShowPlans.value

      return shouldShowLoading
    } catch (error) {
      console.error('Error in loadingPlans:', error)
      return false
    }
  })

  // Add computed for enabling payment button
  const enablePayButton = computed(() => {
    // Allow payment if no unlimited subscription or if the subscription is expiring soon
    return !hasUnlimitedSubscription.value || isExpiringSort.value
  })
</script>

<template>
  <VContainer>
    <!-- Header -->
    <div
      class="text-center mb-12"
      v-if="enablePayButton && availablePlansToShow.length > 0"
    >
      <h4 class="text-h4 mb-4">Available Subscription Plans</h4>
      <p class="text-body-1 mb-6">
        Select from the plans assigned to your company
      </p>
    </div>

    <!-- Active Unlimited Subscription Alert -->
    <VAlert
      v-if="hasUnlimitedSubscription && !isExpiringSort"
      type="success"
      variant="elevated"
      class="mb-6"
      prominent
    >
      <VAlertTitle class="d-flex align-center">
        <VIcon icon="mdi-check-circle" class="mr-2" />
        Active Unlimited Subscription
      </VAlertTitle>
      <div class="mt-2">
        <p class="mb-2">
          Your company has an active unlimited subscription that includes
          unlimited job advertisements.
        </p>
        <p class="mb-0" v-if="unlimitedSubscriptionDetails?.currentPeriodEnd">
          <strong>Valid until:</strong>
          {{
            new Date(
              unlimitedSubscriptionDetails.currentPeriodEnd,
            ).toLocaleDateString()
          }}
          <VChip
            v-if="remainingDays > 0"
            size="small"
            color="success"
            variant="tonal"
            class="ml-2"
          >
            {{ remainingDays }} days remaining
          </VChip>
        </p>
      </div>
    </VAlert>

    <!-- Expiring Soon Alert -->
    <VAlert
      v-if="isExpiringSort"
      type="warning"
      variant="elevated"
      class="mb-6"
      prominent
    >
      <VAlertTitle class="d-flex align-center">
        <VIcon icon="mdi-alert-circle" class="mr-2" />
        Subscription Expiring Soon
      </VAlertTitle>
      <div class="mt-2">
        <p class="mb-2">
          Your unlimited subscription is expiring in
          <strong>{{ remainingDays }} days</strong>. Select a new plan to
          continue enjoying unlimited job advertisements.
        </p>
      </div>
    </VAlert>

    <!-- Loading State -->
    <div v-if="loadingPlans" class="text-center py-12">
      <VProgressCircular indeterminate color="primary" :size="60" />
      <p class="mt-4">Loading available plans...</p>
    </div>

    <!-- No Plans Available -->
    <div
      v-else-if="availablePlansToShow.length === 0"
      class="text-center py-12"
    >
      <VIcon
        icon="mdi-information-outline"
        size="64"
        color="warning"
        class="mb-4"
      />
      <h3 class="text-h5 mb-2">No Pricing Plans Assigned</h3>
      <p class="text-body-2 text-medium-emphasis">
        No pricing plans have been assigned to your company yet.
      </p>
      <p class="text-body-2 text-medium-emphasis">
        Please contact your administrator or support to assign pricing plans.
      </p>
    </div>

    <!-- Plans Grid -->
    <VRow v-else>
      <VCol
        v-for="plan in availablePlansToShow"
        :key="plan.id"
        cols="12"
        md="4"
        class="mx-auto"
      >
        <VCard
          :elevation="plan.id === recommendedPlan?.id ? 8 : 2"
          :class="{
            'border-primary': plan.id === recommendedPlan?.id,
            'current-plan': isCurrentPlan(plan),
          }"
          class="h-100 d-flex flex-column"
        >
          <VCardText class="flex-grow-1">
            <!-- Plan Logo -->
            <div class="text-center mb-4">
              <VImg
                :src="getPlanLogo(plan.planType)"
                :height="120"
                class="mx-auto"
              />
            </div>

            <!-- Plan Name & Description -->
            <h5 class="text-h5 text-center mb-2">
              {{ plan.displayName || plan.name }}
            </h5>
            <p class="text-body-2 text-center text-medium-emphasis mb-6">
              {{ plan.description || 'Professional subscription plan' }}
            </p>

            <!-- Pricing -->
            <div class="text-center mb-6">
              <div class="d-flex justify-center align-center">
                <span class="text-h6 text-medium-emphasis">{{
                  plan.currency || '€'
                }}</span>
                <span class="text-h4 mx-1 font-weight-bold">
                  {{ getDisplayPrice(plan) }}
                </span>
                <span class="text-body-2 text-medium-emphasis"
                  >/{{ getBillingPeriodText(plan) }}</span
                >
              </div>
              <!-- Show breakdown if more than 1 month -->
              <div v-if="plan.durationDays && plan.durationDays > 30" class="mt-2">
                <span class="text-caption text-medium-emphasis">
                  (€{{ getBaseMonthlyPrice(plan) }}/month × {{ Math.round(plan.durationDays / 30) }} months)
                </span>
              </div>
            </div>

            <!-- Job Advert Limits -->
            <VAlert
              type="success"
              variant="tonal"
              density="compact"
              class="mb-4"
            >
              <strong>Unlimited</strong> job advertisements
            </VAlert>
          </VCardText>

          <!-- Action Button -->
          <VCardActions class="pa-4 pt-0">
            <VBtn
              v-if="isCurrentPlan(plan)"
              block
              color="success"
              variant="tonal"
              disabled
            >
              <VIcon icon="mdi-check" class="mr-2" />
              Current Plan
            </VBtn>
            <VBtn
              v-else-if="plan.price === 0"
              block
              color="primary"
              variant="outlined"
              disabled
            >
              Free Plan
            </VBtn>
            <VBtn
              v-else
              block
              :color="plan.id === recommendedPlan?.id ? 'primary' : 'primary'"
              :variant="
                enablePayButton || isExpiringSort ? 'elevated' : 'outlined'
              "
              :loading="loadingCheckout && selectedPlanId === plan.id"
              :disabled="
                loadingCheckout || (hasUnlimitedSubscription && !isExpiringSort)
              "
              @click="handleSelectPlan(plan)"
            >
              <template v-if="hasUnlimitedSubscription && !isExpiringSort">
                <VIcon icon="mdi-lock" class="mr-2" />
                Already Subscribed
              </template>
              <template v-else>
                {{ isUpgrade(plan) ? 'Upgrade' : 'Select' }} Plan
              </template>
            </VBtn>
          </VCardActions>
        </VCard>
      </VCol>
    </VRow>

    <!-- Current Subscription Info -->
    <VAlert
      v-if="currentSubscriptionInfo"
      type="info"
      variant="tonal"
      class="mt-8"
    >
      <VAlertTitle>Current Subscription</VAlertTitle>
      <div class="mt-2">
        <p class="mb-1">
          <strong>Plan:</strong> {{ currentSubscriptionInfo.planName }}
        </p>
        <p class="mb-1">
          <strong>Status:</strong>
          <VChip
            :color="currentSubscriptionInfo.isActive ? 'success' : 'error'"
            size="x-small"
            label
          >
            {{ currentSubscriptionInfo.isActive ? 'Active' : 'Inactive' }}
          </VChip>
        </p>
        <p class="mb-1" v-if="currentSubscriptionInfo.currentPeriodEnd">
          <strong>{{
            currentSubscriptionInfo.cancelAtPeriodEnd
              ? 'Expires on:'
              : 'Renews on:'
          }}</strong>
          {{
            new Date(
              currentSubscriptionInfo.currentPeriodEnd,
            ).toLocaleDateString()
          }}
        </p>
        <p class="mb-0" v-if="currentSubscriptionInfo.cancelAtPeriodEnd">
          <VChip color="warning" size="x-small" label>
            Scheduled for cancellation
          </VChip>
        </p>
      </div>
    </VAlert>
  </VContainer>
</template>

<style scoped>
  .current-plan {
    position: relative;
    border: 2px solid rgb(var(--v-theme-success)) !important;
  }

  .current-plan::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(
      45deg,
      rgba(var(--v-theme-success), 0.1),
      rgba(var(--v-theme-success), 0.05)
    );
    pointer-events: none;
  }
</style>
