<script lang="ts" setup>
  interface Props {
    menuList?: unknown[]
    itemProps?: boolean
    moreIcon?: string
  }

  const props = defineProps<Props>()

  const emit = defineEmits(['item-clicked'])

  function handleItemClick(item: any) {
    emit('item-clicked', item)
  }
</script>

<template>
  <IconBtn
    color="primary--text"
    :icon="moreIcon ? moreIcon : 'tabler-dots-vertical'"
    density="compact"
    elevation="0"
  >
    <VIcon icon="tabler-dots-vertical" />
    <VMenu v-if="props.menuList" activator="parent">
      <v-list density="compact">
        <v-list-item
          v-for="(item, i) in props.menuList"
          :key="i"
          :value="item"
          :title="item.title"
          :to="item.to ? item.to : undefined"
          @click="() => (item.action ? handleItemClick(item) : undefined)"
        >
          <template v-slot:prepend>
            <v-icon :icon="item.prependIcon"></v-icon>
          </template>
        </v-list-item>
      </v-list>
    </VMenu>
  </IconBtn>
</template>
