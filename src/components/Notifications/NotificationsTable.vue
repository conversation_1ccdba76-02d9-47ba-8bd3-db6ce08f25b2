<script setup lang="ts">
  import { useNotifications } from '@/composables/Notifications/useNotifications'
  import { usePusher } from '@/composables/usePusher'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { paginationMeta } from '@/utils/utils'
  import { formatDateToString, timeAgo } from '@core/utils/formatters'
  import { useTheme } from 'vuetify'

  const searchQuery = ref('')
  const { colors } = useTheme().current.value

  const {
    state: {
      notificationsHeader,
      loadingUserNotifications,
      loadingMarkAsRead,
      loadingMarkAllAsRead,
    },
    actions: {
      refetchUserNotifications,
      handleMarkAllAsRead,
      handleMarkAsRead,
      initiateNotificationsListen,
      stopNotificationsListen,
      loadUserNotifications,
    },
  } = useNotifications()

  const authStore = useAuthStore()
  const appStore = useAppStore()

  const handleMarkAllRead = async () => {
    await handleMarkAllAsRead()
    await appStore.setAllNotificationsRead()
    await refetchUserNotifications()
  }

  const onMarkAsRead = async (notificationId: string) => {
    await handleMarkAsRead(notificationId)
    await appStore.setMarkedAsRead(notificationId)
  }

  const companyUserId = computed(() => authStore.claims.companyUserId)

  onMounted(async () => {
    await loadUserNotifications()
    initiateNotificationsListen()
  })

  onUnmounted(() => {
    stopNotificationsListen()
  })

  const itemsPerPage = ref(10)
  const page = ref(1)

  const totalAds: Ref<number> = computed(() =>
    appStore.notifications ? (appStore.notifications as any[]).length : 0,
  )

  const paginatedNotifications = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return appStore.notifications.slice(start, end)
  })
</script>

<template>
  <VCard elevation="20" :title="$t('Notifications')">
    <template v-slot:append>
      <v-btn prepend-icon="mdi-check" size="small" @click="handleMarkAllRead">
        Alles als gelesen markieren
      </v-btn>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Benachrichtigung suchen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>

    <VDataTable
      :loading="
        loadingUserNotifications || loadingMarkAsRead || loadingMarkAllAsRead
      "
      :headers="notificationsHeader"
      :items="paginatedNotifications"
      :item-value="item => item.id + '|' + item.title"
      class="px-2"
      hover
    >
      <template #no-data>
        <VCardText>
          <p>Keine Benachrichtigungen verfügbar</p>
        </VCardText>
      </template>
      <template #[`item.isNew`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-circle"
            v-if="item.isNew"
            color="primary"
            text="Ungelesen"
          />
          <VChip prepend-icon="tabler-eye" v-else color="grey" text="Gelesen" />
        </div>
      </template>

      <template #[`item.time`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          {{ timeAgo(item.time) }} ({{ formatDateToString(item.time) }})
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <VBtn
          :disabled="!item.isNew"
          append-icon="tabler-eye"
          :color="item.isNew ? 'primary' : 'secondary'"
          density="compact"
          @click="() => onMarkAsRead(item.id)"
          class="mx-1"
        >
          Als gelesen markieren
        </VBtn>
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalAds as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalAds / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalAds / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>

    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
