<script setup lang="ts">
  import { fairJobHeaders } from '@/composables/Fairs/fairTableHeaders'
  import { useAppStore } from '@/stores/appStore'
  import { paginationMeta } from '@/utils/utils'
  import { useFairJobs } from '@/composables/FairJobs/useFairJobs'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCompanyFairJob } from '@/composables/CompanyFairJobs/useCompanyFairJob'
  import { useFairStore } from '@/stores/fairs/fairStore'

  const searchQuery = ref('')

  const {
    actions: { handleDeleteCompanyFairJob },
  } = useCompanyFairJob()

  const {
    state: { allFairJobs, loadingAllFairJobs },
    actions: { handleDeleteFairJob, loadFairJobs },
  } = useFairJobs()

  onMounted(async () => {
    await loadFairJobs()
  })

  const appStore = useAppStore()
  const companyStore = useCompanyStore()
  const fairStore = useFairStore()

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)

  const filteredList = computed(() => {
    let filtered = allFairJobs.value || []

    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter((fair: { title: string }) =>
        fair.title.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalList = computed(() =>
    filteredList.value ? filteredList.value.length : 0,
  )

  const paginatedList = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredList.value.slice(start, end)
  })

  const removeFairJob = async (fairJob: { id: string }) => {
    console.log({ fairJob: fairJob.id })
    await handleDeleteFairJob(fairJob.id)
    appStore.showSnack('Job removed successfully')
  }

  const showAddFairJobDialog = () => {
    fairStore.toggleFairJobForm()
  }

  watch([searchQuery], () => {
    page.value = 1
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingAllFairJobs"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <AddFairJobDialog />
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Job Offers') }}
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
            @click="showAddFairJobDialog"
            size="small"
            variant="outlined"
            prepend-icon="mdi-plus"
          >
            {{ $t('Add') }}
          </VBtn>
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Suche nach Stellenangebot"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>

    <VDivider />
    <VDataTable
      :headers="fairJobHeaders"
      :items="paginatedList"
      :loading="loadingAllFairJobs"
      class="px-2"
      :search="searchQuery"
      hover
    >
      <template #no-data>
        <VCardText>
          <p>Keine unternehmensgerechten Jobs verfügbar</p>
        </VCardText>
      </template>
      <template #[`item.logoImageUrl`]="{ item }">
        <div class="ma-4">
          <v-avatar :image="item.logoImageUrl" size="60"></v-avatar>
        </div>
      </template>

      <!--      <template #[`item.actions`]="{ item }">-->
      <!--        <div class="d-flex justify-end">-->
      <!--          <VBtn size="small" @click="removeFairJob(item)">-->
      <!--            {{ $t('Remove') }}-->
      <!--          </VBtn>-->
      <!--        </div>-->
      <!--      </template>-->

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalList as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalFairs / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalList / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
