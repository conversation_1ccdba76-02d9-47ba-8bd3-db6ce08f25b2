<script setup lang="ts">
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useFairJobs } from '@/composables/FairJobs/useFairJobs'

  interface FairJob {
    id: string
    title: string
  }

  const {
    state: { loadingCreateFairJob },
    actions: { handleCreateFairJob, loadFairJobs },
  } = useFairJobs()

  const fairStore = useFairStore()

  const fairJob = ref<FairJob>({
    id: '',
    title: '',
  })

  const resetForm = () => {
    fairStore.toggleFairJobForm()
    fairJob.value = { id: '', title: '' }
  }

  const onFormSubmit = async () => {
    await handleCreateFairJob(fairJob.value.title)
    await loadFairJobs()
    resetForm()
  }

  const dialogModelValueUpdate = (val: boolean) => {
    fairStore.toggleFairJobForm()
  }
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 650"
    :model-value="fairStore.showFairJobForm"
    @update:model-value="dialogModelValueUpdate"
  >
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard class="pa-sm-9 pa-5">
      <VCardItem>
        <VCardTitle class="text-h5 text-center mb-3">
          Add New Fair Job
        </VCardTitle>
        <p class="text-center">Add a new job to the fair</p>
      </VCardItem>

      <VCardText>
        <VForm @submit.prevent="onFormSubmit">
          <VRow>
            <VCol cols="12">
              <VTextField v-model="fairJob.title" label="Job Title" />
            </VCol>

            <VCol cols="12" class="text-center">
              <VBtn
                :loading="loadingCreateFairJob"
                :disabled="loadingCreateFairJob"
                type="submit"
                class="me-3"
              >
                Submit
              </VBtn>

              <VBtn variant="tonal" color="secondary" @click="resetForm">
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
