<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { useAppStore } from '@/stores/appStore'
  import PlacesAutocomplete from '@/components/PlacesAutocomplete.vue'
  import {
    dateValidator,
    emailValidator,
    requiredValidator,
    dateRangeValidator,
    registrationDateValidator,
  } from '@validators'
  import { formatDateToString } from '@core/utils/formatters'
  import { useRouter } from 'vue-router'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'
  import { useFairs } from '@/composables/Fairs/useFairs'
  import ImageCropperResult from '@/types/image-cropper-result'
  import { useFairStore } from '@/stores/fairs/fairStore'
  import dayjs from 'dayjs'

  const props = defineProps({
    isNew: {
      type: Boolean,
      required: false,
      default: false,
    },
  })

  const router = useRouter()
  const formRef = ref()
  const appStore = useAppStore()
  const fairFormStore = useFairFormStore()
  const fairStore = useFairStore()

  const fairFormData = computed(() => {
    return fairFormStore
  })

  const {
    state: { loadingCurrentFair: loadingSingleFair, loadingCreateFair },
    actions: { handleCreateFair, handleCloneFair, handleUpdateFair },
  } = useFairs()

  const startDate = computed(() => fairFormStore.startDate)
  const endDate = computed(() => fairFormStore.endDate)
  const registrationStartDate = computed(
    () => fairFormStore.registrationStartDate,
  )
  const registrationEndDate = computed(() => fairFormStore.registrationEndDate)

  const logoKey = ref(fairFormStore.logoSavedUrl)

  onMounted(() => {
    if (!props.isNew) {
    } else {
      fairFormStore.resetForm()
    }
  })

  const submittingForm = ref(false)

  const clearForm = () => {
    fairFormStore.resetForm()
  }

  const handleFairSubmission = async () => {
    if (props.isNew && fairFormStore.id) {
      await handleCloneFair()
    } else {
      await handleCreateFair()
      appStore.showSnack('Messe erfolgreich erstellt')
    }
  }

  const updateFair = async () => {
    const { valid } = await formRef.value.validate()

    if (!valid) {
      appStore.showSnack('Please fix the validation errors before submitting.')
      return
    }

    submittingForm.value = true

    try {
      await handleUpdateFair()
    } catch (e) {
      console.error('Error during form submission:')
      appStore.showSnack('An error occurred while submitting the form.')
    } finally {
      submittingForm.value = false
      await router.push({ name: 'fair-fairs' })
      appStore.showSnack('Fair updated successfully')
    }
  }

  const submitForm = async () => {
    const { valid } = await formRef.value.validate()

    if (!valid) {
      appStore.showSnack('Please fix the validation errors before submitting.')
      return
    }

    submittingForm.value = true

    try {
      await handleFairSubmission()
      clearForm()
      await router.push({ name: 'fair-fairs' })
    } catch (error: any) {
      console.error('Error during form submission:', error.message)
      appStore.showSnack(
        error.message || 'An error occurred while submitting the form.',
      )
    } finally {
      submittingForm.value = false
    }
  }

  const showFairList = () => {
    fairFormStore.toggleFairCloneList()
  }
  const showFairForm = () => {
    fairStore.toggleFairForm()
  }

  const calculateDaysInRange = () => {
    if (!startDate.value || !endDate.value) return

    let start = dayjs(startDate.value)
    const end = dayjs(endDate.value)
    const days = []

    // Create date objects for 9am and 5pm in local timezone
    const localNineAM = dayjs().hour(9).minute(0).second(0)
    const localFivePM = dayjs().hour(17).minute(0).second(0)

    // Format times in HH:MM format in local timezone
    const localStartTime = localNineAM.format('HH:mm')
    const localEndTime = localFivePM.format('HH:mm')

    while (start.isBefore(end) || start.isSame(end)) {
      days.push({
        day: start.format('YYYY-MM-DD'),
        startTime: localStartTime,
        endTime: localEndTime,
      })
      start = start.add(1, 'day')
    }

    fairFormStore.updateFairDays(days)
  }

  watch([startDate, endDate], () => {
    calculateDaysInRange()
  })
</script>

<template>
  <VCard class="elevation-0" v-if="loadingSingleFair">
    <v-skeleton-loader
      class="mx-auto"
      type="table-heading, list-item-two-line, image"
    ></v-skeleton-loader>
  </VCard>

  <VCard
    v-else
    :disabled="submittingForm"
    :loading="submittingForm"
    :flat="true"
    :title="props.isNew ? 'Create New Fair' : 'Edit Fair'"
  >
    <template v-slot:title>
      <v-row class="px-2 mb-1">
        <v-col cols="6">
          <v-icon>tabler-planet</v-icon>
          {{ $t('Fairs') }}
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
            v-if="props.isNew"
            color="primary"
            variant="outlined"
            @click="showFairList"
          >
            <v-icon class="mr-1" icon="tabler-plus"></v-icon>
            {{
              fairFormData.showFairCloneList
                ? $t('Hide List')
                : $t('Create From Existing')
            }}
          </VBtn>
          <VBtn v-else color="primary" variant="outlined" @click="showFairForm">
            <v-icon class="mr-1" icon="tabler-plus"></v-icon>
            {{ $t('Hide') }}
          </VBtn>
        </v-col>
      </v-row>
      <v-divider></v-divider>
    </template>
    <VForm ref="formRef" class="mx-6">
      <VRow>
        <VCol cols="6">
          <VRow>
            <VCol cols="12">
              <VTextField
                v-model="fairFormData.name"
                :label="$t('Fair Name')"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <PlacesAutocomplete
                :key="fairFormData.location"
                :label="$t('Location')"
                :address="fairFormData.location"
                @place-changed="
                  (place: any) => {
                    fairFormData.city = place.city
                    fairFormData.location = place.address
                  }
                "
              />
            </VCol>

            <VCol cols="12">
              <VTextField
                v-model="fairFormData.locationName"
                :label="$t('Location Name')"
              />
            </VCol>

            <VCol cols="12">
              <VTextField
                v-model="fairFormData.contactPersonName"
                :label="$t('Contact Person Name')"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <VTextField
                v-model="fairFormData.contactPersonEmail"
                :label="$t('Contact Person Email')"
                :rules="[requiredValidator, emailValidator]"
              />
            </VCol>
            <VCol cols="12">
              <VTextField
                v-model="fairFormData.publisherName"
                :label="$t('Publisher Name')"
              />
            </VCol>
          </VRow>
        </VCol>

        <VCol cols="6">
          <VRow>
            <VCol cols="6">
              <VTextField
                :key="fairFormData.startDate"
                :value="formatDateToString(fairFormData.startDate)"
                type="date"
                :rules="[dateValidator]"
                :label="$t('Start Date')"
                @update:model-value="
                  (val: string) => (fairFormData.startDate = new Date(val))
                "
              />
            </VCol>

            <VCol cols="6">
              <VTextField
                :key="fairFormStore.endDate"
                :value="formatDateToString(fairFormData.endDate)"
                type="date"
                :rules="[dateValidator]"
                :label="$t('End Date')"
                @update:model-value="
                  (val: string) => (fairFormData.endDate = new Date(val))
                "
              />
            </VCol>

            <VCol cols="6">
              <VTextField
                :value="formatDateToString(fairFormData.registrationStartDate)"
                type="date"
                :rules="[
                  dateValidator,
                  dateRangeValidator(
                    registrationStartDate,
                    registrationEndDate,
                    'Registrierungsbeginn muss vor Registrierungsende liegen',
                  ),
                ]"
                :label="$t('Registration Start')"
                @update:model-value="
                  (val: string) =>
                    (fairFormData.registrationStartDate = new Date(val))
                "
              />
            </VCol>

            <VCol cols="6">
              <VTextField
                :value="formatDateToString(fairFormData.registrationEndDate)"
                type="date"
                :rules="[
                  dateValidator,
                  registrationDateValidator(
                    registrationEndDate,
                    startDate,
                    'Das Registrierungsende muss vor dem Startdatum liegen',
                  ),
                ]"
                :label="$t('Registration End')"
                @update:model-value="
                  (val: string) =>
                    (fairFormData.registrationEndDate = new Date(val))
                "
              />
            </VCol>
            <VCol cols="12">
              <ImageInputCrop
                :key="logoKey"
                :title="$t('Fair Logo')"
                :image-url="fairFormData.logoSavedUrl"
                :min-aspect-ratio="0.5"
                :max-aspect-ratio="10"
                @image-cropped="
                  (result: ImageCropperResult) =>
                    fairFormData.handleImageChange(result, 'logo')
                "
              />
            </VCol>

            <VCol cols="12">
              <ImageInputCrop
                :key="fairFormData.publisherLogoSavedUrl"
                :min-aspect-ratio="0.5"
                :max-aspect-ratio="10"
                :title="$t('Publisher Logo')"
                :image-url="fairFormData.publisherLogoSavedUrl"
                @image-cropped="
                  (result: ImageCropperResult) =>
                    fairFormData.handleImageChange(result, 'publisherLogo')
                "
              />
            </VCol>

            <VCol cols="12">
              <VTextarea
                v-model="fairFormData.description"
                :label="Description"
                rows="4"
              />
            </VCol>
          </VRow>
        </VCol>
      </VRow>

      <!-- Dynamic Time Pickers Section -->
      <VRow v-if="fairFormData.fairDays.length > 0">
        <VCol cols="12">
          <VCard class="elevation-1">
            <VCardTitle class="bg-tertiary text-white pa-4">
              <v-icon start>mdi-clock-outline</v-icon>
              Messezeit
            </VCardTitle>
            <v-divider></v-divider>
            <VCardText>
              <VRow v-for="(day, index) in fairFormData.fairDays" :key="index">
                <VCol cols="12" md="4">
                  <VTextField :model-value="day.day" label="Date" readonly />
                </VCol>
                <VCol cols="12" md="4">
                  <VTextField
                    :value="day.startTime"
                    v-model="day.startTime"
                    label="Start Time"
                    type="time"
                  />
                </VCol>
                <VCol cols="12" md="4">
                  <VTextField
                    v-model="day.endTime"
                    label="End Time"
                    type="time"
                  />
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VForm>

    <v-card-actions class="mt-8">
      <VBtn color="primary" variant="text" @click="router.go(-1)">
        <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
        Back
      </VBtn>

      <VBtn @click="clearForm" color="grey" variant="text">
        <v-icon class="mr-1" icon="tabler-square-rounded-x-filled"></v-icon>
        Clear Fields
      </VBtn>

      <v-spacer></v-spacer>

      <VBtn
        v-if="props.isNew"
        :loading="loadingCreateFair || submittingForm"
        :disabled="loadingCreateFair || !fairFormStore.isValid"
        @click="submitForm"
        color="success"
        variant="flat"
      >
        {{ $t('Create Fair') }}
      </VBtn>
      <VBtn
        v-else
        :loading="loadingCreateFair || submittingForm"
        :disabled="loadingCreateFair || !fairFormStore.isValid"
        @click="updateFair"
        color="success"
        variant="flat"
      >
        {{ $t('Update Fair') }}
      </VBtn>
    </v-card-actions>
  </VCard>
</template>
