<script setup lang="ts">
  import { ref } from 'vue'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'
  import { useFairs } from '@/composables/Fairs/useFairs'
  import { formatDateToString } from '@core/utils/formatters'

  const fairFormStore = useFairFormStore()
  const showSearch = ref(true)
  const searchQuery = ref('')

  const closeCloneList = () => {
    fairFormStore.toggleFairCloneList()
  }

  const {
    state: { allFairsList, loadingAllFairs },
    actions: { loadFairs },
  } = useFairs()

  onMounted(async () => {
    await loadFairs()
  })

  const toggleSearch = () => {
    showSearch.value = !showSearch.value
    if (!showSearch.value) {
      searchQuery.value = '' // Clear search query when hiding the search bar
    }
  }

  const searchFairList = computed(() => {
    return allFairsList.value.filter((fair: { name: string }) => {
      return fair.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    })
  })
</script>

<template>
  <v-card class="mx-auto" max-width="400">
    <v-layout>
      <v-app-bar color="pink">
        <v-btn @click="closeCloneList" icon="mdi-close"></v-btn>
        <v-toolbar-title>{{ $t('Fairs') }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn @click="toggleSearch" icon="mdi-magnify"></v-btn>
      </v-app-bar>

      <v-main>
        <v-container>
          <v-row v-if="showSearch">
            <v-col cols="12">
              <v-text-field
                v-model="searchQuery"
                label="Search Fairs"
                single-line
                hide-details
                clearable
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- Fair List -->
          <v-row dense class="scrollable-container">
            <v-col
              cols="12"
              v-for="(fair, index) in searchFairList"
              :key="index"
            >
              <v-card class="pa-2" :loading="loadingAllFairs">
                <div class="d-flex flex-no-wrap justify-space-between">
                  <div>
                    <v-card-title
                      :class="
                        fair.id === fairFormStore.id
                          ? 'text-h6 text-primary'
                          : 'text-h6'
                      "
                    >
                      {{
                        fair.name.length > 20
                          ? fair.name.slice(0, 20) + '...'
                          : fair.name
                      }}
                    </v-card-title>
                    <v-card-subtitle>
                      <v-chip
                        size="small"
                        prepend-icon="tabler-calendar-event"
                        @click="lights"
                      >
                        {{ formatDateToString(fair.startDate) }}
                      </v-chip>
                    </v-card-subtitle>
                  </div>
                  <v-avatar :image="fair.logoImageUrl" size="80"></v-avatar>
                </div>
                <v-divider class="my-1"></v-divider>

                <v-list-item
                  prepend-icon="mdi-chevron-left"
                  :subtitle="$t('Use This Fair')"
                  link
                  @click="() => fairFormStore.setFair(fair)"
                ></v-list-item>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-main>
    </v-layout>
  </v-card>
</template>

<style scoped lang="scss">
  .scrollable-container {
    max-height: 650px; /* Adjust height as needed */
    overflow-y: auto;
    padding-right: 8px; /* Add padding to avoid scrollbar overlap */

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e32753;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
</style>
