<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useFairJobs } from '@/composables/FairJobs/useFairJobs'
  import { useCompanyFairJob } from '@/composables/CompanyFairJobs/useCompanyFairJob'
  import { useAppStore } from '@/stores/appStore'

  const companyStore = useCompanyStore()
  const appStore = useAppStore()
  const {
    state: { allFairJobs, loadingAllFairJobs },
    actions: { loadFairJobs },
  } = useFairJobs()

  onMounted(async () => {
    await loadFairJobs()
  })

  const {
    actions: { handleCreateCompanyFairJob },
  } = useCompanyFairJob()

  const showSearch = ref(true)
  const searchQuery = ref('')

  // Toggle search bar visibility
  const toggleSearch = () => {
    showSearch.value = !showSearch.value
    if (!showSearch.value) {
      searchQuery.value = ''
    }
  }

  const closeList = () => {
    companyStore.setFairJobsListView(false)
  }

  const filteredFairJobs = computed(() => {
    return allFairJobs.value.filter((fair: { title: string }) =>
      fair.title.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  })

  const addFairJob = (fairJob: { id: string }) => {
    console.log({ fairJob })
    handleCreateCompanyFairJob({ fairJobId: fairJob?.id })
    appStore.showSnack('Fairer Job im Unternehmen hinzugefügt')
  }
</script>

<template>
  <v-card class="mx-auto" max-width="500">
    <v-layout>
      <!-- App Bar -->
      <v-app-bar color="pink">
        <v-btn @click="closeList" icon="mdi-close" aria-label="Close"></v-btn>
        <v-toolbar-title>{{ $t('Fair Jobs') }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn
          @click="toggleSearch"
          icon="mdi-magnify"
          aria-label="Search"
        ></v-btn>
      </v-app-bar>

      <!-- Main Content -->
      <v-main>
        <v-container>
          <!-- Search Bar -->
          <v-row v-if="showSearch">
            <v-col cols="12">
              <v-text-field
                v-model="searchQuery"
                :label="$t('Search Jobs')"
                single-line
                hide-details
                clearable
                aria-label="Search Jobs"
              ></v-text-field>
            </v-col>
          </v-row>

          <v-row dense class="scrollable-container">
            <v-col v-if="loadingAllFairJobs" cols="12">
              <v-skeleton-loader type="card"></v-skeleton-loader>
            </v-col>
            <v-col v-else-if="filteredFairJobs.length === 0" cols="12">
              <v-alert color="primary" class="text-center">
                {{ $t('No jobs found.') }}
              </v-alert>
            </v-col>
            <v-col
              v-for="(fair, index) in filteredFairJobs"
              :key="index"
              cols="12"
            >
              <v-card
                :disabled="fair.isInCompanyFairJobs"
                class="pa-2 list"
                :loading="loadingAllFairJobs"
                hover
              >
                <div class="d-flex flex-no-wrap justify-space-between">
                  <div>
                    <v-card-title class="text-h6">
                      <v-icon class="mr-2" size="small"
                        >tabler-briefcase
                      </v-icon>
                      {{
                        fair.title.length > 30
                          ? fair.title.slice(0, 30) + '...'
                          : fair.title
                      }}
                    </v-card-title>
                  </div>
                </div>
                <v-divider
                  v-if="!fair.isInCompanyFairJobs"
                  class="my-1"
                ></v-divider>

                <v-list-item
                  v-if="!fair.isInCompanyFairJobs"
                  prepend-icon="mdi-plus"
                  @click="addFairJob(fair)"
                  :subtitle="$t('Add this fair job to the company fair')"
                  link
                ></v-list-item>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-main>
    </v-layout>
  </v-card>
</template>

<style scoped lang="scss">
  .scrollable-container {
    max-height: 650px; /* Adjust height as needed */
    overflow-y: auto;
    padding-right: 8px; /* Add padding to avoid scrollbar overlap */

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e32753;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }

  .v-card .list {
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
</style>
