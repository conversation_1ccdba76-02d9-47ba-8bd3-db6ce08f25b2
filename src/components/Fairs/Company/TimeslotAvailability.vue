<template>
  <div>
    <v-menu v-model="menu" :close-on-content-click="false" location="bottom">
      <template v-slot:activator="{ props }">
        <div class="d-flex align-center">
          <v-icon
            v-bind="props"
            :icon="
              selectedAvailability === 'Available'
                ? 'tabler-door'
                : 'tabler-door-off'
            "
            :color="
              selectedAvailability === 'Available' ? 'success' : 'primary'
            "
          >
          </v-icon>
          <v-tooltip activator="parent" location="top"
            >Change Availability</v-tooltip
          >
        </div>
      </template>

      <v-list>
        <v-list-item
          v-for="(item, index) in availabilityOptions"
          :key="index"
          :value="item"
          @click="selectAvailability(item)"
        >
          <v-list-item-title>{{ item }}</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script setup>
  import { ref, watch, onMounted } from 'vue'

  const props = defineProps({
    modelValue: {
      type: String,
      default: 'Available',
    },
    availabilityOptions: {
      type: Array,
      default: () => ['Available', 'Occupied'],
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const menu = ref(false)
  const selectedAvailability = ref(props.modelValue)

  // Watch for changes in the prop
  watch(
    () => props.modelValue,
    newValue => {
      selectedAvailability.value = newValue
    },
  )

  onMounted(() => {
    selectedAvailability.value = props.modelValue
  })

  const selectAvailability = item => {
    selectedAvailability.value = item
    emit('update:modelValue', item)
    menu.value = false
  }
</script>
