<script setup lang="ts">
  import { companyFairJobHeaders } from '@/composables/Fairs/fairTableHeaders'
  import { useAppStore } from '@/stores/appStore'
  import { paginationMeta } from '@/utils/utils'
  import { useFairJobs } from '@/composables/FairJobs/useFairJobs'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCompanyFairJob } from '@/composables/CompanyFairJobs/useCompanyFairJob'

  const searchQuery = ref('')

  const {
    state: { companyFairJobsInFairList, loadingFairJobs },
    actions: { loadContactPersonsAndJobsInFair },
  } = useFairJobs()

  onMounted(async () => {
    await loadContactPersonsAndJobsInFair()
  })

  const {
    state: { loadingCompanyDeleteFairJob },
    actions: { handleDeleteCompanyFairJob },
  } = useCompanyFairJob()

  const appStore = useAppStore()
  const companyStore = useCompanyStore()

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)

  const filteredList = computed(() => {
    let filtered = companyFairJobsInFairList.value || []

    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter((fair: { title: string }) =>
        fair.title.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalList = computed(() =>
    filteredList.value ? filteredList.value.length : 0,
  )

  const paginatedList = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredList.value.slice(start, end)
  })

  const toggleList = () => {
    if (companyStore.showFairJobsList) {
      companyStore.setFairJobsListView(false)
    } else {
      companyStore.setFairJobsListView(true)
      companyStore.setContactPersonsListView(false)
      companyStore.setContactPersonTimeSlotsView(false)
    }
  }

  const openFairJobEditDialog = (editData: any) => {
    companyStore.updateFairJobEditDialogData(editData)
    companyStore.setFairJobEditDialog(true)
  }

  const removeFairJob = async (fairJob: { id: string }) => {
    await handleDeleteCompanyFairJob(fairJob.id)
  }

  watch([searchQuery], () => {
    page.value = 1
  })
</script>

<template>
  <CompanyFairJobDialog />
  <v-skeleton-loader
    v-if="loadingFairJobs"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Job Offers') }}
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
            @click="toggleList"
            size="small"
            variant="outlined"
            prepend-icon="mdi-plus"
          >
            {{ companyStore.showFairJobsList ? $t('Hide') : $t('Add') }}
          </VBtn>
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Suche nach Stellenangebot"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>
    <VDivider />
    <VDataTable
      :headers="companyFairJobHeaders"
      :items="paginatedList"
      :loading="loadingFairJobs || loadingCompanyDeleteFairJob"
      class="px-2"
      :search="searchQuery"
      hover
    >
      <template #no-data>
        <VCardText>
          <p>Keine unternehmensgerechten Jobs verfügbar</p>
        </VCardText>
      </template>
      <template #[`item.logoImageUrl`]="{ item }">
        <div class="ma-4">
          <v-avatar :image="item.logoImageUrl" size="60"></v-avatar>
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <div class="d-flex justify-end">
          <v-btn
            @click="openFairJobEditDialog(item)"
            class="mr-2"
            size="x-small"
            color="primary"
            icon="mdi-pencil"
            variant="tonal"
          >
            <v-icon>mdi-pencil</v-icon>
            <v-tooltip activator="parent" location="top">Edit</v-tooltip>
          </v-btn>
          <v-btn
            @click="removeFairJob(item)"
            size="x-small"
            color="primary"
            icon="mdi-minus"
            variant="tonal"
          >
            <v-icon>mdi-minus</v-icon>
            <v-tooltip activator="parent" location="top">Entfernen</v-tooltip>
          </v-btn>
        </div>
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalList as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalFairs / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalList / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
