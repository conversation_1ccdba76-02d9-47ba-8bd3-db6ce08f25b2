<script setup lang="ts">
  import { useAuthStore } from '@/stores/authStore'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useCompanyStore } from '@/stores/companyStore'
  import { ref, computed } from 'vue'

  const authStore = useAuthStore()
  const {
    actions: { manageFair },
    state: { loadingManageFair },
  } = useCompanyGraph()

  const companyStore = useCompanyStore()
  const confirmDialog = ref(false)
  const isSwitchingToReceiveNotifications = ref<boolean | null>(null)

  const receiveNotifications = computed(
    () => !companyStore.company?.isFairManaged,
  )

  const handleSwitch = (manage: boolean) => {
    isSwitchingToReceiveNotifications.value = manage
    confirmDialog.value = true
  }

  const onConfirmSwitch = async () => {
    if (isSwitchingToReceiveNotifications.value !== null && authStore.companyId) {
      await manageFair({
        companyId: authStore.companyId,
        isFairManaged: !isSwitchingToReceiveNotifications.value,
      })
      companyStore.updateCompanyManagedStatus(
        !isSwitchingToReceiveNotifications.value,
      )
      confirmDialog.value = false
      isSwitchingToReceiveNotifications.value = null
    } else {
      console.error('Company ID not found or invalid manage value.')
    }
  }

  const onCloseDialog = () => {
    confirmDialog.value = false
    isSwitchingToReceiveNotifications.value = null
  }
</script>

<template>
  <div>
    <!-- Main notification strip -->
    <v-alert
      :color="receiveNotifications ? 'success' : 'info'"
      variant="tonal"
      border="start"
      class="mb-4"
    >
      <template v-slot:prepend>
        <v-icon
          :icon="receiveNotifications ? 'mdi-bell-check' : 'mdi-bell-outline'"
          size="24"
        />
      </template>

      <template v-slot:title>
        <div class="d-flex justify-space-between">
          <div>
            <span class="text-h6">Benachrichtigungen für Terminanfragen</span>
            <v-spacer />
          </div>
        </div>
      </template>

      <template v-slot:text>
        <div class="d-flex align-center justify-space-between">
          <div class="flex-grow-1">
            <p class="mb-1">
              {{
                receiveNotifications
                  ? 'Sie erhalten derzeit Benachrichtigungen über neue Terminanfragen.'
                  : 'Aktivieren Sie Benachrichtigungen, um über neue Terminanfragen informiert zu werden.'
              }}
            </p>
            <div class="d-flex align-center">
              <v-chip
                :color="receiveNotifications ? 'success' : 'warning'"
                size="small"
                variant="flat"
              >
                <v-icon
                  :icon="
                    receiveNotifications
                      ? 'mdi-check-circle'
                      : 'mdi-alert-circle'
                  "
                  size="16"
                  class="mr-1"
                />
                {{ receiveNotifications ? 'Aktiviert' : 'Deaktiviert' }}
              </v-chip>
              <v-switch
                :model-value="receiveNotifications"
                :loading="loadingManageFair"
                :disabled="loadingManageFair"
                color="primary"
                hide-details
                inset
                @update:model-value="handleSwitch"
                class="ml-6"
              />
            </div>
          </div>
        </div>
      </template>
    </v-alert>

    <!-- Confirmation dialog -->
    <v-dialog v-model="confirmDialog" persistent max-width="500">
      <v-card class="pa-2">
        <v-card-title class="d-flex align-center">
          <v-icon
            :icon="
              isSwitchingToReceiveNotifications
                ? 'mdi-bell-check'
                : 'mdi-bell-off'
            "
            :color="
              isSwitchingToReceiveNotifications ? 'success' : 'warning'
            "
            size="28"
            class="mr-3"
          />
          <span class="text-h5"
            >Benachrichtigungen
            {{
              isSwitchingToReceiveNotifications ? 'aktivieren' : 'deaktivieren'
            }}</span
          >
        </v-card-title>

        <v-card-text class="pt-4">
          <p class="text-body-1 mb-3">
            Sind Sie sicher, dass Sie die Benachrichtigungen für neue
            Terminanfragen
            <strong
              >{{
                isSwitchingToReceiveNotifications
                  ? 'aktivieren'
                  : 'deaktivieren'
              }}</strong
            >
            möchten?
          </p>

          <v-alert
            :color="isSwitchingToReceiveNotifications ? 'success' : 'warning'"
            variant="tonal"
            density="compact"
          >
            <template v-slot:prepend>
              <v-icon
                :icon="
                  isSwitchingToReceiveNotifications
                    ? 'mdi-information'
                    : 'mdi-alert'
                "
                size="20"
              />
            </template>
            <span class="text-body-2">
              {{
                isSwitchingToReceiveNotifications
                  ? 'Sie erhalten ab sofort E-Mail-Benachrichtigungen bei neuen Terminanfragen.'
                  : 'Sie erhalten keine E-Mail-Benachrichtigungen mehr bei neuen Terminanfragen.'
              }}
            </span>
          </v-alert>
        </v-card-text>

        <v-card-actions class="px-6 pb-4">
          <v-spacer />
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="onCloseDialog"
            :disabled="loadingManageFair"
          >
            Abbrechen
          </v-btn>
          <v-btn
            :color="isSwitchingToReceiveNotifications ? 'success' : 'warning'"
            :loading="loadingManageFair"
            :disabled="loadingManageFair"
            @click="onConfirmSwitch"
            variant="elevated"
          >
            {{
              isSwitchingToReceiveNotifications ? 'Aktivieren' : 'Deaktivieren'
            }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped lang="scss">
  .v-alert {
    border-radius: 8px;

    .v-switch {
      flex: 0 0 auto;
    }
  }

  .v-dialog .v-card {
    border-radius: 12px;
  }
</style>