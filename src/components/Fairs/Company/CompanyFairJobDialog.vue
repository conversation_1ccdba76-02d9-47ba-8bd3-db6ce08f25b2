<script setup lang="ts">
  import { useFairJobs } from '@/composables/FairJobs/useFairJobs'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCompanyFairJob } from '@/composables/CompanyFairJobs/useCompanyFairJob'

  const {
    state: { loadingCompanyUpdateFairJob },
    actions: { handleUpdateCompanyFairJob },
  } = useCompanyFairJob()

  const {
    actions: { loadFairJobs },
  } = useFairJobs()

  const companyStore = useCompanyStore()

  const resetForm = () => {
    companyStore.setFairJobEditDialog(false)
    companyStore.resetCompanyFairJob()
  }

  const onFormSubmit = async () => {
    await handleUpdateCompanyFairJob()
    companyStore.resetCompanyFairJob()
    await loadFairJobs()
  }

  const dialogModelValueUpdate = (val: boolean) => {
    companyStore.setFairJobEditDialog(val)
  }
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 650"
    :model-value="companyStore.showCompanyFairJobEditDialog"
    @update:model-value="dialogModelValueUpdate"
  >
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard class="pa-sm-9 pa-5">
      <VCardItem>
        <VCardTitle class="text-h5 text-center mb-8">
          Edit Company Fair Job
        </VCardTitle>
        <p class="text-center font-weight-bold text-h6">
          <v-icon>tabler-briefcase</v-icon>
          {{ companyStore.companyFairJobEditDialogData?.title }}
        </p>
      </VCardItem>

      <VCardText>
        <VForm @submit.prevent="onFormSubmit">
          <VRow>
            <VCol cols="12">
              <VTextarea
                v-model="companyStore.companyFairJobEditDialogData.description"
                label="Job Description"
              />
            </VCol>

            <VCol cols="12" class="text-center">
              <VBtn
                :loading="loadingCompanyUpdateFairJob"
                :disabled="loadingCompanyUpdateFairJob"
                type="submit"
                class="me-3"
              >
                Submit
              </VBtn>

              <VBtn variant="tonal" color="secondary" @click="resetForm">
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
