<script setup lang="ts">
  import { contactPersonHeaders } from '@/composables/Fairs/fairTableHeaders'
  import { useAppStore } from '@/stores/appStore'
  import { paginationMeta } from '@/utils/utils'
  import { useContactPersons } from '@/composables/ContactPersons/useContactPersons'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCompanyFairContactPerson } from '@/composables/CompanyFairContactPersons/useCompanyFairContactPerson'
  import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'

  const searchQuery = ref('')
  const selectedStatus = ref()

  const {
    state: { companyContactPersonsInFairList, loadingContactPersons },
    actions: { loadContactPersonByCompanyId },
  } = useContactPersons()

  onMounted(async () => {
    await loadContactPersonByCompanyId()
  })

  const {
    actions: { handleDeleteCompanyFairContactPerson },
  } = useCompanyFairContactPerson()

  const appStore = useAppStore()
  const companyStore = useCompanyStore()
  const contactPersonStore = useContactPersonStore()

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)

  const filteredList = computed(() => {
    let filtered = companyContactPersonsInFairList.value || []

    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter((fair: { name: string }) =>
        fair.name.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalList = computed(() =>
    filteredList.value ? filteredList.value.length : 0,
  )

  const paginatedList = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredList.value.slice(start, end)
  })

  const toggleList = () => {
    if (companyStore.showContactPersonsList) {
      companyStore.setContactPersonsListView(false)
      companyStore.setContactPersonTimeSlotsView(false)
    } else {
      companyStore.setContactPersonsListView(true)
      companyStore.setFairJobsListView(false)
      companyStore.setContactPersonTimeSlotsView(false)
    }
  }

  const showContactPersonTimeslots = (e: Event, row: any) => {
    console.log(row)
    const timeslots = row.contactPersonTimeslots
    contactPersonStore.updateContactPersonTimeslots(timeslots, row.fairDays)
    contactPersonStore.setContactPersonName(
      row.contactPersonName,
      row.companyFairContactPersonId,
    )
    companyStore.setContactPersonsListView(false)
    companyStore.setFairJobsListView(false)
    companyStore.setContactPersonTimeSlotsView(true)
  }

  const removeContactPerson = async (person: any) => {
    await handleDeleteCompanyFairContactPerson(
      person.companyFairContactPersonId,
    )
    appStore.showSnack('Contact person removed from the company')
  }

  watch([searchQuery, selectedStatus], () => {
    page.value = 1
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingContactPersons"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Contact Persons') }}
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
            @click="toggleList"
            size="small"
            variant="outlined"
            prepend-icon="mdi-plus"
          >
            {{ companyStore.showContactPersonsList ? $t('Hide') : $t('Add') }}
          </VBtn>
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Suche nach Ansprechpartner"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>

    <VDivider />
    <VDataTable
      :headers="contactPersonHeaders"
      :items="paginatedList"
      :loading="loadingContactPersons"
      class="px-2"
      :search="searchQuery"
      hover
      @click:row="
        (event: MouseEvent, value: any) =>
          showContactPersonTimeslots(event, value.item)
      "
    >
      <!--      No item template below-->
      <template #no-data>
        <VCardText>
          <p>Keine Kontaktpersonen hinzugefügt</p>
        </VCardText>
      </template>
      <template #[`item.logoImageUrl`]="{ item }">
        <div class="ma-4">
          <v-avatar :image="item.logoImageUrl" size="60"></v-avatar>
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <v-btn
          @click="removeContactPerson(item)"
          size="x-small"
          color="primary"
          icon="mdi-minus"
          variant="tonal"
        >
          <v-icon>mdi-minus</v-icon>
          <v-tooltip activator="parent" location="top">Entfernen</v-tooltip>
        </v-btn>
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalList as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalFairs / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalList / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
