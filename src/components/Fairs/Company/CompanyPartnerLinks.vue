<script setup lang="ts">
  import { usePartnerLink } from '@/composables/PartnerLink/usePartnerLink'
  import { useCompanyFairParticipation } from '@/composables/CompanyFairParticipation/useCompanyFairParticipation'
  import { onMounted } from 'vue'
  import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

  const {
    state: { companyPartnerLinks, fairParticipationByFairAndCompany },
    actions: { loadContactPersonsAndJobsInFair },
  } = useCompanyContactPersonGraphData()

  const partId = computed(() => fairParticipationByFairAndCompany.value.id)

  let selectedLinks = ref([])
  let newLinkName = ref('')
  let newLinkUrl = ref('')

  const showEdit = ref(false)
  const showAddNewLinkForm = ref(false)

  const {
    state: { partnerLinkList },
    actions: { loadPartnerLinks, createPartnerLink },
  } = usePartnerLink()

  const {
    state: { loadingUpdate },
    actions: { handleUpdatePartnerLinks },
  } = useCompanyFairParticipation()

  // Load partner links when component mounts
  onMounted(async () => {
    await loadPartnerLinks()
    await loadContactPersonsAndJobsInFair()
  })

  const partnerLinksAction = () => {
    if (showEdit.value) {
      savePartnerLinks()
    } else {
      toggleEditPartnerLinks()
    }
  }

  const savePartnerLinks = () => {
    const linkIds = selectedLinks.value.map((link: any) => link.id)
    handleUpdatePartnerLinks(partId.value, linkIds)
    toggleEditPartnerLinks()
  }

  const toggleEditPartnerLinks = () => {
    selectedLinks.value = [...companyPartnerLinks.value]
    showEdit.value = !showEdit.value
    showAddNewLinkForm.value = false
  }

  const addNewLink = async () => {
    if (newLinkName.value.trim() && newLinkUrl.value.trim()) {
      try {
        const result = await createPartnerLink({
          name: newLinkName.value.trim(),
          url: newLinkUrl.value.trim(),
        })
        await loadPartnerLinks()
        // Extract the actual partner link data from the GraphQL mutation result
        const newLink = result?.data?.createPartnerLink
        if (newLink) {
          // Add the new link to the selection
          selectedLinks.value.push(newLink)
        }
        newLinkName.value = ''
        newLinkUrl.value = ''
        showAddNewLinkForm.value = false
      } catch (error) {
        console.error('Error creating partner link:', error)
      }
    }
  }

  const removeLink = (linkId: string) => {
    selectedLinks.value = selectedLinks.value.filter(
      (link: any) => link.id !== linkId,
    )
  }

  const openLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  const isValidUrl = (url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const formatUrl = (url: string) => {
    if (!url) return ''
    return url.replace(/^https?:\/\//, '').replace(/\/$/, '')
  }

  const getDomainFromUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname
      return domain.replace('www.', '')
    } catch {
      return formatUrl(url)
    }
  }

  const getFaviconUrl = (url: string) => {
    try {
      const domain = new URL(url).origin
      return `${domain}/favicon.ico`
    } catch {
      return null
    }
  }

  // Handle combobox selection without navigation
  const handleLinkSelection = (selectedItems: any[]) => {
    selectedLinks.value = selectedItems
  }
</script>

<template>
  <v-skeleton-loader
    v-if="loadingUpdate"
    class="mx-auto"
    elevation="2"
    type="sentences, button"
  ></v-skeleton-loader>

  <v-card class="mx-auto mt-4" v-else>
    <v-row class="pa-3">
      <v-col cols="6">
        <v-toolbar-title class="font-weight-bold">{{
          $t('Partner Links')
        }}</v-toolbar-title>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn
          class="ml-4"
          @click="partnerLinksAction"
          :prepend-icon="showEdit ? 'mdi-content-save' : 'mdi-pencil'"
          size="small"
          variant="outlined"
          :color="showEdit ? 'success' : 'primary'"
        >
          {{ showEdit ? $t('Save') : $t('Edit') }}
        </v-btn>
      </v-col>
    </v-row>

    <v-divider></v-divider>

    <!-- Display Mode -->
    <v-card-text v-if="!showEdit">
      <div v-if="companyPartnerLinks.length > 0" class="link-grid">
        <v-card
          v-for="link in companyPartnerLinks"
          :key="link.id"
          class="link-card ma-2"
          variant="outlined"
          @click="openLink(link.url)"
          hover
        >
          <v-card-text class="pa-3">
            <div class="d-flex align-center">
              <v-avatar size="32" class="mr-3">
                <v-img
                  :src="getFaviconUrl(link.url)"
                  :alt="link.name"
                  @error="$event.target.style.display = 'none'"
                >
                  <template v-slot:placeholder>
                    <v-icon color="grey-lighten-1">mdi-link</v-icon>
                  </template>
                </v-img>
              </v-avatar>
              <div class="flex-grow-1">
                <div class="font-weight-medium text-body-2 mb-1">
                  {{ link.name }}
                </div>
                <div class="text-caption text-grey-600">
                  {{ getDomainFromUrl(link.url) }}
                </div>
              </div>
              <v-icon color="primary" size="20">mdi-open-in-new</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </div>
      <div v-else class="text-grey-400 text-center py-8">
        <v-icon size="48" color="grey-lighten-2" class="mb-2"
          >mdi-link-off</v-icon
        >
        <div>{{ $t('No partner links added yet') }}</div>
      </div>
    </v-card-text>

    <!-- Edit Mode -->
    <v-card-text v-else>
      <v-expansion-panels flat>
        <v-expansion-panel>
          <v-expansion-panel-title
            expand-icon="mdi-plus"
            collapse-icon="mdi-minus"
          >
            <v-icon start>mdi-link-plus</v-icon>
            <span class="font-weight-medium">Add New Partner Link</span>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row class="mb-4 mt-2">
              <v-col cols="12" md="5">
                <v-text-field
                  v-model="newLinkName"
                  label="Link Name"
                  variant="outlined"
                  density="compact"
                  placeholder="e.g., Company Website"
                  clearable
                />
              </v-col>
              <v-col cols="12" md="5">
                <v-text-field
                  v-model="newLinkUrl"
                  label="URL"
                  variant="outlined"
                  density="compact"
                  placeholder="https://example.com"
                  :rules="[
                    v => !!v || 'URL is required',
                    v =>
                      isValidUrl(v) ||
                      'Please enter a valid URL (include http:// or https://)',
                  ]"
                  clearable
                />
              </v-col>
              <v-col cols="12" md="2">
                <v-btn
                  @click="addNewLink"
                  :disabled="
                    !newLinkName.trim() ||
                    !newLinkUrl.trim() ||
                    !isValidUrl(newLinkUrl)
                  "
                  color="primary"
                  variant="flat"
                  size="small"
                  block
                >
                  Add Link
                </v-btn>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <v-divider class="my-4" />

      <div class="mb-4">
        <h4 class="mb-3 text-h6">Selected Partner Links</h4>
        <div v-if="selectedLinks.length > 0" class="selected-links">
          <v-card
            v-for="link in selectedLinks"
            :key="link.id"
            class="link-card-edit ma-2"
            variant="outlined"
          >
            <v-card-text class="pa-3">
              <div class="d-flex align-center">
                <v-avatar size="32" class="mr-3">
                  <v-img
                    :src="getFaviconUrl(link.url)"
                    :alt="link.name"
                    @error="$event.target.style.display = 'none'"
                  >
                    <template v-slot:placeholder>
                      <v-icon color="grey-lighten-1">mdi-link</v-icon>
                    </template>
                  </v-img>
                </v-avatar>
                <div class="flex-grow-1">
                  <div class="font-weight-medium text-body-2 mb-1">
                    {{ link.name }}
                  </div>
                  <div class="text-caption text-grey-600">
                    {{ getDomainFromUrl(link.url) }}
                  </div>
                </div>
                <v-btn
                  icon="mdi-open-in-new"
                  size="small"
                  variant="text"
                  @click="openLink(link.url)"
                  class="mr-2"
                />
                <v-btn
                  icon="mdi-close"
                  size="small"
                  variant="text"
                  color="error"
                  @click="removeLink(link.id)"
                />
              </div>
            </v-card-text>
          </v-card>
        </div>
        <v-alert
          v-else
          type="info"
          variant="tonal"
          class="mt-2"
          density="compact"
        >
          No partner links selected yet
        </v-alert>
      </div>

      <v-divider class="my-4" />

      <div>
        <h4 class="mb-3 text-h6">Available Partner Links</h4>
        <v-combobox
          :model-value="selectedLinks"
          @update:model-value="handleLinkSelection"
          :items="partnerLinkList"
          item-text="name"
          item-value="id"
          item-title="name"
          return-object
          label="Search and select partner links"
          variant="outlined"
          chips
          clearable
          closable-chips
          multiple
          :menu-props="{ maxHeight: '300px' }"
        >
          <template v-slot:chip="{ props, item }">
            <v-chip v-bind="props" color="primary" closable>
              <v-avatar start>
                <v-img
                  :src="getFaviconUrl((item.raw as any).url)"
                  :alt="(item.raw as any).name"
                  @error="$event.target.style.display = 'none'"
                >
                  <template v-slot:placeholder>
                    <v-icon color="grey-lighten-1" size="16">mdi-link</v-icon>
                  </template>
                </v-img>
              </v-avatar>
              <span class="font-weight-medium">{{
                (item.raw as any).name
              }}</span>
            </v-chip>
          </template>
          <template v-slot:item="{ props, item }">
            <v-list-item
              v-bind="props"
              :title="(item.raw as any).name"
              :subtitle="getDomainFromUrl((item.raw as any).url)"
              @click="props.onClick"
            >
              <template v-slot:prepend>
                <v-avatar size="32" class="mr-2">
                  <v-img
                    :src="getFaviconUrl((item.raw as any).url)"
                    :alt="(item.raw as any).name"
                    @error="$event.target.style.display = 'none'"
                  >
                    <template v-slot:placeholder>
                      <v-icon color="grey-lighten-1">mdi-link</v-icon>
                    </template>
                  </v-img>
                </v-avatar>
              </template>
              <template v-slot:append>
                <v-btn
                  icon="mdi-open-in-new"
                  size="small"
                  variant="text"
                  @click.stop="openLink((item.raw as any).url)"
                />
              </template>
            </v-list-item>
          </template>
        </v-combobox>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped lang="scss">
  .link-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 8px;
  }

  .link-card {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: rgb(var(--v-theme-primary));
    }
  }

  .link-card-edit {
    transition: all 0.2s ease-in-out;
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

    &:hover {
      border-color: rgb(var(--v-theme-primary));
    }
  }

  .selected-links {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 8px;
  }

  .v-expansion-panel-title {
    font-size: 0.95rem;
  }

  .text-caption {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  .v-avatar img {
    object-fit: contain;
  }
</style>
