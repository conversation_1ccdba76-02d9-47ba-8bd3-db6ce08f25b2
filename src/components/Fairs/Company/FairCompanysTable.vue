<script setup lang="ts">
  import MoreBtn from '@/components/MoreBtn.vue'
  import {
    fairCompanyHeaders,
    fairHeaders,
  } from '@/composables/Fairs/fairTableHeaders'
  import router from '@/router'
  import { useAppStore } from '@/stores/appStore'
  import { inlineTranslate, paginationMeta } from '@/utils/utils'
  import AppSelect from '@core/components/AppSelect.vue'
  import { useFairs } from '@/composables/Fairs/useFairs'
  import { Fair } from '@/gql/graphql'
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useCompanyFairParticipation } from '@/composables/CompanyFairParticipation/useCompanyFairParticipation'

  const searchQuery = ref('')
  const selectedStatus = ref()
  const selectedRows = ref<string[]>([])

  const {
    state: { loadingAllFairs, companiesInFairList, loadingCompaniesInFair },
    actions: { handleFairCompanyClicked, loadCompaniesInFair },
  } = useFairs()

  onMounted(async () => {
    await loadCompaniesInFair()
  })

  const {
    actions: { loadAllCompanies },
  } = useCompanyGraph()

  const {
    state: { loadingRemoveCompanyFromFair },
    actions: { handleRemoveParticipation },
  } = useCompanyFairParticipation()

  const appStore = useAppStore()
  const fairStore = useFairStore()

  const removeCompanyFromFair = async (company: any) => {
    console.log('company removed from fair', company)
    try {
      await handleRemoveParticipation(company.id)
    } catch (e) {
      console.log(e)
    }
    await loadCompaniesInFair()
  }

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)

  const filteredFairCompanyList = computed(() => {
    let filtered = companiesInFairList.value || []

    if (selectedStatus.value) {
      switch (selectedStatus.value) {
        case 'ACTIVE':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'ACTIVE',
          )
          break
        case 'INACTIVE':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'INACTIVE',
          )
          break
        case 'ARCHIVED':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'ARCHIVED',
          )
          break
      }
    }

    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter((fair: { name: string }) =>
        fair.name.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalFairs = computed(() =>
    filteredFairCompanyList.value ? filteredFairCompanyList.value.length : 0,
  )

  const paginatedFairCompanyList = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredFairCompanyList.value.slice(start, end)
  })

  const statusIcons = {
    ACTIVE: 'mdi-check',
    INACTIVE: 'mdi-close',
    ARCHIVED: 'mdi-archive',
  }

  const statusColors = {
    ACTIVE: 'success',
    INACTIVE: 'error',
    ARCHIVED: 'grey',
  }

  const statusTexts = {
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
    ARCHIVED: 'ARCHIVED',
  }

  const computedMoreList = computed(() => {
    return (raw: Fair) => [
      {
        title: inlineTranslate('Remove'),
        value: 'remove',
        prependIcon: 'tabler-trash',
        action: () => () => removeCompanyFromFair(raw),
      },

      // {
      //   title: inlineTranslate('Pause'),
      //   value: 'pause',
      //   prependIcon: 'tabler-player-pause',
      //   action: () => {
      //     return () => {
      //       console.log('pause')
      //     }
      //   },
      // },
      // {
      //   title: inlineTranslate('Activate'),
      //   value: 'link',
      //   prependIcon: 'tabler-player-play',
      //   action: () => () => {
      //     console.log('activate')
      //   },
      // },
    ]
  })

  const handleMoreAction = (item: any) => {
    console.log({ item })
    if (item && item.action) {
      item.action()()
    }
  }

  const toggleList = async () => {
    if (!fairStore.showCompanyList) {
      await loadAllCompanies()
    }
    fairStore.toggleCompanyList()
  }

  watch([searchQuery, selectedStatus], () => {
    page.value = 1
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingAllFairs || loadingCompaniesInFair"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Participating Companies') }}
          <v-badge color="error" :content="totalFairs" inline></v-badge>
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
            @click="toggleList"
            size="small"
            variant="outlined"
            prepend-icon="mdi-plus"
          >
            {{ fairStore.showCompanyList ? $t('Hide') : $t('Add') }}
          </VBtn>
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Suche nach Unternehmen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
        <div class="invoice-list-status">
          <AppSelect
            v-model="selectedStatus"
            density="compact"
            placeholder="Status filtern"
            clearable
            item-title="text"
            item-value="value"
            clear-icon="tabler-x"
            :items="[
              { text: 'Activ', value: 'ACTIVE' },
              { text: 'Inaktiv', value: 'INACTIVE' },
              { text: 'Archiviert', value: 'ARCHIVED' },
            ]"
            style="inline-size: 12rem"
          />
        </div>
      </div>
    </VCardText>

    <VDivider />
    <VDataTable
      :headers="fairCompanyHeaders"
      :items="paginatedFairCompanyList"
      :loading="loadingAllFairs || appLoader || loadingRemoveCompanyFromFair"
      class="px-2"
      :search="searchQuery"
      hover
      @click:row="
        (event: MouseEvent, value: any) =>
          handleFairCompanyClicked(event, value.item)
      "
    >
      <!--      No item template below-->
      <template #no-data>
        <VCardText>
          <p>Keine Firmen vorhanden</p>
        </VCardText>
      </template>
      <template #[`item.logoImageUrl`]="{ item }">
        <div class="ma-4">
          <v-avatar :image="item.logoImageUrl" size="60"></v-avatar>
        </div>
      </template>

      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            :prepend-icon="statusIcons[item.status]"
            :color="statusColors[item.status]"
            :text="statusTexts[item.status]"
          />
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <!--        <VBtn-->
        <!--          prepend-icon="tabler-trash"-->
        <!--          density="compact"-->
        <!--          class="mx-1"-->
        <!--          elevation="0"-->
        <!--          @click:prevent="removeCompanyFromFair(item)"-->
        <!--        >-->
        <!--          Remove <VIcon />-->
        <!--        </VBtn>-->

        <MoreBtn
          :menu-list="computedMoreList(item)"
          item-props
          color="undefined"
          @item-clicked="(val: any) => handleMoreAction(val)"
        />
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalFairs as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalFairs / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalFairs / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
