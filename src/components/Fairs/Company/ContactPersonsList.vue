<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useContactPersons } from '@/composables/ContactPersons/useContactPersons'
  import { useCompanyFairContactPerson } from '@/composables/CompanyFairContactPersons/useCompanyFairContactPerson'
  import { useAppStore } from '@/stores/appStore'
  import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
  import { confirmContactPersonDelete } from '@/composables/useSweetAlert'

  const companyStore = useCompanyStore()
  const appStore = useAppStore()
  const contactPersonStore = useContactPersonStore()

  const {
    state: { loadingContactPersonByCompanyId, contactPersonsByCompanyIdList },
    actions: { handleRemoveContactPerson, loadContactPersonByCompanyId },
  } = useContactPersons()

  onMounted(async () => {
    await loadContactPersonByCompanyId()
  })

  const {
    state: { loadingCompanyCreateFairContactPerson },
    actions: { handleCreateCompanyFairContactPerson },
  } = useCompanyFairContactPerson()

  const showSearch = ref(true)
  const searchQuery = ref('')

  const toggleSearch = () => {
    showSearch.value = !showSearch.value
    if (!showSearch.value) {
      searchQuery.value = ''
    }
  }

  const removeContactPerson = async (person: any) => {
    contactPersonStore.updateContactPersonForm(person)
    const confirm = await confirmContactPersonDelete(true)
    if (confirm.isConfirmed) {
      await handleRemoveContactPerson()
      appStore.showSnack('Contact person removed successfully')
    } else {
      appStore.showSnack('Contact person not removed')
    }
  }

  const showUpdateDialog = (person: any) => {
    contactPersonStore.toggleContactPersonForm()
    contactPersonStore.updateContactPersonForm(person)
  }

  const toggleAddDialog = () => {
    contactPersonStore.toggleContactPersonForm()
  }

  const closeList = () => {
    companyStore.setContactPersonsListView(false)
  }

  // Filter fair jobs based on search query
  const filteredContactPersons = computed(() => {
    return contactPersonsByCompanyIdList.value.filter(
      (fair: { name: string }) =>
        fair.name.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  })

  // Add a fair job to the company
  const addFairContactPerson = async (person: { id: string }) => {
    await handleCreateCompanyFairContactPerson({ id: person.id })
    // Optional: Show a success message or animation
    appStore.showSnack('Kontaktperson wurde dem Unternehmen hinzugefügt')
  }
</script>

<template>
  <ContactPersonDialog />
  <v-card class="mx-auto" max-width="500">
    <v-layout>
      <!-- App Bar -->
      <v-app-bar color="pink">
        <v-btn @click="closeList" icon="mdi-close" aria-label="Close"></v-btn>
        <v-toolbar-title>{{ $t('Contact Persons') }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn
          @click="toggleSearch"
          icon="mdi-magnify"
          aria-label="Search"
        ></v-btn>
        <v-btn
          class="ml-4"
          @click="toggleAddDialog"
          prepend-icon="mdi-plus"
          aria-label="Add"
          size="small"
          variant="outlined"
          >{{ $t('Add') }}
        </v-btn>
      </v-app-bar>

      <!-- Main Content -->
      <v-main>
        <v-container>
          <!-- Search Bar -->
          <v-row v-if="showSearch">
            <v-col cols="12">
              <v-text-field
                v-model="searchQuery"
                :label="$t('Search Contact Persons')"
                single-line
                hide-details
                @click:clear="searchQuery = ''"
                clearable
                aria-label="Search Contact Persons"
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- Job List -->
          <v-row dense class="scrollable-container">
            <!-- Loading State -->
            <v-col v-if="loadingContactPersonByCompanyId" cols="12">
              <v-skeleton-loader type="card"></v-skeleton-loader>
            </v-col>

            <!-- Empty State -->
            <v-col v-else-if="filteredContactPersons.length === 0" cols="12">
              <v-alert color="primary" class="text-center">
                {{ $t('No Contact Persons Found.') }}
              </v-alert>
            </v-col>

            <v-col
              v-for="(person, index) in filteredContactPersons"
              :key="index"
              cols="12"
            >
              <v-card
                :disabled="person.isInCompany"
                class="pa-2 list"
                :loading="loadingCompanyCreateFairContactPerson"
                hover
              >
                <div class="d-flex flex-no-wrap justify-space-between">
                  <div>
                    <v-card-title class="text-h6">
                      <v-row>
                        <v-col cols="9">
                          <v-icon class="mr-2" size="small"
                            >tabler-user-square
                          </v-icon>
                          {{
                            person.name.length > 30
                              ? person.name.slice(0, 30) + '...'
                              : person.name
                          }}
                        </v-col>
                        <v-col cols="3" class="d-flex justify-end">
                          <v-btn
                            class="action-btn"
                            variant="tonal"
                            @click="showUpdateDialog(person)"
                            size="x-small"
                            icon
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </v-btn>
                          <v-btn
                            class="ml-2 action-btn"
                            variant="tonal"
                            @click="removeContactPerson(person)"
                            size="x-small"
                            icon
                          >
                            <v-icon>mdi-delete</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-title>
                    <v-row class="ml-1">
                      <v-col cols="12" sm="6">
                        <v-chip
                          variant="outlined"
                          v-if="person.email"
                          class="ma-1"
                          prepend-icon="mdi-email"
                          color="primary"
                        >
                          {{ person.email }}
                        </v-chip>
                      </v-col>
                      <v-col cols="12" sm="6">
                        <v-chip
                          v-if="person.phone"
                          class="ma-1"
                          prepend-icon="mdi-phone"
                          color="secondary"
                        >
                          {{ person.phone }}
                        </v-chip>
                      </v-col>
                    </v-row>
                  </div>
                </div>
                <v-divider v-if="!person.isInCompany" class="my-1"></v-divider>

                <v-list-item
                  v-if="!person.isInCompany"
                  prepend-icon="mdi-plus"
                  @click="addFairContactPerson(person)"
                  :subtitle="$t('Add this contact person to the fair')"
                  link
                ></v-list-item>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-main>
    </v-layout>
  </v-card>
</template>

<style scoped lang="scss">
  .scrollable-container {
    max-height: 650px; /* Adjust height as needed */
    overflow-y: auto;
    padding-right: 8px; /* Add padding to avoid scrollbar overlap */

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e32753;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }

  .v-card .list {
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .action-btn {
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
</style>
