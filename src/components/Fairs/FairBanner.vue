<script lang="ts" setup>
  import { ref } from 'vue'
  import { inlineTranslate } from '@/utils/utils'
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'

  const fairStore = useFairStore()
  const fair = ref(fairStore.fair)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      // hour: '2-digit',
      // minute: '2-digit',
    })
  }

  const editFairDetails = () => {
    fairStore.toggleFairForm()
  }

  const statusIcons = {
    ACTIVE: 'mdi-check',
    INACTIVE: 'mdi-close',
    ARCHIVED: 'mdi-archive',
  }

  const statusColors = {
    ACTIVE: 'success',
    INACTIVE: 'error',
    ARCHIVED: 'grey',
  }
</script>

<template>
  <VCard>
    <VCardText
      class="d-flex align-center flex-sm-row flex-column justify-center gap-x-5"
    >
      <div class="d-flex">
        <VAvatar
          size="100"
          :image="fair.logoImageUrl"
          class="mx-auto my-auto"
        />
      </div>

      <div class="w-100 mt-8 pt-4 mt-sm-0">
        <h6 class="text-h6 text-center text-sm-start font-weight-medium mb-3">
          {{ fair.name }}
          <VChip
            class="ml-4"
            :prepend-icon="statusIcons[fair.status]"
            :color="statusColors[fair.status]"
            label
            text-color="white"
          >
            {{ fair.status }}
          </VChip>
        </h6>

        <div
          class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4"
        >
          <div
            class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4"
          >
            <span class="d-flex">
              <VIcon size="20" icon="tabler-map-pin" class="me-1" />
              <span class="text-body-1">
                {{ fair.location }}, {{ fair.city }}
              </span>
            </span>
            <span v-if="fair.locationName" class="d-flex">
              <VIcon size="20" icon="tabler-location" class="me-1" />
              <span class="text-body-1">
                {{ fair.locationName }}
              </span>
            </span>

            <span class="d-flex">
              <VIcon size="20" icon="tabler-calendar-event" class="me-1" />
              <span class="text-body-1">
                <b>{{ $t('From') }}: </b> {{ formatDate(fair.startDate) }} -
                <b>{{ $t('To') }}: </b>{{ formatDate(fair.endDate) }}
              </span>
            </span>

            <span class="d-flex">
              <VIcon size="20" icon="tabler-calendar" class="me-1" />
              <b class="font-bold">{{ inlineTranslate('Registration') }}:</b>
              <span class="text-body-1 ml-2">
                {{ formatDate(fair.registrationStartDate) }} -
                {{ formatDate(fair.registrationEndDate) }}
              </span>
            </span>

            <!-- Contact Person -->
            <span class="d-flex">
              <VIcon size="20" icon="tabler-user" class="me-1" />
              <span class="text-body-1">
                {{ fair.contactPersonName }} ({{ fair.contactPersonEmail }})
              </span>
            </span>
          </div>
        </div>
      </div>

      <div class="d-flex">
        <VBtn
          variant="outlined"
          @click="editFairDetails"
          prepend-icon="tabler-pencil"
          >{{ $t('Edit') }}
        </VBtn>
      </div>
    </VCardText>
    <v-card class="mx-auto" style="border-radius: 0px" flat elevation="0">
      <v-card-text class="bg-grey-200 pt-4">
        {{ fair.description }}
      </v-card-text>
    </v-card>
  </VCard>
</template>

<style lang="scss">
  .fair-banner {
    .v-card-text {
      padding: 24px;
    }

    .v-avatar {
      border: 3px solid rgb(var(--v-theme-surface));
      background-color: rgb(var(--v-theme-surface)) !important;
    }

    .v-chip {
      font-weight: bold;
    }
  }
</style>
