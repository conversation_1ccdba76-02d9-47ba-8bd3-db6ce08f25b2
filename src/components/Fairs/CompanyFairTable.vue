<script setup lang="ts">
  import MoreBtn from '@/components/MoreBtn.vue'
  import { fairHeaders } from '@/composables/Fairs/fairTableHeaders'
  import router from '@/router'
  import { useAppStore } from '@/stores/appStore'
  import { inlineTranslate, paginationMeta } from '@/utils/utils'
  import AppSelect from '@core/components/AppSelect.vue'
  import { useFairs } from '@/composables/Fairs/useFairs'
  import { Fair } from '@/gql/graphql'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'
  import { useCompanyFairGraph } from '@/api/graphHooks/fair/useCompanyFairGraph'
  import { formatDate } from '@core/utils/formatters'

  const searchQuery = ref('')
  const selectedStatus = ref()
  const selectedRows = ref<string[]>([])

  const {
    state: { allFairsList, loadingAllFairs },
    actions: { handleCompanyFairClicked, loadFairs },
  } = useFairs()

  const appStore = useAppStore()
  const fairFormStore = useFairFormStore()

  const {
    state: { allCompanyFairs, loadingCompanyFair },
  } = useCompanyFairGraph()

  onMounted(async () => {
    await loadFairs()
  })

  const editFairDetails = async (fair: any) => {
    await router.push({
      name: 'fair-fairs-fid',
      params: { fid: fair.id },
    })
    fairStore.updateFair(fair)
    fairFormStore.setFair(fair)
    fairStore.openFairForm()
  }

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)

  const filteredFairList = computed(() => {
    let filtered = allCompanyFairs.value || []

    if (selectedStatus.value) {
      switch (selectedStatus.value) {
        case 'ACTIVE':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'ACTIVE',
          )
          break
        case 'INACTIVE':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'INACTIVE',
          )
          break
        case 'ARCHIVED':
          filtered = filtered.filter(
            (fair: { status: string }) => fair.status === 'ARCHIVED',
          )
          break
      }
    }

    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter((fair: { name: string }) =>
        fair.name.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalFairs = computed(() =>
    filteredFairList.value ? filteredFairList.value.length : 0,
  )

  const paginatedFairList = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredFairList.value.slice(start, end)
  })

  const statusIcons = {
    ACTIVE: 'mdi-check',
    INACTIVE: 'mdi-close',
    ARCHIVED: 'mdi-archive',
  }

  const statusColors = {
    ACTIVE: 'success',
    INACTIVE: 'error',
    ARCHIVED: 'grey',
  }

  const statusTexts = {
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
    ARCHIVED: 'ARCHIVED',
  }

  const computedMoreList = computed(() => {
    return (raw: Fair) => [
      {
        title: inlineTranslate('buttons.edit'),
        value: 'edit',
        prependIcon: 'tabler-pencil',
        action: () => () => editFairDetails(raw),
      },
      {
        title: inlineTranslate('buttons.duplicate'),
        value: 'duplicate',
        prependIcon: 'tabler-copy',
        action: () => () => {
          console.log('copy here')
        },
      },
      {
        title: inlineTranslate('buttons.pause'),
        value: 'pause',
        prependIcon: 'tabler-player-pause',
        action: () => {
          return () => {
            console.log('pause')
          }
        },
      },
      {
        title: inlineTranslate('buttons.activate'),
        value: 'link',
        prependIcon: 'tabler-player-play',
        action: () => () => {
          console.log('activate')
        },
      },
    ]
  })

  const handleMoreAction = (item: any) => {
    if (item && item.action) {
      item.action()()
    }
  }

  const goToFairForm = async () => {
    fairFormStore.resetForm()
    await router.push('/fair/fairs/new')
  }

  watch([searchQuery, selectedStatus], () => {
    page.value = 1
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingCompanyFair"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Fairs') }}
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <!--          <VBtn color="primary" variant="outlined" @click="goToFairForm">-->
          <!--            <v-icon class="mr-1" icon="tabler-plus"></v-icon>-->
          <!--            {{ $t('Create New Fair') }}-->
          <!--          </VBtn>-->
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Suche nach Messen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
        <div class="invoice-list-status">
          <AppSelect
            v-model="selectedStatus"
            density="compact"
            placeholder="Status filtern"
            clearable
            item-title="text"
            item-value="value"
            clear-icon="tabler-x"
            :items="[
              { text: 'Activ', value: 'ACTIVE' },
              { text: 'Inaktiv', value: 'INACTIVE' },
              { text: 'Archiviert', value: 'ARCHIVED' },
            ]"
            style="inline-size: 12rem"
          />
        </div>
      </div>
    </VCardText>

    <VDivider />
    <VDataTable
      v-model="selectedRows"
      :headers="fairHeaders"
      :items="paginatedFairList"
      :loading="loadingAllFairs || appLoader"
      class="px-2"
      :search="searchQuery"
      hover
      show-select
      @click:row="
        (event: MouseEvent, value: any) =>
          handleCompanyFairClicked(event, value.item)
      "
    >
      <!--      No item template below-->
      <template #no-data>
        <VCardText>
          <p>Keine Messen vorhanden</p>
        </VCardText>
      </template>
      <template #[`item.logoImageUrl`]="{ item }">
        <div class="ma-4">
          <v-avatar :image="item.logoImageUrl" size="60"></v-avatar>
        </div>
      </template>

      <template #[`item.startDate`]="{ item }">
        {{ formatDate(item.startDate) }}
      </template>

      <template #[`item.endDate`]="{ item }">
        {{ formatDate(item.endDate) }}
      </template>

      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            :prepend-icon="statusIcons[item.status]"
            :color="statusColors[item.status]"
            :text="statusTexts[item.status]"
          />
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <IconBtn icon="tabler-eye" density="compact" class="mx-1" elevation="0">
          <VIcon />
        </IconBtn>

        <!--        <MoreBtn-->
        <!--          :menu-list="computedMoreList(item)"-->
        <!--          item-props-->
        <!--          color="undefined"-->
        <!--          @item-clicked="(val: any) => handleMoreAction(val)"-->
        <!--        />-->
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalFairs as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalFairs / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalFairs / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
