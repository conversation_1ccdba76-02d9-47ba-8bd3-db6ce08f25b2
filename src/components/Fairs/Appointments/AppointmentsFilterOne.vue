<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { format } from 'date-fns'

  const props = defineProps({
    rawAppointmentsList: {
      type: Array,
      required: true,
    },
  })

  const emit = defineEmits(['update:filters'])

  const filters = reactive({
    timeRange: {
      start: null as Date | null,
      end: null as Date | null,
    },
    applicantId: null as string | null,
    contactPersonId: null as string | null,
    companyId: null as string | null,
    fairId: null as string | null,
    status: null as string | null,
    dateRange: {
      start: null as Date | null,
      end: null as Date | null,
    },
  })

  // Track if the filter panel is open or closed
  const isFilterPanelOpen = ref(false)
  const appliedFiltersCount = ref(0)

  // Extract unique values for dropdowns from the appointments list
  const applicants = computed(() => {
    if (!props.rawAppointmentsList || !props.rawAppointmentsList.length)
      return []

    const uniqueApplicants = new Map()
    props.rawAppointmentsList.forEach((appointment: any) => {
      if (appointment.applicant && appointment.applicant.id) {
        uniqueApplicants.set(appointment.applicant.id, {
          id: appointment.applicant.id,
          text:
            `${appointment.applicant.firstName || ''} ${
              appointment.applicant.lastName || ''
            }`.trim() || 'Unknown',
        })
      }
    })
    return Array.from(uniqueApplicants.values())
  })

  const contactPersons = computed(() => {
    if (!props.rawAppointmentsList || !props.rawAppointmentsList.length)
      return []

    const uniqueContactPersons = new Map()
    props.rawAppointmentsList.forEach((appointment: any) => {
      if (
        appointment.contactPersonTimeslot?.companyFairContactPerson
          ?.contactPerson
      ) {
        const contactPerson =
          appointment.contactPersonTimeslot.companyFairContactPerson
            .contactPerson
        uniqueContactPersons.set(contactPerson.id, {
          id: contactPerson.id,
          text:
            `${contactPerson.name || ''}
          `.trim() || 'Unknown',
        })
      }
    })
    return Array.from(uniqueContactPersons.values())
  })

  const companies = computed(() => {
    if (!props.rawAppointmentsList || !props.rawAppointmentsList.length)
      return []

    const uniqueCompanies = new Map()
    props.rawAppointmentsList.forEach((appointment: any) => {
      if (
        appointment.contactPersonTimeslot?.companyFairContactPerson
          ?.companyFairParticipation?.company
      ) {
        const company =
          appointment.contactPersonTimeslot.companyFairContactPerson
            .companyFairParticipation.company
        uniqueCompanies.set(company.id, {
          id: company.id,
          text: company.name || 'Unknown Company',
        })
      }
    })
    return Array.from(uniqueCompanies.values())
  })

  const fairs = computed(() => {
    if (!props.rawAppointmentsList || !props.rawAppointmentsList.length)
      return []

    const uniqueFairs = new Map()
    props.rawAppointmentsList.forEach((appointment: any) => {
      if (
        appointment.contactPersonTimeslot?.companyFairContactPerson
          ?.companyFairParticipation?.fair
      ) {
        const fair =
          appointment.contactPersonTimeslot.companyFairContactPerson
            .companyFairParticipation.fair
        uniqueFairs.set(fair.id, {
          id: fair.id,
          text: fair.name || 'Unknown Fair',
        })
      }
    })
    return Array.from(uniqueFairs.values())
  })

  // Status options
  const statusOptions = [
    { id: 'confirmed', text: 'Confirmed' },
    { id: 'requested', text: 'Requested' },
    { id: 'cancelled', text: 'Cancelled' },
    { id: 'rejected', text: 'Rejected' },
  ]

  // Handle time menu state
  const timeRangeMenu = ref(false)
  const dateRangeMenu = ref(false)

  // Format time for display
  const formattedTimeRange = computed(() => {
    const { start, end } = filters.timeRange
    if (!start && !end) return ''

    const formattedStart = start ? format(start, 'HH:mm') : '00:00'
    const formattedEnd = end ? format(end, 'HH:mm') : '23:59'

    return `${formattedStart} - ${formattedEnd}`
  })

  // Format date range for display
  const formattedDateRange = computed(() => {
    const { start, end } = filters.dateRange
    if (!start && !end) return ''

    const formattedStart = start ? format(start, 'dd.MM.yyyy') : ''
    const formattedEnd = end ? format(end, 'dd.MM.yyyy') : ''

    if (formattedStart && formattedEnd) {
      return `${formattedStart} - ${formattedEnd}`
    }
    return formattedStart || formattedEnd
  })

  // Count applied filters
  const updateAppliedFiltersCount = () => {
    let count = 0

    if (filters.timeRange.start || filters.timeRange.end) count++
    if (filters.dateRange.start || filters.dateRange.end) count++
    if (filters.applicantId) count++
    if (filters.contactPersonId) count++
    if (filters.companyId) count++
    if (filters.fairId) count++
    if (filters.status) count++

    appliedFiltersCount.value = count
  }

  // Apply filters
  const applyFilters = () => {
    emit('update:filters', { ...filters })
    updateAppliedFiltersCount()
    isFilterPanelOpen.value = false
  }

  // Clear all filters
  const clearAllFilters = () => {
    Object.assign(filters, {
      timeRange: { start: null, end: null },
      applicantId: null,
      contactPersonId: null,
      companyId: null,
      fairId: null,
      status: null,
      dateRange: { start: null, end: null },
    })

    emit('update:filters', { ...filters })
    updateAppliedFiltersCount()
  }

  // Toggle filter panel
  const toggleFilterPanel = () => {
    isFilterPanelOpen.value = !isFilterPanelOpen.value
  }

  // Watch for changes to update filter count
  watch(
    filters,
    () => {
      updateAppliedFiltersCount()
    },
    { deep: true },
  )
</script>

<template>
  <div class="filter-component mb-4">
    <!-- Filter Button and Badge -->
    <div class="d-flex justify-space-between align-center mb-4">
      <v-btn
        prepend-icon="mdi-filter-variant"
        color="primary"
        variant="outlined"
        @click="toggleFilterPanel"
        class="filter-btn"
      >
        {{ $t('Filter') }}
        <v-badge
          v-if="appliedFiltersCount > 0"
          :content="appliedFiltersCount"
          color="primary"
          offset-x="10"
          offset-y="-10"
        ></v-badge>
      </v-btn>

      <v-btn
        v-if="appliedFiltersCount > 0"
        variant="text"
        size="small"
        @click="clearAllFilters"
        class="clear-btn"
      >
        {{ $t('Clear all filters') }}
      </v-btn>
    </div>

    <!-- Filter Panel -->
    <v-expand-transition>
      <v-card v-if="isFilterPanelOpen" class="mb-4 filter-panel">
        <v-card-text>
          <v-row>
            <!-- Date Range Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-menu
                v-model="dateRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="formattedDateRange"
                    :label="$t('Date Range')"
                    prepend-icon="mdi-calendar-range"
                    readonly
                    clearable
                    @click:clear="
                      filters.dateRange = { start: null, end: null }
                    "
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="filters.dateRange"
                  range
                  @update:model-value="dateRangeMenu = false"
                ></v-date-picker>
              </v-menu>
            </v-col>

            <!-- Time Range Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-menu
                v-model="timeRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="formattedTimeRange"
                    :label="$t('Time Range')"
                    prepend-icon="mdi-clock-outline"
                    readonly
                    clearable
                    @click:clear="
                      filters.timeRange = { start: null, end: null }
                    "
                  ></v-text-field>
                </template>

                <v-card min-width="300px">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                        <v-label>{{ $t('Start Time') }}</v-label>
                        <v-time-picker
                          v-model="filters.timeRange.start"
                          format="24hr"
                          scrollable
                        ></v-time-picker>
                      </v-col>

                      <v-col cols="12">
                        <v-label>{{ $t('End Time') }}</v-label>
                        <v-time-picker
                          v-model="filters.timeRange.end"
                          format="24hr"
                          scrollable
                        ></v-time-picker>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn text @click="timeRangeMenu = false" color="primary">
                      {{ $t('Apply') }}
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
            </v-col>

            <!-- Status Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-select
                v-model="filters.status"
                :items="statusOptions"
                item-title="text"
                item-value="id"
                :label="$t('Status')"
                prepend-inner-icon="mdi-check-circle-outline"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <!-- Applicant Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                v-model="filters.applicantId"
                :items="applicants"
                item-title="text"
                density="compact"
                variant="outlined"
                item-value="id"
                :label="$t('Applicant')"
                prepend-inner-icon="mdi-account"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <!-- Contact Person Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                v-model="filters.contactPersonId"
                :items="contactPersons"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Contact Person')"
                prepend-inner-icon="mdi-account-tie"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <!-- Company Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                v-model="filters.companyId"
                :items="companies"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Company')"
                prepend-inner-icon="mdi-domain"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <!-- Fair Filter -->
            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                v-model="filters.fairId"
                :items="fairs"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Fair')"
                prepend-inner-icon="mdi-tent"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" variant="text" @click="clearAllFilters">
            {{ $t('Clear') }}
          </v-btn>
          <v-btn color="primary" @click="applyFilters">
            {{ $t('Apply Filters') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-expand-transition>
  </div>
</template>

<style scoped>
  .filter-panel {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  }

  .filter-btn {
    position: relative;
  }
</style>
