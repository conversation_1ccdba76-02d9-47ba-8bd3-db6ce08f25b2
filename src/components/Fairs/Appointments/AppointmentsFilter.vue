<script setup lang="ts">
  import { useFilterStore } from '@/stores/fairs/filterStore'
  import { useAppointments } from '@/composables/Appointments/useAppointments'
  import { formatDateToString } from '@core/utils/formatters'
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'

  // Extend dayjs with UTC plugin
  dayjs.extend(utc)

  const filterStore = useFilterStore()

  const { filters, clearTimeRangeFilter } = filterStore

  const {
    state: { filterOptions, prepareFiltersForApi, loadingFilteredAppointments },
    actions: { refetchFilteredAppointments, loadFilterOptions },
  } = useAppointments()

  const timeRangeDisplay = computed(() => {
    const { startTime, endTime } = filterStore.filters.timeRange
    return startTime && endTime ? `${startTime} - ${endTime}` : '00:00 - 00:00'
  })

  const isFilterPanelOpen = ref(false)

  const resetFilters = () => {
    filterStore.clearAllFilters()
    clearTimeRangeFilter()
    timeRangeMenu.value = false
    refetchFilteredAppointments({
      filter: {},
      skip: 0,
      take: 10,
    })
  }

  const statusOptions = [
    { id: 'CONFIRMED', text: 'Confirmed' },
    { id: 'REQUESTED', text: 'Requested' },
    { id: 'CANCELED', text: 'Canceled' },
    { id: 'REJECTED', text: 'Rejected' },
  ]

  const clearTimeFilter = () => {
    clearTimeRangeFilter()
    timeRangeMenu.value = false
  }

  const timeRangeMenu = ref(false)

  const toggleFilterPanel = () => {
    isFilterPanelOpen.value = !isFilterPanelOpen.value
  }

  const fetchFilteredAppointments = () => {
    refetchFilteredAppointments({
      filter: prepareFiltersForApi.value,
      skip: 0,
      take: 10,
    })
  }

  onMounted(async () => {
    await loadFilterOptions()
  })

  watch(() => filterStore.filteredAppointments, async (newAppointments) => {
    if (newAppointments.length > 0) {
      await loadFilterOptions()
    }
  }, { deep: true })
</script>

<template>
  <div class="filter-component mb-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <v-btn
        prepend-icon="mdi-filter-variant"
        color="primary"
        size="small"
        variant="outlined"
        @click="toggleFilterPanel"
        class="filter-btn"
      >
        {{ $t('Filter') }}
        <template v-slot:append>
          <v-badge
            v-if="filterStore.appliedFiltersCount > 0"
            color="error"
            :content="filterStore.appliedFiltersCount"
            inline
          ></v-badge>
        </template>
      </v-btn>
    </div>

    <v-expand-transition>
      <v-card elevation="1" v-if="isFilterPanelOpen" class="mb-4">
        <v-card-text>
          <v-row>
            <v-col cols="12" sm="6" md="4">
              <VTextField
                :disabled="loadingFilteredAppointments"
                :value="formatDateToString(filterStore.filters.date)"
                density="compact"
                @click:clear="filterStore.clearDateFilter"
                variant="outlined"
                clearable
                type="date"
                label="Date"
                @update:model-value="
                  (val: any) =>
                    (filterStore.filters.date = dayjs.utc(val).toDate())
                "
              />
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-menu
                :disabled="loadingFilteredAppointments"
                v-model="timeRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="timeRangeDisplay"
                    :disabled="!filterStore.filters.date"
                    :label="$t('Time Range')"
                    prepend-inner-icon="mdi-clock-outline"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearTimeFilter"
                  ></v-text-field>
                </template>

                <v-card min-width="300px">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredAppointments"
                          v-model="filterStore.filters.timeRange.startTime"
                          label="Start Time"
                          type="time"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>

                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredAppointments"
                          v-model="filterStore.filters.timeRange.endTime"
                          label="End Time"
                          type="time"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn text @click="clearTimeFilter" color="primary">
                      {{ $t('Clear') }}
                    </v-btn>
                    <v-btn color="success" text @click="timeRangeMenu = false">
                      {{ $t('Apply') }}
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredAppointments"
                v-model="filterStore.filters.status"
                :items="statusOptions"
                item-title="text"
                item-value="id"
                :label="$t('Status')"
                prepend-inner-icon="mdi-check-circle-outline"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                :disabled="loadingFilteredAppointments"
                v-model="filterStore.filters.applicantId"
                :items="filterOptions.applicants"
                item-title="text"
                density="compact"
                variant="outlined"
                item-value="id"
                :label="$t('Applicant')"
                prepend-inner-icon="mdi-account"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                :disabled="loadingFilteredAppointments"
                v-model="filterStore.filters.contactPersonId"
                :items="filterOptions.contactPersons"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Contact Person')"
                prepend-inner-icon="mdi-account-tie"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                :disabled="loadingFilteredAppointments"
                v-model="filterStore.filters.companyId"
                :items="filterOptions.companies"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Company')"
                prepend-inner-icon="mdi-domain"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-autocomplete
                :disabled="loadingFilteredAppointments"
                v-model="filterStore.filters.fairId"
                :items="filterOptions.fairs"
                item-title="text"
                item-value="id"
                density="compact"
                variant="outlined"
                :label="$t('Fair')"
                prepend-inner-icon="mdi-tent"
                clearable
                return-object
              ></v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            size="small"
            prepend-icon="mdi-close"
            @click="resetFilters"
            class="clear-btn mx-4"
          >
            {{ $t('Clear All') }}
          </v-btn>
          <v-btn
            :loading="loadingFilteredAppointments"
            :disabled="
              filterStore.appliedFiltersCount === 0 ||
              loadingFilteredAppointments
            "
            variant="outlined"
            @click="fetchFilteredAppointments"
          >
            {{ $t('Filter Appointments') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-expand-transition>
  </div>
</template>
