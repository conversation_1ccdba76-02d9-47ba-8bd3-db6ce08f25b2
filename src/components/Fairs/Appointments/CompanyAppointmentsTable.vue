<script setup lang="ts">
  import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
  import { useAppStore } from '@/stores/appStore'
  import { paginationMeta } from '@/utils/utils'
  import { useAppointments } from '@/composables/Appointments/useAppointments'
  import { appointmentsTableHeaders } from '@/composables/Appointments/appointmentsTableHeaders'
  import { useFilterStore } from '@/stores/fairs/filterStore'
  import { useRouter } from 'vue-router'
  import { useAuthStore } from '@/stores/authStore'

  const router = useRouter()
  const authStore = useAuthStore()
  const appStore = useAppStore()
  const filterStore = useFilterStore()

  const {
    state: {
      appointmentsList,
      loadingAllAppointments,
      rawAppointmentsList,
      loadingFilteredAppointments,
    },
    actions: {
      handleDeleteAppointment,
      handleUpdateAppointmentStatus,
      fetchCompanyAppointments,
      initiateAppointmentListen,
      stopAppointmentListen,
    },
  } = useAppointments()

  const appLoader = computed(() => appStore.showLoader)
  const searchQuery = ref('')

  const itemsPerPage = ref(8)
  const page = ref(1)

  const totalItems = computed(() => filterStore.totalAppointmentsCount || 0)

  const companyFairContactPersonId = computed(
    () =>
      filterStore.filteredAppointments[0]?.contactPersonTimeslot
        ?.companyFairContactPerson?.id || '',
  )

  const removeAppointment = async (appointment: any, event: Event) => {
    event.stopPropagation()
    try {
      await handleDeleteAppointment(appointment.id)
      appStore.showSnack('Appointment successfully removed')
      if (authStore.companyId) {
        await fetchCompanyAppointments(
          authStore.companyId,
          (page.value - 1) * itemsPerPage.value,
          itemsPerPage.value,
        )
      }
    } catch (error) {
      appStore.showSnack('Error removing appointment')
      console.error('Error removing appointment:', error)
    }
  }

  const updateStatus = async (
    appointment: any,
    newStatus: string,
    event: Event,
  ) => {
    event.stopPropagation()
    try {
      const appointmentId = appointment.id
      await handleUpdateAppointmentStatus(appointmentId, newStatus)
      appStore.showSnack(`Appointment status updated to ${newStatus}`)
      if (authStore.companyId) {
        await fetchCompanyAppointments(
          authStore.companyId,
          (page.value - 1) * itemsPerPage.value,
          itemsPerPage.value,
        )
      }
    } catch (error) {
      appStore.showSnack('Error updating appointment status')
      console.error('Error updating appointment status:', error)
    }
  }

  const goToApplicantDetails = (appointment: any) => {
    filterStore.setActiveAppointment(appointment)
    router.push({
      name: 'fair-appointments-id-applicant-uid',
      params: {
        id: appointment.id,
        uid: appointment.applicantId,
      },
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'success'
      case 'requested':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'rejected':
        return 'error'
      default:
        return 'default'
    }
  }

  const updateFilters = (newFilters: any) => {
    page.value = 1
    if (authStore.companyId) {
      fetchCompanyAppointments(authStore.companyId, 0, itemsPerPage.value)
    }
  }

  onMounted(() => {
    if (authStore.companyId) {
      fetchCompanyAppointments(authStore.companyId, 0, itemsPerPage.value)
    } else {
      console.error('Company ID not found. Cannot fetch appointments.')
    }

    // TODO: Listener logic still needs review
    initiateAppointmentListen(companyFairContactPersonId.value)
  })

  watch(
    [page, itemsPerPage],
    ([newPage, newItemsPerPage]) => {
      if (authStore.companyId) {
        fetchCompanyAppointments(
          authStore.companyId,
          (newPage - 1) * newItemsPerPage,
          newItemsPerPage,
        )
      }
    },
    { immediate: false },
  )

  onUnmounted(() => {
    stopAppointmentListen()
    console.log('Stopped appointment listener')
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingAllAppointments && !appointmentsList.length"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Appointments') }}
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <AppointmentsFilter
        :rawAppointmentsList="rawAppointmentsList"
        @update:filters="updateFilters"
      />
    </VCardText>

    <VDivider />
    <VDataTable
      :headers="appointmentsTableHeaders"
      :items="appointmentsList"
      :loading="loadingAllAppointments || loadingFilteredAppointments"
      class="px-2"
      :search="searchQuery"
      hover
      :items-per-page="-1"
      @click:row="(event: Event, { item }: any) => goToApplicantDetails(item)"
    >
      <!-- No item template below -->
      <template #no-data>
        <VCardText>
          <p>{{ $t('Keine Termine gefunden') }}</p>
        </VCardText>
      </template>

      <!-- Image column -->
      <template #[`item.profileImageUrl`]="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="45"
            :color="item.profileImageUrl ? '' : 'primary'"
            :class="
              item.profileImageUrl ? '' : 'v-avatar-light-bg primary--text'
            "
            :variant="!item.profileImageUrl ? 'tonal' : undefined"
          >
            <VImg v-if="item.profileImageUrl" :src="item.profileImageUrl" />
            <span v-else>{{ item.applicantName }}</span>
          </VAvatar>

          <div class="d-flex flex-column align-start ms-3">
            <span
              class="d-block font-weight-bold text-body-1 text-high-emphasis text-truncate"
              >{{ item.applicantName }}</span
            >
            <small>
              <v-chip size="x-small">
                <v-icon size="small" class="mr-2"> mdi-domain</v-icon
                >{{ item.applicantCity }}</v-chip
              >
            </small>
          </div>
        </div>
      </template>

      <!-- Status column -->
      <template #[`item.status`]="{ item }">
        <VChip
          :color="getStatusColor(item.status)"
          size="small"
          class="text-capitalize"
        >
          {{ item.status }}
        </VChip>
      </template>

      <!-- Actions column -->
      <template #[`item.actions`]="{ item }">
        <div class="d-flex gap-2">
          <v-btn
            @click="updateStatus(item, 'CONFIRMED', $event)"
            size="x-small"
            :disabled="item.status === 'confirmed'"
            color="success"
            icon="mdi-check"
            variant="tonal"
          >
            <v-icon>mdi-check</v-icon>
            <v-tooltip activator="parent" location="top">{{
              $t('Confirm')
            }}</v-tooltip>
          </v-btn>

          <v-btn
            @click="removeAppointment(item, $event)"
            size="x-small"
            color="error"
            icon="mdi-delete"
            variant="tonal"
          >
            <v-icon>mdi-delete</v-icon>
            <v-tooltip activator="parent" location="top">{{
              $t('Remove')
            }}</v-tooltip>
          </v-btn>

          <v-btn
            size="x-small"
            color="default"
            icon="mdi-dots-vertical"
            variant="text"
            v-menu:menu
          >
            <v-icon>mdi-dots-vertical</v-icon>
            <v-menu activator="parent" location="bottom end">
              <v-list density="compact">
                <v-list-item
                  @click="updateStatus(item, 'CANCELED', $event)"
                  v-if="item.status !== 'CANCELED'"
                >
                  <v-list-item-title>{{ $t('Cancel') }}</v-list-item-title>
                </v-list-item>
                <v-list-item @click="goToApplicantDetails(item)">
                  <v-list-item-title>{{
                    $t('View Profile')
                  }}</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="
                    () => {
                      goToApplicantDetails(item)
                    }
                  "
                >
                  <v-list-item-title>{{
                    $t('Send Message')
                  }}</v-list-item-title>
                </v-list-item>
                <v-list-item @click="() => {}">
                  <v-list-item-title>{{ $t('Rate') }}</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="updateStatus(item, 'REJECTED', $event)"
                  v-if="item.status !== 'REJECTED'"
                >
                  <v-list-item-title>{{ $t('Reject') }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-btn>
        </div>
      </template>

      <!-- Pagination -->
      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalItems) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalItems / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : $vuetify.display.md ? 5 : 7
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ $t('Previous') }}
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ $t('Next') }}
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  .appointment-status-confirmed {
    color: var(--v-success-base);
  }
  .appointment-status-pending {
    color: var(--v-warning-base);
  }
  .appointment-status-cancelled {
    color: var(--v-error-base);
  }
</style>
