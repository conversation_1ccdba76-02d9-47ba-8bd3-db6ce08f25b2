<script setup lang="ts">
  import { computed, ref, watch, onMounted } from 'vue'
  import { useAppStore } from '@/stores/appStore'
  import { paginationMeta } from '@/utils/utils'
  import { useAppointments } from '@/composables/Appointments/useAppointments'
  import { appointmentsTableHeaders } from '@/composables/Appointments/appointmentsTableHeaders'
  import { useFilterStore } from '@/stores/fairs/filterStore'
  import { useRouter } from 'vue-router'
  import { useTheme } from 'vuetify'
  import {
    confirmAppointmentReject,
    confirmAppointmentDelete,
  } from '@/composables/useSweetAlert'

  const router = useRouter()
  const theme = useTheme()

  const {
    state: {
      appointmentsList,
      loadingAllAppointments,
      rawAppointmentsList,
      loadingFilteredAppointments,
      prepareFiltersForApi,
    },
    actions: {
      handleDeleteAppointment,
      handleUpdateAppointmentStatus,
      refetchFilteredAppointments,
      initiateAppointmentListen,
    },
  } = useAppointments()

  const appStore = useAppStore()
  const filterStore = useFilterStore()

  const appLoader = computed(() => appStore.showLoader)
  const searchQuery = ref('')

  const itemsPerPage = ref(8)
  const page = ref(1)

  // No need for filteredList and slicing, use directly from API
  const totalItems = computed(() => filterStore.totalAppointmentsCount || 0)

  const companyFairContactPersonId = computed(
    () =>
      filterStore.filteredAppointments[0]?.contactPersonTimeslot
        ?.companyFairContactPerson?.id || '',
  )

  const removeAppointment = async (appointment: any, event: Event) => {
    event.stopPropagation()

    try {
      // Show SweetAlert dialog for reject reason
      const result = await confirmAppointmentDelete(
        theme.global.current.value.dark,
      )

      if (result.isConfirmed) {
        const rejectReason = result.value || undefined
        await handleDeleteAppointment(appointment.id, rejectReason)
        appStore.showSnack('Appointment successfully rejected')
        refetchCurrentPage()
      }
    } catch (error) {
      appStore.showSnack('Error rejecting appointment')
      console.error('Error removing appointment:', error)
    }
  }

  const rejectAppointment = async (appointment: any, event: Event) => {
    event.stopPropagation()

    try {
      // Show SweetAlert dialog for reject reason
      const result = await confirmAppointmentReject(
        theme.global.current.value.dark,
      )

      if (result.isConfirmed) {
        const rejectReason = result.value || undefined
        await handleUpdateAppointmentStatus(
          appointment.id,
          'REJECTED',
          rejectReason,
        )
        appStore.showSnack('Appointment successfully rejected')
        refetchCurrentPage()
      }
    } catch (error) {
      appStore.showSnack('Error rejecting appointment')
      console.error('Error rejecting appointment:', error)
    }
  }

  const updateStatus = async (
    appointment: any,
    newStatus: string,
    event: Event,
  ) => {
    event.stopPropagation()
    try {
      const appointmentId = appointment.id
      await handleUpdateAppointmentStatus(appointmentId, newStatus)
      appStore.showSnack(`Terminstatus auf ${newStatus} aktualisiert`)
      // Refetch after status update
      refetchCurrentPage()
    } catch (error) {
      appStore.showSnack('Error updating appointment status')
      console.error('Error updating appointment status:', error)
    }
  }

  const goToApplicantDetails = (e: Event, appointment: any) => {
    filterStore.setActiveAppointment(appointment)
    router.push({
      name: 'fair-appointments-id-applicant-uid',
      params: {
        id: appointment.id,
        uid: appointment.applicantId,
      },
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'success'
      case 'requested':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'rejected':
        return 'error'
      default:
        return 'default'
    }
  }

  const updateFilters = (newFilters: any) => {
    page.value = 1
    refetchCurrentPage()
  }

  const refetchCurrentPage = () => {
    refetchFilteredAppointments({
      filter: prepareFiltersForApi.value,
      skip: (page.value - 1) * itemsPerPage.value,
      take: itemsPerPage.value,
    })
  }

  onMounted(() => {
    initiateAppointmentListen(companyFairContactPersonId.value)
    if (filterStore.filteredAppointments.length === 0) {
      refetchCurrentPage()
    }
  })

  watch([page, itemsPerPage], () => {
    refetchCurrentPage()
  })
</script>

<template>
  <v-skeleton-loader
    v-if="loadingAllAppointments && !appointmentsList.length"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :disabled="appLoader">
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6">
          {{ $t('Appointments Overview') }}
        </v-col>
      </v-row>
    </template>
    <VCardText>
      <AppointmentsFilter
        :rawAppointmentsList="rawAppointmentsList"
        @update:filters="updateFilters"
      />
    </VCardText>

    <VDivider />
    <VDataTable
      :headers="appointmentsTableHeaders"
      :items="appointmentsList"
      class="px-2"
      :search="searchQuery"
      hover
      :items-per-page="-1"
      @click:row="
        (event: Event, { item }: any) => goToApplicantDetails(event, item)
      "
    >
      <!-- No item template below -->
      <template #no-data>
        <VCardText>
          <p>{{ $t('Keine Termine gefunden') }}</p>
        </VCardText>
      </template>

      <!-- Image column -->
      <template #[`item.profileImageUrl`]="{ item }">
        <div class="d-flex align-center">
          <VBadge
            dot
            location="bottom right"
            offset-x="3"
            offset-y="3"
            color="error"
            :model-value="item.companyIsNew"
          >
            <VAvatar
              size="45"
              :color="item.profileImageUrl ? '' : 'primary'"
              :class="
                item.profileImageUrl ? '' : 'v-avatar-light-bg primary--text'
              "
              :variant="!item.profileImageUrl ? 'tonal' : undefined"
            >
              <VImg v-if="item.profileImageUrl" :src="item.profileImageUrl" />
              <span v-else>{{ item.applicantName }}</span>
            </VAvatar>
          </VBadge>

          <div class="d-flex flex-column align-start ms-3">
            <span
              class="d-block font-weight-bold text-body-1 text-high-emphasis text-truncate"
              >{{ item.applicantName }}</span
            >
            <small>
              <v-chip size="x-small">
                <v-icon size="small" class="mr-2"> mdi-domain</v-icon
                >{{ item.applicantCity }}</v-chip
              >
            </small>
          </div>
        </div>
      </template>

      <!-- Status column -->
      <template #[`item.status`]="{ item }">
        <VChip
          :color="getStatusColor(item.status)"
          size="small"
          class="text-capitalize"
        >
          {{ item.status }}
        </VChip>
      </template>

      <!-- Actions column -->
      <template #[`item.actions`]="{ item }">
        <div class="d-flex gap-2">
          <v-btn
            @click="(event: Event) => updateStatus(item, 'CONFIRMED', event)"
            size="x-small"
            :disabled="item.status === 'confirmed'"
            color="success"
            icon="mdi-check"
            variant="tonal"
          >
            <v-icon>mdi-check</v-icon>
            <v-tooltip activator="parent" location="top">{{
              $t('Confirm')
            }}</v-tooltip>
          </v-btn>

          <v-btn
            @click="(event: Event) => removeAppointment(item, event)"
            size="x-small"
            :disabled="item.status === 'rejected' || item.status === 'canceled'"
            color="error"
            icon="mdi-delete"
            variant="tonal"
          >
            <v-icon>mdi-delete</v-icon>
            <v-tooltip activator="parent" location="top">{{
              $t('Reject')
            }}</v-tooltip>
          </v-btn>

          <v-btn
            size="x-small"
            color="default"
            icon="mdi-dots-vertical"
            variant="text"
            v-menu:menu
          >
            <v-icon>mdi-dots-vertical</v-icon>
            <v-menu activator="parent" location="bottom end">
              <v-list density="compact">
                <v-list-item
                  @click="
                    (event: Event) => updateStatus(item, 'CANCELED', event)
                  "
                  v-if="item.status !== 'CANCELED'"
                >
                  <v-list-item-title>{{ $t('Cancel') }}</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="event => goToApplicantDetails(event, item)"
                >
                  <v-list-item-title>{{
                    $t('View Profile')
                  }}</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="event => goToApplicantDetails(event, item)"
                >
                  <v-list-item-title>{{
                    $t('Send Message')
                  }}</v-list-item-title>
                </v-list-item>
                <v-list-item @click="() => {}">
                  <v-list-item-title>{{ $t('Rate') }}</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="(event: Event) => rejectAppointment(item, event)"
                  v-if="item.status !== 'REJECTED'"
                >
                  <v-list-item-title>{{ $t('Reject') }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-btn>
        </div>
      </template>

      <!-- Pagination -->
      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalItems) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalItems / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : $vuetify.display.md ? 5 : 7
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ $t('Previous') }}
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ $t('Next') }}
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  .appointment-status-confirmed {
    color: var(--v-success-base);
  }
  .appointment-status-pending {
    color: var(--v-warning-base);
  }
  .appointment-status-cancelled {
    color: var(--v-error-base);
  }
</style>
