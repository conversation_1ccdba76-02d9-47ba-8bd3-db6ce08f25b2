<script setup lang="ts">
  import { useDynamicLinks } from '@/composables/useDynamicLinks'

  interface Emit {
    (e: 'onClose', close: boolean): void
  }

  const props = defineProps({
    open: <PERSON><PERSON><PERSON>,
    url: String,
    jobAdId: String,
  })

  const {
    state: { jobAdLinkLoading },
    actions: { getDynamicLinkForJobAd },
  } = useDynamicLinks()

  const copied = ref(false)

  const fetchedLink = ref('')

  const close = false

  const emit = defineEmits<Emit>()

  watch(
    () => props.open,
    async newVal => {
      if (newVal && !props.url) {
        fetchedLink.value = await getDynamicLinkForJobAd(props?.jobAdId)
      }
    },
    { immediate: true },
  )

  let jobAdvertLink = ref('')

  watchEffect(() => {
    jobAdvertLink.value =
      props.url && props.url.trim() !== '' ? props.url : fetchedLink.value
  })

  const copyToClipboard = () => {
    navigator.clipboard.writeText(jobAdvertLink.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 1500)
  }
</script>

<template>
  <v-row justify="center">
    <v-col cols="auto">
      <v-dialog
        transition="dialog-top-transition"
        width="450"
        :persistent="false"
        v-model="props.open"
      >
        <template v-slot:default="{ isActive }">
          <v-card>
            <v-toolbar density="compact" color="primary" class="px-2">
              <v-icon icon="tabler-copy" />
              <v-toolbar-title>Copy Job Advert Link</v-toolbar-title>
              <v-spacer></v-spacer>
              <v-icon @click="() => emit('onClose', close)" icon="tabler-x" />
            </v-toolbar>
            <v-card-text>
              <div class="text-center">
                <h3>Link zu Ihrer Anzeige kopieren</h3>
                <p>
                  Dies ist ein spezieller Link, der direkt auf Ihre
                  Ausbildungsstelle in der App verlinkt. Wird er auf einem
                  Smartphone angeklickt, öffnet sich automatisch die Bridge-App
                  und Ihre Stellenanzeige wird angezeigt. Dies funktioniert auch
                  dann, wenn die App vorher noch nicht installiert war
                </p>
              </div>

              <v-tooltip top>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    :loading="jobAdLinkLoading"
                    :disabled="jobAdLinkLoading"
                    :placeholder="jobAdLinkLoading ? 'Generating link ...' : ''"
                    v-bind="attrs"
                    v-on="on"
                    :value="
                      jobAdLinkLoading ? 'Generating link ...' : jobAdvertLink
                    "
                    outlined
                    readonly
                    :append-icon="copied ? 'tabler-check' : 'tabler-copy'"
                    @click:append="copyToClipboard"
                  ></v-text-field>
                </template>
                <span>Copy to clipboard</span>
              </v-tooltip>
            </v-card-text>
          </v-card>
        </template>
      </v-dialog>
    </v-col>
  </v-row>
</template>

<style scoped lang="scss"></style>
