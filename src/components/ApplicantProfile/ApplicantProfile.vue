<script lang="ts" setup>
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
  import { useJobActions } from '@/composables/JobActions/useJobActions'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import {
    confirmMatchDecline,
    confirmAppointmentReject,
  } from '@/composables/useSweetAlert'
  import { useRoute } from 'vue-router'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useTheme } from 'vuetify'
  import { useAppointments } from '@/composables/Appointments/useAppointments'
  import { useFilterStore } from '@/stores/fairs/filterStore'

  const isDarkLayout = useTheme().global.current
  const dark = isDarkLayout.value.dark

  const appStore = useAppStore()
  const filterStore = useFilterStore()
  const appLoader = computed(() => appStore.showLoader)

  const appointment = computed(() => filterStore.activeAppointment)

  const {
    state: { isFairAppointmentModule, loadingStatusUpdate },
    actions: { handleUpdateAppointmentStatus },
  } = useAppointments()

  const {
    state: {
      personalMarks,
      environmentMarks,
      personalityValue,
      environmentValue,
      applicant,
      loadingApplicant,
    },
    actions: { handleApplicantDelete },
  } = useApplicantProfile()

  const {
    state: { isDecliningMatchedApplicant },
    actions: { handleDeclineMatchedApplicant },
  } = useJobActions()

  const {
    actions: { refetchLikes },
  } = useJobAdverts()

  const route = useRoute()
  const router = useRouter()

  const appointmentId = computed(() => route.params.id as string)

  const jobActionState = ref(route.query?.status || '')

  const onRejectApplicant = async () => {
    const confirmBlock = await confirmMatchDecline(dark)

    if (confirmBlock.isConfirmed) {
      try {
        appStore.showAppLoader()
        await handleDeclineMatchedApplicant('', confirmBlock.value)
        appStore.showSnack('Bewerber wurde abgelehnt')
        await refetchLikes()
      } catch (e) {
        appStore.hideAppLoader()
      } finally {
        appStore.hideAppLoader()
        router.go(-1)
      }
    }
  }

  const updateStatus = async (newStatus: string) => {
    try {
      await handleUpdateAppointmentStatus(appointmentId.value, newStatus)
      appStore.showSnack('Terminstatus aktualisiert')
    } catch (error) {
      console.log(error)
      appStore.showSnack("Couldn't update status")
    }
  }

  const rejectAppointment = async () => {
    try {
      // Show SweetAlert dialog for reject reason
      const result = await confirmAppointmentReject(dark)

      if (result.isConfirmed) {
        const rejectReason = result.value || undefined
        await handleUpdateAppointmentStatus(
          appointmentId.value,
          'REJECTED',
          rejectReason,
        )
        appStore.showSnack('Appointment successfully rejected')
      }
    } catch (error) {
      console.log(error)
      appStore.showSnack('Error rejecting appointment')
    }
  }

  const authStore = useAuthStore()

  const isSuper = authStore.isSuperUser
</script>

<template>
  <v-skeleton-loader
    v-if="loadingApplicant"
    class="mx-auto"
    elevation="2"
    type="paragraph, article, sentences,  button"
  ></v-skeleton-loader>
  <VCard v-else class="mb-4" :disabled="appLoader">
    <VCardText>
      <!-- Applicant data loaded on mount -->
      <p class="font-weight-bold">
        {{ $t('About Me') }}
      </p>
      <p>
        {{ applicant?.description }}
      </p>

      <VRow>
        <VCol cols="6">
          <p class="font-weight-bold mt-5">
            {{ $t('School') }}
          </p>
          <p>{{ applicant?.schoolName }}</p>
        </VCol>
        <VCol cols="6">
          <p class="font-weight-bold mt-5">
            {{ $t('Targeted SA') }}
          </p>
          <p>{{ applicant?.graduation }}</p>
        </VCol>
      </VRow>

      <!--      Subjects List -->
      <div v-if="applicant?.subjects">
        <p class="font-weight-bold mt-5">
          {{ $t('Favorite Subjects') }}
        </p>
        <div>
          <VChip
            v-for="item in applicant?.subjects"
            :key="item"
            class="ma-2"
            label
          >
            <VIcon start icon="tabler-school" class="mr-2" />

            {{ item }}
          </VChip>
        </div>
      </div>

      <!--      Strenghts List -->
      <div v-if="applicant?.strengths">
        <p class="font-weight-bold mt-5">
          {{ $t('Strength') }}
        </p>
        <div>
          <VChip
            v-for="item in applicant?.strengths"
            :key="item"
            class="ma-2"
            label
          >
            <VIcon start icon="tabler-bulb" class="mr-2" />

            {{ item }}
          </VChip>
        </div>
      </div>

      <!--      Weakness List -->
      <div v-if="applicant?.weaknesses">
        <p class="font-weight-bold mt-5">
          {{ $t('Weakness') }}
        </p>
        <div>
          <VChip
            v-for="item in applicant?.weaknesses"
            :key="item"
            class="ma-2"
            label
          >
            <VIcon start icon="tabler-unlink" class="mr-2" />

            {{ item }}
          </VChip>
        </div>
      </div>

      <!--      How applicant prefers to work -->
      <div v-if="applicant?.personality" class="mt-5">
        <p class="font-weight-bold">
          {{ $t('Prefer Working') }}
        </p>
        <div>
          <VRow>
            <VCol cols="12">
              <VSlider
                v-model="personalityValue"
                :ticks="personalMarks"
                :max="5"
                step="1"
                show-ticks="always"
                tick-size="2"
                :readonly="true"
              />
            </VCol>
          </VRow>
        </div>
      </div>
      <VSpacer class="py-2" />

      <!--      Professional Environmen -->
      <div v-if="applicant?.environment" class="mt-5">
        <p class="font-weight-bold">
          {{ $t('Professional Environment') }}
        </p>
        <div>
          <VRow>
            <VCol cols="12">
              <VSlider
                v-model="environmentValue"
                :ticks="environmentMarks"
                :max="5"
                step="1"
                show-ticks="always"
                tick-size="2"
                :readonly="true"
              />
            </VCol>
          </VRow>
        </div>
      </div>
    </VCardText>
    <v-card-text v-if="isFairAppointmentModule" class="mt-8">
      <div class="text-center">
        <VBtn
          :loading="
            appointment?.status === 'rejected' ||
            appointment?.status === 'canceled'
              ? loadingStatusUpdate
              : false
          "
          :disabled="loadingStatusUpdate || appointment?.status === 'confirmed'"
          @click="updateStatus('CONFIRMED')"
          color="success"
        >
          <VIcon class="mr-2" icon="tabler-check" />
          {{ $t('Confirm') }}
        </VBtn>
        <VBtn
          class="mx-2"
          variant="outlined"
          :loading="
            appointment?.status === 'confirmed' ? loadingStatusUpdate : false
          "
          :disabled="
            loadingStatusUpdate ||
            appointment?.status === 'rejected' ||
            appointment?.status === 'canceled'
          "
          @click="rejectAppointment"
        >
          <VIcon class="mr-2" icon="tabler-trash" />
          {{ $t('Reject') }}
        </VBtn>
      </div>
    </v-card-text>
    <v-card-text class="mt-8" v-else>
      <div class="text-center">
        <VBtn v-if="isSuper" @click="handleApplicantDelete">
          <VIcon class="mr-2" icon="tabler-trash" />
          {{ $t('Delete Applicant') }}
        </VBtn>
        <VBtn
          :loading="isDecliningMatchedApplicant"
          :disabled="
            isDecliningMatchedApplicant || jobActionState === 'DELETED'
          "
          v-else
          @click="onRejectApplicant"
        >
          <VIcon class="mr-2" icon="tabler-trash" />
          {{ $t('Reject And Delete') }}
        </VBtn>
      </div>
    </v-card-text>
  </VCard>

  <VCard
    v-if="
      applicant?.applicantDocuments?.length &&
      applicant.applicantDocuments.length > 0
    "
    class="p-4"
  >
    <VCardText>
      <p class="font-weight-bold">
        {{ $t('My Documents') }}
      </p>
      <VRow>
        <VCol v-for="document in applicant?.applicantDocuments" cols="6">
          <a
            :key="document?.name + document?.url"
            :href="document?.url"
            target="_blank"
          >
            <VCard class="mx-auto" max-width="344">
              <VImg :src="document?.documentPreviewUrl" height="200px" cover />

              <VCardTitle>
                {{ document?.name }}
              </VCardTitle>
            </VCard>
          </a>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
  .card-list {
    --v-card-list-gap: 16px;
  }
</style>
