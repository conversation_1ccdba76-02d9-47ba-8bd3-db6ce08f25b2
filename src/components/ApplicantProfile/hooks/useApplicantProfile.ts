import { confirmApplicantDelete } from '@/composables/useSweetAlert'
import LikeMatch from '@/types/like-match'
import { computed } from 'vue'
import { inlineTranslate } from '@/utils/utils'
import { useApplicantGraph } from '@/api/graphHooks/useApplicantGraph'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/appStore'

interface Marks {
  [key: number]: string
}

export const useApplicantProfile = () => {
  const {
    actions: {
      refetchApplicant,
      refetchAllApplicants,
      deleteApplicant,
      loadAllApplicants,
      loadPaginatedApplicants,
      updatePaginationParams,
    },
    state: {
      applicant,
      loadingApplicantById: loadingApplicant,
      allApplicantsList,
      loadingAllApplicants,
      paginatedApplicants,
      loadingPaginatedApplicants,
    },
  } = useApplicantGraph()

  const router = useRouter()

  const route = useRoute()

  const appStore = useAppStore()

  const applicantId: string = route.params.id as string

  const personalMarks = computed<Marks>(() => {
    return {
      0: inlineTranslate('Alone'),
      1: '.',
      2: inlineTranslate('Neutral'),
      3: '.',
      4: inlineTranslate('In a Team'),
    }
  })

  const environmentMarks = computed<Marks>(() => {
    return {
      0: inlineTranslate('Inside'),
      1: '.',
      2: inlineTranslate('No Matter'),
      3: '.',
      4: inlineTranslate('Outside'),
    }
  })

  const statusArray = computed(() => {
    return [
      inlineTranslate('Hired'),
      inlineTranslate('Cancelled'),
      inlineTranslate('Invited'),
      inlineTranslate('Telephoned'),
    ]
  })

  const personalityValue = computed<number>(() => {
    const defaultPersonality = 0

    return (applicant.value?.personality || defaultPersonality) - 1
  })

  const environmentValue = computed<number>(() => {
    const defaultEnvironment = 0

    return (applicant.value?.environment || defaultEnvironment) - 1
  })

  const allApplicants = computed(() => {
    return (
      allApplicantsList.value?.map(
        (like: {
          firstName: string
          lastName: string
          birthDate: string
          city: string
          id: string
          profileImageUrl: string
        }) => {
          return {
            applicantId: like?.id,
            name: like.firstName + '' + like.lastName,
            birthday: like.birthDate,
            city: like.city,
            image: like.profileImageUrl,
          }
        },
      ) ?? []
    )
  })

  const paginatedApplicantsList = computed(() => {
    return (
      paginatedApplicants.value?.items?.map(
        (like: {
          firstName: string
          lastName: string
          birthDate: string
          city: string
          id: string
          profileImageUrl: string
        }) => {
          return {
            applicantId: like?.id,
            name: (like.firstName || '') + ' ' + (like.lastName || ''),
            birthday: like.birthDate,
            city: like.city,
            image: like.profileImageUrl,
          }
        },
      ) ?? []
    )
  })

  const handleApplicantClicked = async (applicant: LikeMatch) => {
    if (appStore.isInCompanyView) {
      await router.push({
        name: 'company-job-ads-id-applicants-uid',
        params: {
          id: route.params.id,
          uid: applicant.applicantId,
        },
      })
      return
    } else {
      await router.push({
        name: 'super-applicant-id',
        params: {
          id: applicant.applicantId,
        },
      })
    }
  }

  const handleApplicantDelete = async (dark: boolean) => {
    try {
      const deleteAlert = await confirmApplicantDelete(dark)

      if (deleteAlert?.isConfirmed) {
        const deletedApplicant = await deleteApplicant({
          applicantId: applicantId,
        })
        await router.push({
          name: 'super-applicant',
        })
      }
    } catch (e) {
      console.log(e)
    }
  }

  const handleSearch = async (
    search: string = '',
    page: number = 1,
    limit: number = 10,
  ) => {
    await updatePaginationParams(page, limit, search)
  }

  return {
    state: {
      personalMarks,
      environmentMarks,
      personalityValue,
      environmentValue,
      statusArray,
      applicant,
      loadingApplicant,
      allApplicants,
      paginatedApplicantsList,
      paginatedApplicants,
      loadingAllApplicants,
      loadingPaginatedApplicants,
    },
    actions: {
      refetchApplicant,
      handleApplicantDelete,
      refetchAllApplicants,
      handleApplicantClicked,
      loadAllApplicants,
      loadPaginatedApplicants,
      handleSearch,
      updatePaginationParams,
    },
  }
}
