<script setup lang="ts">
  import { usePricingPlan } from '@/composables/PricingPlan/usePricingPlan'
  import { useAppStore } from '@/stores/appStore'
  import { computed, ref, onMounted } from 'vue'

  const appStore = useAppStore()

  const {
    state: {
      allPlans,
      loadingAllPlans,
      creatingPlan,
      updatingPlan,
      deactivatingPlan,
    },
    actions: {
      fetchAllPlans,
      createPricingPlan,
      updatePricingPlan,
      deactivatePricingPlan,
      refetchAllPlans,
    },
  } = usePricingPlan()

  // Dialog states
  const showCreateDialog = ref(false)
  const showEditDialog = ref(false)
  const showDeleteDialog = ref(false)

  // Form data
  const formData = ref({
    name: '',
    description: '',
    type: 'STANDARD',
    basePrice: 0,
    minimumSubscriptionLength: 1,
    jobAdvertLimit: 10,
    features: [''],
    isActive: true,
    prices: [
      { billingPeriod: 'MONTHLY', amount: 0 },
      { billingPeriod: 'QUARTERLY', amount: 0 },
      { billingPeriod: 'YEARLY', amount: 0 },
    ],
  })

  const editingPlan = ref<any>(null)
  const deletingPlan = ref<any>(null)

  // Plan types
  const planTypes = [
    { title: 'Standard', value: 'STANDARD' },
    { title: 'Enterprise', value: 'ENTERPRISE' },
    { title: 'Custom', value: 'CUSTOM' },
    { title: 'Promotional', value: 'PROMOTIONAL' },
  ]

  // Form validation
  const formRules = {
    required: (v: any) => !!v || 'This field is required',
    positiveNumber: (v: any) => v >= 0 || 'Must be a positive number',
    jobLimit: (v: any) => v >= -1 || 'Use -1 for unlimited',
  }

  // Reset form
  const resetForm = () => {
    formData.value = {
      name: '',
      description: '',
      type: 'STANDARD',
      basePrice: 0,
      minimumSubscriptionLength: 1,
      jobAdvertLimit: 10,
      features: [''],
      isActive: true,
      prices: [
        { billingPeriod: 'MONTHLY', amount: 0 },
        { billingPeriod: 'QUARTERLY', amount: 0 },
        { billingPeriod: 'YEARLY', amount: 0 },
      ],
    }
    editingPlan.value = null
  }

  // Add feature
  const addFeature = () => {
    formData.value.features.push('')
  }

  // Remove feature
  const removeFeature = (index: number) => {
    formData.value.features.splice(index, 1)
  }

  // Open create dialog
  const openCreateDialog = () => {
    resetForm()
    showCreateDialog.value = true
  }

  // Open edit dialog
  const openEditDialog = (plan: any) => {
    editingPlan.value = plan

    // Prefill prices by aggregating other plans with the same name
    const siblingPlans = (allPlans.value || []).filter(
      (p: any) => p.name === plan.name,
    )
    const priceMap: Record<string, number> = {}
    siblingPlans.forEach((p: any) => {
      if (p?.billingPeriod && typeof p.price === 'number') {
        priceMap[p.billingPeriod] = p.price
      }
    })

    formData.value = {
      name: plan.name,
      description: plan.description || '',
      type: plan.planType || 'STANDARD',
      basePrice: typeof plan.price === 'number' ? plan.price : 0,
      minimumSubscriptionLength: plan.durationDays
        ? Math.max(1, Math.round(plan.durationDays / 30))
        : 1,
      jobAdvertLimit: plan.unlimitedJobAdverts ? -1 : 10,
      features: [],
      isActive: plan.isActive,
      prices: [
        {
          billingPeriod: 'MONTHLY',
          amount:
            priceMap['MONTHLY'] ??
            (typeof plan.price === 'number' ? plan.price : 0),
        },
        { billingPeriod: 'QUARTERLY', amount: priceMap['QUARTERLY'] ?? 0 },
        { billingPeriod: 'ANNUAL', amount: priceMap['ANNUAL'] ?? 0 },
      ],
    }
    showEditDialog.value = true
  }

  // Open delete dialog
  const openDeleteDialog = (plan: any) => {
    deletingPlan.value = plan
    showDeleteDialog.value = true
  }

  // Handle create plan
  const handleCreatePlan = async () => {
    try {
      const planData = {
        name: formData.value.name,
        displayName: formData.value.name,
        description: formData.value.description,
        price: Number(formData.value.basePrice),
        billingPeriod: formData.value.prices[0]?.billingPeriod || 'MONTHLY',
        durationDays: formData.value.minimumSubscriptionLength * 30,
        planType: formData.value.type,
        isActive: formData.value.isActive,
        unlimitedJobAdverts: formData.value.jobAdvertLimit === -1,
        hasCustomBranding: false,
        displayOrder: 0,
      }

      const result = await createPricingPlan(planData)

      if (result) {
        appStore.showSnack('Pricing plan created successfully')
        showCreateDialog.value = false
        resetForm()
        await refetchAllPlans()
      } else {
        throw new Error('Failed to create pricing plan')
      }
    } catch (error) {
      console.error('Create plan error:', error)
      appStore.showSnack('Failed to create pricing plan')
    }
  }

  // Handle update plan
  const handleUpdatePlan = async () => {
    if (!editingPlan.value) return

    try {
      const planData = {
        id: editingPlan.value.id,
        name: formData.value.name,
        displayName: formData.value.name,
        description: formData.value.description,
        price: Number(formData.value.basePrice),
        durationDays: formData.value.minimumSubscriptionLength * 30,
        planType: formData.value.type,
        isActive: formData.value.isActive,
        unlimitedJobAdverts: formData.value.jobAdvertLimit === -1,
      }

      const result = await updatePricingPlan(planData)

      if (result) {
        appStore.showSnack('Pricing plan updated successfully')
        showEditDialog.value = false
        resetForm()
        await refetchAllPlans()
      } else {
        throw new Error('Failed to update pricing plan')
      }
    } catch (error) {
      console.error('Update plan error:', error)
      appStore.showSnack('Failed to update pricing plan')
    }
  }

  // Handle deactivate plan
  const handleDeactivatePlan = async () => {
    if (!deletingPlan.value) return

    try {
      const result = await deactivatePricingPlan(deletingPlan.value.id)

      if (result) {
        appStore.showSnack('Pricing plan deactivated successfully')
        showDeleteDialog.value = false
        deletingPlan.value = null
        await refetchAllPlans()
      } else {
        throw new Error('Failed to deactivate pricing plan')
      }
    } catch (error) {
      console.error('Deactivate plan error:', error)
      appStore.showSnack('Failed to deactivate pricing plan')
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount)
  }

  // Get plan badge color
  const getPlanBadgeColor = (type: string) => {
    switch (type) {
      case 'STANDARD':
        return 'info'
      case 'ENTERPRISE':
        return 'warning'
      case 'CUSTOM':
        return 'success'
      case 'PROMOTIONAL':
        return 'primary'
      default:
        return 'grey'
    }
  }

  // Get billing interval display text
  const getBillingIntervalDisplay = (plan: any) => {
    // Calculate months from durationDays
    const months = plan.durationDays ? Math.max(1, Math.round(plan.durationDays / 30)) : 1
    
    if (months === 1) return 'Monthly'
    if (months === 3) return 'Quarterly'
    if (months === 6) return 'Semi-Annual'
    if (months === 12) return 'Annual'
    return `Every ${months} months`
  }

  // Get actual total price for display
  const getTotalPrice = (plan: any) => {
    if (!plan.price) return 0
    
    // Calculate months from durationDays
    const months = plan.durationDays ? Math.max(1, Math.round(plan.durationDays / 30)) : 1
    
    // The price stored is the base monthly price
    // Total price = base price * months
    return plan.price * months
  }

  // Get price display with breakdown
  const getPriceDisplay = (plan: any) => {
    const months = plan.durationDays ? Math.max(1, Math.round(plan.durationDays / 30)) : 1
    const total = getTotalPrice(plan)
    
    if (months === 1) {
      return formatCurrency(plan.price || 0)
    } else {
      return `${formatCurrency(total)} (€${plan.price}/mo × ${months})`
    }
  }

  // Computed properties for plan summary
  const totalPrice = computed(() => {
    return formData.value.basePrice * formData.value.minimumSubscriptionLength
  })

  const billingIntervalText = computed(() => {
    const months = formData.value.minimumSubscriptionLength
    if (months === 1) return 'Monthly'
    if (months === 3) return 'Every 3 months'
    if (months === 6) return 'Every 6 months'
    if (months === 12) return 'Annually'
    return `Every ${months} month${months > 1 ? 's' : ''}`
  })

  const planSummary = computed(() => {
    const base = formData.value.basePrice
    const months = formData.value.minimumSubscriptionLength
    const total = base * months
    
    return {
      basePrice: base,
      months: months,
      totalPrice: total,
      billingInterval: billingIntervalText.value,
      description: `Customers will be charged €${total.toFixed(2)} ${billingIntervalText.value.toLowerCase()}`,
    }
  })

  onMounted(async () => {
    await fetchAllPlans()
  })
</script>

<template>
  <VContainer>
    <!-- Header -->
    <VRow class="mb-6">
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h2 class="text-h4 mb-2">Pricing Plan Management</h2>
            <p class="text-body-2 text-medium-emphasis">
              Manage subscription plans and pricing
            </p>
          </div>
          <VBtn
            color="primary"
            prepend-icon="mdi-plus"
            @click="openCreateDialog"
          >
            Create Plan
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <!-- Loading State -->
    <div v-if="loadingAllPlans" class="text-center py-12">
      <VProgressCircular indeterminate color="primary" :size="60" />
      <p class="mt-4">Loading pricing plans...</p>
    </div>

    <!-- Plans Table -->
    <VCard v-else elevation="2" class="pa-4">
      <VDataTable
        :items="allPlans"
        :headers="[
          { title: 'Name', key: 'name' },
          { title: 'Type', key: 'planType' },
          { title: 'Total Price', key: 'price' },
          { title: 'Billing Interval', key: 'billingPeriod' },
          { title: 'Status', key: 'isActive' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        items-per-page="10"
      >
        <template #item.planType="{ item }">
          <VChip :color="getPlanBadgeColor(item.planType)" size="small" label>
            {{ item.planType }}
          </VChip>
        </template>

        <template #item.price="{ item }">
          <div>
            <div class="font-weight-bold">{{ formatCurrency(getTotalPrice(item)) }}</div>
            <div v-if="item.durationDays && item.durationDays > 30" class="text-caption text-medium-emphasis">
              {{ formatCurrency(item.price) }}/mo × {{ Math.round(item.durationDays / 30) }}
            </div>
          </div>
        </template>

        <template #item.billingPeriod="{ item }">
          <VChip size="small" variant="tonal">
            {{ getBillingIntervalDisplay(item) }}
          </VChip>
        </template>

        <template #item.isActive="{ item }">
          <VChip
            :color="item.isActive ? 'success' : 'error'"
            size="small"
            label
          >
            {{ item.isActive ? 'Active' : 'Inactive' }}
          </VChip>
        </template>

        <template #item.actions="{ item }">
          <VBtn
            icon="mdi-pencil"
            size="small"
            variant="text"
            @click="openEditDialog(item)"
          />
          <VBtn
            icon="mdi-delete"
            size="small"
            variant="text"
            color="error"
            :disabled="!item.isActive"
            @click="openDeleteDialog(item)"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- Create Dialog -->
    <VDialog v-model="showCreateDialog" max-width="800" persistent>
      <VCard class="pa-4">
        <VCardTitle>
          <span class="text-h5">Create Pricing Plan</span>
        </VCardTitle>

        <VCardText>
          <VForm @submit.prevent="handleCreatePlan">
            <VRow>
              <VCol cols="12" md="6">
                <VTextField
                  v-model="formData.name"
                  label="Plan Name"
                  :rules="[formRules.required]"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12" md="6">
                <VSelect
                  v-model="formData.type"
                  label="Plan Type"
                  :items="planTypes"
                  variant="outlined"
                  required
                />
              </VCol>
              <VCol cols="12" md="6">
                <VTextField
                  v-model.number="formData.basePrice"
                  label="Base Monthly Price"
                  type="number"
                  :rules="[formRules.required, formRules.positiveNumber]"
                  variant="outlined"
                  prefix="€"
                  hint="Price per month (will be multiplied by minimum subscription length)"
                  persistent-hint
                  required
                />
              </VCol>

              <VCol cols="12" md="6">
                <VTextField
                  v-model.number="formData.minimumSubscriptionLength"
                  label="Minimum Subscription Length"
                  type="number"
                  :rules="[formRules.required, formRules.positiveNumber]"
                  variant="outlined"
                  suffix="months"
                  hint="Number of months for billing cycle"
                  persistent-hint
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  v-model="formData.description"
                  label="Description"
                  rows="2"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VSwitch
                  v-model="formData.isActive"
                  label="Active"
                  color="primary"
                />
              </VCol>

              <!-- Plan Summary -->
              <VCol cols="12">
                <VAlert
                  type="info"
                  variant="tonal"
                  class="mt-4"
                  prominent
                >
                  <VAlertTitle>Plan Summary</VAlertTitle>
                  <div class="mt-3">
                    <div class="d-flex justify-space-between mb-2">
                      <span>Base Monthly Price:</span>
                      <strong>€{{ planSummary.basePrice.toFixed(2) }}</strong>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span>Minimum Subscription:</span>
                      <strong>{{ planSummary.months }} month{{ planSummary.months > 1 ? 's' : '' }}</strong>
                    </div>
                    <VDivider class="my-2" />
                    <div class="d-flex justify-space-between mb-2">
                      <span class="text-h6">Total Price:</span>
                      <strong class="text-h6 text-primary">€{{ planSummary.totalPrice.toFixed(2) }}</strong>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span>Billing Interval:</span>
                      <strong>{{ planSummary.billingInterval }}</strong>
                    </div>
                    <VDivider class="my-2" />
                    <p class="text-body-2 mb-0 text-medium-emphasis">
                      {{ planSummary.description }}
                    </p>
                  </div>
                </VAlert>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn variant="text" @click="showCreateDialog = false"> Cancel </VBtn>
          <VBtn
            color="primary"
            variant="flat"
            :loading="creatingPlan"
            :disabled="creatingPlan"
            @click="handleCreatePlan"
          >
            Create Plan
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Edit Dialog -->
    <VDialog v-model="showEditDialog" max-width="800" persistent>
      <VCard>
        <VCardTitle>
          <span class="text-h5">Edit Pricing Plan</span>
        </VCardTitle>

        <VCardText>
          <VForm @submit.prevent="handleUpdatePlan">
            <VRow>
              <VCol cols="12" md="6">
                <VTextField
                  v-model="formData.name"
                  label="Plan Name"
                  :rules="[formRules.required]"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12" md="6">
                <VSelect
                  v-model="formData.type"
                  label="Plan Type"
                  :items="planTypes"
                  variant="outlined"
                  required
                  :disabled="true"
                />
              </VCol>

              <VCol cols="12" md="6">
                <VTextField
                  v-model.number="formData.basePrice"
                  label="Base Monthly Price"
                  type="number"
                  :rules="[formRules.required, formRules.positiveNumber]"
                  variant="outlined"
                  prefix="€"
                  hint="Price per month (will be multiplied by minimum subscription length)"
                  persistent-hint
                  required
                />
              </VCol>

              <VCol cols="12" md="6">
                <VTextField
                  v-model.number="formData.minimumSubscriptionLength"
                  label="Minimum Subscription Length"
                  type="number"
                  :rules="[formRules.required, formRules.positiveNumber]"
                  variant="outlined"
                  suffix="months"
                  hint="Number of months for billing cycle"
                  persistent-hint
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  v-model="formData.description"
                  label="Description"
                  rows="2"
                  variant="outlined"
                  required
                />
              </VCol>
              <VCol cols="12">
                <VSwitch
                  v-model="formData.isActive"
                  label="Active"
                  color="primary"
                />
              </VCol>

              <!-- Plan Summary -->
              <VCol cols="12">
                <VAlert
                  type="info"
                  variant="tonal"
                  class="mt-4"
                  prominent
                >
                  <VAlertTitle>Plan Summary</VAlertTitle>
                  <div class="mt-3">
                    <div class="d-flex justify-space-between mb-2">
                      <span>Base Monthly Price:</span>
                      <strong>€{{ planSummary.basePrice.toFixed(2) }}</strong>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span>Minimum Subscription:</span>
                      <strong>{{ planSummary.months }} month{{ planSummary.months > 1 ? 's' : '' }}</strong>
                    </div>
                    <VDivider class="my-2" />
                    <div class="d-flex justify-space-between mb-2">
                      <span class="text-h6">Total Price:</span>
                      <strong class="text-h6 text-primary">€{{ planSummary.totalPrice.toFixed(2) }}</strong>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                      <span>Billing Interval:</span>
                      <strong>{{ planSummary.billingInterval }}</strong>
                    </div>
                    <VDivider class="my-2" />
                    <p class="text-body-2 mb-0 text-medium-emphasis">
                      {{ planSummary.description }}
                    </p>
                  </div>
                </VAlert>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn variant="text" @click="showEditDialog = false"> Cancel </VBtn>
          <VBtn
            color="primary"
            variant="flat"
            :loading="updatingPlan"
            :disabled="updatingPlan"
            @click="handleUpdatePlan"
          >
            Update Plan
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Delete Dialog -->
    <VDialog v-model="showDeleteDialog" max-width="500">
      <VCard>
        <VCardTitle>
          <span class="text-h5">Deactivate Pricing Plan</span>
        </VCardTitle>

        <VCardText>
          <VAlert type="warning" variant="tonal" class="mb-4">
            This will deactivate the plan. Existing subscriptions will continue
            until their expiry.
          </VAlert>

          <p>
            Are you sure you want to deactivate the
            <strong>{{ deletingPlan?.name }}</strong> plan?
          </p>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn variant="text" @click="showDeleteDialog = false"> Cancel </VBtn>
          <VBtn
            color="error"
            variant="flat"
            :loading="deactivatingPlan"
            :disabled="deactivatingPlan"
            @click="handleDeactivatePlan"
          >
            Deactivate
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VContainer>
</template>
