<script setup lang="ts">
  import { useFairStats } from '@/composables/FairStats/useFairStats'

  const {
    state: { statistics, loadingFairStats },
  } = useFairStats()

  const props = defineProps({
    title: {
      type: String,
      default: 'Fair Statistics',
    },
  })
</script>

<template>
  <VCard :title="title">
    <template #append>
      <span class="text-sm text-disabled">{{ $t('Real Time Data') }}</span>
    </template>

    <VCardText>
      <VRow>
        <VCol v-for="item in statistics" :key="item.title" cols="6" md="3">
          <div class="d-flex">
            <VAvatar size="70" class="me-3" :image="item.icon" />

            <div class="d-flex flex-column justify-center">
              <span v-if="loadingFairStats" class="text-h6 font-weight-medium">
                <v-progress-circular
                  :width="3"
                  color="primary"
                  indeterminate
                ></v-progress-circular>
              </span>
              <span v-else class="text-h5 font-weight-medium">{{
                item.stats
              }}</span>
              <span class="font-weight-medium">
                {{ item.title }}
              </span>
            </div>
          </div>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>
