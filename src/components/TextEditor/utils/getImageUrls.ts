import { Delta } from 'quill/core'

const getImgUrls = (delta: Delta): string[] => {
  return delta.ops
    .filter(i => {
      if (i.insert && typeof i.insert === 'object') {
        if (
          'image' in i.insert &&
          typeof i.insert.image === 'string' &&
          i.insert.image.startsWith('http')
        ) {
          return true
        } else if (
          'imageBlot' in i.insert &&
          typeof i.insert.imageBlot === 'string' &&
          i.insert.imageBlot.startsWith('http')
        ) {
          return true
        }
      }
      return false
    })
    .map(i => {
      const insert = i.insert as Record<string, unknown>
      if ('image' in insert) {
        return (i.insert as Record<string, string>).image
      } else if ('imageBlot' in insert) {
        return (i.insert as Record<string, string>).imageBlot
      }
      return ''
    })
    .filter(url => url !== '') // Entfernen Sie leere Strings aus dem Ergebnis
}

export { getImgUrls }
