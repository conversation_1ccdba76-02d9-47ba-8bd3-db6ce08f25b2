<template>
  <div>
    <div id="toolbar">
      <button class="ql-header">Überschrift</button>
      <button class="ql-bold">Bold</button>
      <button class="ql-italic">Italic</button>
      <button type="button" class="ql-list" value="bullet" />
      <button type="button" class="ql-image" />
    </div>
    <div ref="editorRef" class="quill-editor"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed, Ref, watch } from 'vue'
  import Quill from 'quill'
  import ImageUploader from './imageUploader/imageUploader'
  import 'quill/dist/quill.snow.css'
  import {
    deleteTextImage,
    uploadTextImage,
  } from '@/libs/firebase/upload-text-image'
  import { Delta } from 'quill/core'
  import { QuillOptions, Range } from 'quill'
  import './modules/quillModule'
  import { getImgUrls } from './utils/getImageUrls'
  import { useStaleImage } from '@/composables/StaleImage/useStaleImage'

  const props = defineProps({
    modelValue: {
      type: String,
      required: false,
      default:
        '<h1>Das Leitbild unseres Unternehmens: </h1><p>Lore ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.&nbsp;</p><p><br></p><h1>Dafür stehen wir:</h1><ul><li>Lorem ipsum dolor sit amet, consetetur</li><li>Lorem ipsum dolor sit amet, consetetur</li><li>Lorem ipsum dolor sit amet, consetetur</li></ul>',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    maxHeight: {
      type: String,
      required: false,
      default: 'unset',
    },
  })

  const emit = defineEmits<{
    (e: 'update:model-value', value: string): void
    (e: 'update:images-to-delete', value: string[]): void
    (e: 'update:stale-img-ids', value: string[]): void
  }>()

  const resetEditor = () => {
    staleImgages.value = []
    imagesToDelete.value = []
    editorImagesTemp.value = []
    onEditorChange()
  }

  defineExpose({
    resetEditor,
  })

  const {
    actions: { handleCreateStaleImage },
  } = useStaleImage()

  const imagesToDelete = ref<string[]>([])
  const staleImgages = ref<{ url: string; id: string }[]>([])
  const editorImagesTemp = ref<string[]>([])
  const editorRef = ref<HTMLDivElement | null>(null)
  let isInternalChange = false

  const onEditorChange = async () => {
    if (!quillRef.value) return

    const newUrls = getImgUrls(quillRef.value.getContents())

    const deletedUrls = editorImagesTemp.value.filter(x => !newUrls.includes(x))

    editorImagesTemp.value = newUrls

    for (const url of deletedUrls) {
      const index = staleImgages.value.findIndex(value => value.url === url)
      if (index > -1) {
        // Image was uploaded during edit
        // Remove from staleImages, so that this image gets deleted by the scheduler
        staleImgages.value.splice(index, 1)
      } else {
        // Image was already there and got deleted now
        imagesToDelete.value.push(url)
      }

      emit(
        'update:stale-img-ids',
        staleImgages.value.map(item => item.id),
      )
      emit('update:images-to-delete', imagesToDelete.value)
      emit('update:model-value', quillRef.value.getSemanticHTML())
    }

    emit(
      'update:stale-img-ids',
      staleImgages.value.map(item => item.id),
    )
    emit('update:images-to-delete', imagesToDelete.value)
    emit('update:model-value', quillRef.value.getSemanticHTML())
  }

  function matchHeadline(_node: any, delta: Delta) {
    return delta.compose(new Delta().retain(delta.length(), { header: 1 }))
  }

  const initQuill = () => {
    if (!editorRef.value) return null

    const options: QuillOptions = {
      theme: 'snow',
      modules: {
        toolbar: '#toolbar',
        clipboard: {
          matchers: [['H2', matchHeadline]],
          substituteBlockElements: false,
          allowed: {
            tags: ['a', 'b', 'strong', 'i', 'p', 'ul', 'ol', 'li', 'h1', 'h2'],
            attributes: ['href'],
          },
        },
        imageUploader: {
          upload: async (file: File) => {
            try {
              const url = await uploadTextImage(file)
              const staleId = await handleCreateStaleImage({ url })
              staleImgages.value.push({ id: staleId, url: url })
              emit(
                'update:stale-img-ids',
                staleImgages.value.map(item => item.id),
              )
              return url
            } catch (error) {
              console.log(error)
            }
          },
        },
        readOnly: props.disabled,
      },
    }
    Quill.register('modules/imageUploader', ImageUploader, true)
    const quill = new Quill(editorRef.value, options)
    //quill.setContents(quill.clipboard.convert({html: props.modelValue}))

    quill.on('text-change', onEditorChange)
    //onEditorChange()

    return quill
  }

  const setEditorContent = (content: string) => {
    const outputString = content
      .replace(/<\/?ul>/g, match => {
        return match === '<ul>' ? '<ol>' : '</ol>'
      })
      // Füge das Attribut data-list="bullet" zu allen <li>-Tags hinzu
      .replace(/<li(.*?)>/g, (match, attributes) => {
        return `<li${attributes} data-list="bullet">`
      })
    if (quillRef.value) quillRef.value.root.innerHTML = outputString
  }

  const quillRef: Ref<Quill | null> = ref(null)

  onMounted(() => {
    quillRef.value = initQuill()
    setEditorContent(props.modelValue)
  })
</script>

<style>
  .ql-editor li > .ql-ui:before {
    display: none !important;
  }

  .ql-editor li {
    list-style-type: disc;
    padding-left: 0.2em;
    position: relative;
  }

  .quill-editor.ql-container.ql-snow ol {
    margin-bottom: 20px;
  }

  .quill-editor.ql-container.ql-snow p {
    margin-bottom: 10px;
  }

  .quill-editor.ql-container.ql-snow h1:not(:first-child) {
    margin-top: 25px;
  }

  .ql-container {
    overflow: auto;
    block-size: fit-content;
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
    font-family: Montserrat, sans-serif;
    font-size: 1em;
    max-block-size: v-bind('props.maxHeight');
    min-block-size: 150px;
    padding-block-end: 20px;
  }

  .ql-snow .ql-editor h1 {
    font-size: 1.25em;
    margin-block-end: 12px;
  }

  .ql-toolbar.ql-snow {
    border-radius: 6px 6px 0 0;
    font-family: Montserrat, sans-serif;
  }

  .ql-snow .ql-picker.ql-header {
    inline-size: 115px;
  }

  .ql-snow .ql-header {
    width: 115px !important;
    font-weight: 600 !important;
    padding-left: 0 !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
    font-size: 1.25em;
    font-weight: 700;
  }

  .ql-container.ql-snow {
    border: 1px solid #d1d5db;
    border-radius: 0 0 6px 6px;
  }

  .ql-snow .ql-picker {
    color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
  }

  .ql-snow.ql-toolbar svg * {
    stroke: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
  }

  .ql-snow .ql-stroke {
    stroke-width: 1.5px;
  }

  .ql-snow.ql-toolbar button,
  .ql-snow .ql-toolbar button {
    border-radius: 4px;
    block-size: 24px;
    inline-size: 32px;
    padding-block: 0;
    padding-inline: 5px;
  }

  .ql-toolbar.ql-snow .ql-picker-label {
    border-radius: 4px;
  }

  .ql-snow.ql-toolbar button:hover,
  .ql-snow .ql-toolbar button:hover,
  .ql-snow.ql-toolbar button:focus,
  .ql-snow .ql-toolbar button:focus,
  .ql-snow.ql-toolbar .ql-picker-label:hover,
  .ql-snow .ql-toolbar .ql-picker-label:hover,
  .ql-snow.ql-toolbar .ql-picker-item:hover,
  .ql-snow .ql-toolbar .ql-picker-item:hover {
    background-color: rgb(var(--v-theme-primary), 0.6);
    color: rgb(var(--v-theme-on-primary));
  }

  .ql-snow.ql-toolbar button.ql-active,
  .ql-snow .ql-toolbar button.ql-active,
  .ql-snow.ql-toolbar .ql-picker-label.ql-active,
  .ql-snow .ql-toolbar .ql-picker-label.ql-active,
  .ql-snow.ql-toolbar .ql-picker-item.ql-selected,
  .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
    background-color: rgb(var(--v-theme-primary));
    color: rgb(var(--v-theme-on-primary));
  }

  .ql-snow.ql-toolbar button.ql-active .ql-stroke,
  .ql-snow .ql-toolbar button.ql-active .ql-stroke,
  .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
  .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
  .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
  .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
  .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
  .ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
  .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
  .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
  .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
  .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
  .ql-snow.ql-toolbar button:hover .ql-stroke,
  .ql-snow.ql-toolbar button:hover .ql-fill,
  .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke {
    stroke: rgb(var(--v-theme-on-primary));
  }

  .ql-snow .ql-picker-options {
    background-color: rgb(var(--v-theme-surface));
    color: rgb(var(--v-theme-on-surface), 0.8);
  }
</style>
