import Quill, { Range } from 'quill'
import LoadingImage from './blots/image'
import Toolbar from 'quill/modules/toolbar'
import './style.css'

class ImageUploader {
  quill: Quill
  options: any
  range: Range | null
  placeholderDelta: any
  fileHolder: HTMLInputElement | null = null

  constructor(quill: Quill, options: any) {
    this.quill = quill
    this.options = options
    this.range = null
    this.placeholderDelta = null

    if (typeof this.options.upload !== 'function') {
      console.warn(
        '[Missing config] upload function that returns a promise is required',
      )
    }

    const toolbar = this.quill.getModule('toolbar') as Toolbar
    if (toolbar) {
      toolbar.addHandler('image', this.selectLocalImage.bind(this))
    }
  }

  selectLocalImage() {
    this.quill.focus()
    this.range = this.quill.getSelection()
    this.fileHolder = document.createElement('input')
    this.fileHolder.setAttribute('type', 'file')
    this.fileHolder.setAttribute('accept', 'image/*')
    this.fileHolder.style.visibility = 'hidden'

    this.fileHolder.onchange = this.fileChanged.bind(this)

    document.body.appendChild(this.fileHolder)
    this.fileHolder.click()

    window.requestAnimationFrame(() => {
      if (this.fileHolder && this.fileHolder.parentNode) {
        document.body.removeChild(this.fileHolder)
      }
    })
  }

  fileChanged() {
    const file = this.fileHolder?.files ? this.fileHolder.files[0] : null
    if (file) {
      this.readAndUploadFile(file)
    }
  }

  readAndUploadFile(file: File) {
    let isUploadReject = false
    const fileReader = new FileReader()

    fileReader.addEventListener(
      'load',
      () => {
        if (!isUploadReject) {
          const base64ImageSrc = fileReader.result as string
          this.insertBase64Image(base64ImageSrc)
        }
      },
      false,
    )

    if (file) {
      fileReader.readAsDataURL(file)
    }

    this.options.upload(file).then(
      (imageUrl: string) => {
        this.insertToEditor(imageUrl)
      },
      (error: any) => {
        isUploadReject = true
        this.removeBase64Image()
        console.warn(error)
      },
    )
  }

  insertBase64Image(url: string) {
    const range = this.range
    if (range == null) return
    this.placeholderDelta = this.quill.insertEmbed(
      range.index,
      LoadingImage.blotName,
      `${url}`,
      'user',
    )
  }

  insertToEditor(url: string) {
    const range = this.range
    if (range == null) return
    const lengthToDelete = this.calculatePlaceholderInsertLength()

    const [blot] = this.quill.getLeaf(range.index)
    if (blot && blot.domNode instanceof HTMLElement) {
      LoadingImage.setLoaded(blot.domNode, url)
    } else {
      this.quill.deleteText(range.index, lengthToDelete, 'user')
      this.quill.insertEmbed(range.index, 'image', `${url}`, 'user')
    }

    range.index++
    this.quill.setSelection(range, 'user')
  }

  calculatePlaceholderInsertLength() {
    return this.placeholderDelta.ops.reduce(
      (accumulator: number, deltaOperation: any) => {
        if (deltaOperation.hasOwnProperty('insert')) accumulator++
        return accumulator
      },
      0,
    )
  }

  removeBase64Image() {
    const range = this.range
    if (range == null) return
    const lengthToDelete = this.calculatePlaceholderInsertLength()
    this.quill.deleteText(range.index, lengthToDelete, 'user')
  }
}

export default ImageUploader
