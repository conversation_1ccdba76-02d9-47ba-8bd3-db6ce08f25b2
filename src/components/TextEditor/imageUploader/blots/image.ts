import { BlockEmbed } from 'quill/blots/block'
import Quill from 'quill'

class LoadingImage extends BlockEmbed {
  static create(src: string) {
    console.log('Src: ', src)
    const node = super.create() as Element

    const image = document.createElement('img')
    image.setAttribute('src', src)
    node.appendChild(image)

    // Fügen Sie eine Klasse für den Ladeindikator hinzu
    if (!src.startsWith('http')) {
      node.classList.add('image-uploading')
    }

    return node
  }

  static value(domNode: HTMLElement) {
    const image = domNode.querySelector('img')
    return image ? image.getAttribute('src') : ''
  }

  // Neue Methode zum Entfernen der Ladeindikator-Klasse und Setzen der neuen Bild-URL
  static setLoaded(domNode: HTMLElement, src: string) {
    console.log('Update Image: ', src)
    const image = domNode.querySelector('img')
    if (image) {
      image.setAttribute('src', src)
    }
    domNode.classList.remove('image-uploading')
  }
}

LoadingImage.blotName = 'imageBlot'
LoadingImage.tagName = 'div'

Quill.register({ 'formats/imageBlot': LoadingImage })

export default LoadingImage
