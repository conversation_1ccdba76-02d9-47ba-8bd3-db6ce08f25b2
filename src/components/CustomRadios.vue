<script lang="ts" setup>
  import { PaymentCardContent, GridColumn } from '@/types/checkout'

  interface Props {
    selectedRadio: string
    radioContent: PaymentCardContent[]
    gridColumn?: GridColumn
  }

  interface Emit {
    (e: 'update:selectedRadio', value: string): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emit>()

  const updateSelectedOption = (value: string) => {
    emit('update:selectedRadio', value)
  }
</script>

<template>
  <VRadioGroup
    v-if="props.radioContent"
    :model-value="props.selectedRadio"
    @update:model-value="updateSelectedOption"
  >
    <VRow>
      <VCol
        v-for="item in props.radioContent"
        :key="item.brand"
        v-bind="gridColumn"
      >
        <VLabel
          class="custom-input custom-radio rounded cursor-pointer"
          :class="props.selectedRadio === item.value ? 'active' : ''"
        >
          <div>
            <VRadio :value="item.value" />
          </div>
          <slot :item="item">
            <div class="flex-grow-1">
              <div class="d-flex align-center mb-1">
                <h6 class="cr-title text-base">
                  {{ item.name }}
                </h6>
                <VSpacer />
                <span v-if="item.brand" class="text-disabled text-base">{{
                  item.brand
                }}</span>
              </div>
              <p class="text-sm mb-0">
                {{ item.brand }}
              </p>
            </div>
          </slot>
        </VLabel>
      </VCol>
    </VRow>
  </VRadioGroup>
</template>

<style lang="scss" scoped>
  .custom-radio {
    display: flex;
    align-items: flex-start;
    gap: 0.375rem;

    .v-radio {
      margin-block-start: -0.45rem;
    }

    .cr-title {
      font-weight: 500;
    }
  }
</style>
