import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
import { useJobAdvertGraph } from '@/api/graphHooks/useJobAdvertGraph'
import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { initStores, resetStores } from '@/stores/initStores'
import { useCompanyDetailsGraphData } from '@/api/graphHooks/company/useCompanyDetailsGraphData'

export const useSwitchDialog = () => {
  const companyStore = useCompanyStore()
  const {
    actions: { updateActiveCompany, loadCompanyUser, loadCompanyUsers },
  } = useCompanyUser()

  const {
    actions: { setCustomClaims },
  } = useAuthGraph()

  const {
    actions: { refetchJobAds },
  } = useJobAdvertGraph()

  const {
    actions: { refetchCompany },
  } = useCompanyDetailsGraphData()

  const authStore = useAuthStore()
  const appStore = useAppStore()

  const companyId = companyStore.company?.id

  const handleSwitchCompany = async (companyId: string) => {
    appStore.showAppLoader()

    await updateActiveCompany(companyId)
    await setCustomClaims()
    await resetStores()
    await initStores()
    await refetchJobAds({ companyId: companyId })
    await refetchCompany()
    await loadCompanyUser()
    await loadCompanyUsers()
    await authStore.setCompanyId(companyId)
    window.location.reload()

    appStore.hideAppLoader()
  }

  return {
    state: {
      companyId,
    },
    actions: {
      handleSwitchCompany,
    },
  }
}
