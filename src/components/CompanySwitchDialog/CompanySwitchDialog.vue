<script setup lang="ts">
  import { useSwitchDialog } from '@/components/CompanySwitchDialog/hooks/useSwitchDialog'
  import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
  import { useAppStore } from '@/stores/appStore'
  import AppSelect from '@core/components/AppSelect.vue'

  const {
    state: { companyId },
    actions: { handleSwitchCompany },
  } = useSwitchDialog()

  const {
    state: { companyUserInfo: companyUser, loadingCompanyUser },
    actions: { loadCompanyUser },
  } = useCompanyUser()

  const companyUserCompanies = computed(() => companyUser.value.companies)

  const appStore = useAppStore()

  const appLoader = computed(() => appStore.showLoader)

  onMounted(async () => {
    await loadCompanyUser()
  })

  interface Emit {
    (e: 'onClose', close: boolean): void
  }

  const props = defineProps({
    open: <PERSON><PERSON><PERSON>,
  })

  const close = false

  const emit = defineEmits<Emit>()

  const selectedCompany = ref<string | undefined>(companyId)

  const onChangeCompany = async (companyId: string) => {
    if (!selectedCompany) return
    await handleSwitchCompany(<string>companyId)
    if (!appLoader) {
      emit('onClose', close)
    }
  }
</script>

<template>
  <v-row justify="center">
    <v-col cols="auto">
      <v-dialog
        transition="dialog-top-transition"
        width="auto"
        :persistent="false"
        v-model="props.open"
        :disabled="appLoader"
      >
        <template v-slot:default="{ isActive }">
          <v-card v-if="!loadingCompanyUser">
            <v-toolbar density="compact" color="primary" class="px-2">
              <v-icon icon="tabler-refresh" />
              <v-toolbar-title>Unternehmen wechseln</v-toolbar-title>
            </v-toolbar>
            <v-card-text>
              <AppSelect
                v-model="selectedCompany"
                item-title="name"
                item-value="id"
                density="compact"
                placeholder="Select Company"
                :items="companyUserCompanies"
                style="inline-size: 12rem"
              />
            </v-card-text>
            <v-card-actions class="justify-end">
              <v-btn
                variant="text"
                size="small"
                @click="() => emit('onClose', close)"
                >schließen</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                :disabled="companyId === selectedCompany || !selectedCompany"
                color="primary"
                variant="flat"
                size="small"
                @click="() => onChangeCompany(<string>selectedCompany)"
                prepend-icon="tabler-refresh"
                >Wechseln</v-btn
              >
            </v-card-actions>
          </v-card>

          <v-card v-else>
            <v-toolbar density="compact" color="primary" class="px-2">
              <v-icon icon="tabler-refresh" />
              <v-toolbar-title>Unternehmen wechseln</v-toolbar-title>
            </v-toolbar>
            <v-card-text>
              <VSkeletonLoader
                :loading="loadingCompanyUser"
                :count="1"
                height="40px"
              />
              <p>Loading...</p>
            </v-card-text>
          </v-card>
        </template>
      </v-dialog>
    </v-col>
  </v-row>
</template>
