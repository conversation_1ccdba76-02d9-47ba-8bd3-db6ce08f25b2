<template>
  <canvas ref="chartRef"></canvas>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { Chart } from 'chart.js'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value: string) =>
      ['line', 'bar', 'pie', 'doughnut', 'radar', 'polarArea'].includes(value),
  },
  data: {
    type: Object,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({}),
  },
})

const chartRef = ref<HTMLCanvasElement | null>(null)
let chart: Chart | null = null

onMounted(() => {
  if (chartRef.value) {
    chart = new Chart(chartRef.value, {
      type: props.type as any,
      data: props.data,
      options: props.options,
    })
  }
})

onUnmounted(() => {
  if (chart) {
    chart.destroy()
  }
})

watch(
  () => props.data,
  (newData) => {
    if (chart && newData) {
      chart.data = newData
      chart.update()
    }
  },
  { deep: true }
)

watch(
  () => props.options,
  (newOptions) => {
    if (chart && newOptions) {
      chart.options = newOptions
      chart.update()
    }
  },
  { deep: true }
)
</script>
