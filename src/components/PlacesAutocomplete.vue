<script setup lang="ts">
  import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
  import { onMounted } from 'vue'
  import { VTextField } from 'vuetify/components'
  import initAutocomplete from '@/libs/places-autocomplete'
  import type PlaceChanged from '@/types/place-changed'

  const formStore = useJobAdFormStore()

  const props = defineProps({
    placeholder: {
      type: String,
      required: false,
      default: 'Beispielstraße 23 Bayreuth',
    },
    label: {
      type: String,
      required: false,
      default: 'Genaue Adresse',
    },
    address: {
      type: String,
      required: false,
      default: '',
    },
  })

  const emit = defineEmits<{
    (e: 'placeChanged', payload: PlaceChanged): void
  }>()

  /**
   * Autocomplete object
   */
  const autocomplete = ref<google.maps.places.Autocomplete>()

  /**
   * Autocomplete input text
   */

  const inputText = ref(props.address)

  /**
   * ID of input field
   */
  const inputId = 'places-autocomplete-input'

  /**
   * Currently selected place
   */
  const place = ref<google.maps.places.PlaceResult>()

  /**
   * Curently selected city
   */
  const city = ref<string>()

  /**
   * Currently selected housenumber
   */
  const houseNumber = ref<string>()

  const inputRef = ref<VTextField>()
  const isDirty = ref(false)
  const touched = ref(false)

  /**
   * Get currently selected place and emit result object with address, city and coordinates
   */
  const placeChanged = async () => {
    place.value = autocomplete.value?.getPlace()
    city.value = place.value?.address_components?.find(item =>
      item.types.includes('locality'),
    )?.long_name
    houseNumber.value = place.value?.address_components?.find(item =>
      item.types.includes('street_number'),
    )?.long_name

    const coordinates = {
      lat: place.value?.geometry?.location?.lat(),
      long: place.value?.geometry?.location?.lng(),
    }

    if (
      place.value?.formatted_address &&
      city.value &&
      coordinates.lat &&
      coordinates.long
    )
      emit('placeChanged', {
        address: place.value?.formatted_address,
        city: city.value,
        coordinates: { lat: coordinates.lat, long: coordinates.long },
      })

    inputText.value = place.value?.formatted_address ?? ''
    isDirty.value = false
    await inputRef.value?.validate()
  }

  const setupAutocomplete = () => {
    const options = {
      componentRestrictions: {
        country: 'de',
      },
      fields: ['address_components', 'formatted_address', 'geometry'],
      types: ['address'],
      bounds: new google.maps.LatLngBounds(
        new google.maps.LatLng(49.63735, 11.0544),
        new google.maps.LatLng(50.2024, 12.1348),
      ),
    }

    const inputElement = document.getElementById(inputId) as HTMLInputElement

    if (inputElement != null) {
      autocomplete.value = new google.maps.places.Autocomplete(
        inputElement,
        options,
      )
      autocomplete.value.addListener('place_changed', placeChanged)
    }
  }

  onMounted(async () => {
    try {
      await initAutocomplete()
      setupAutocomplete()
    } catch (error) {
      console.log(error)
    }
  })

  const rule = (_value: any) => {
    if (!touched.value && props.address?.length > 0) return true
    if (isDirty.value) return 'Bitte wählen Sie die Adresse aus den Ergebnissen'
    // if (!houseNumber.value)  //TODO: check why this is not working
    //   return 'Bitte geben Sie eine Hausnummer ein'

    return true
  }
</script>

<template>
  <VTextField
    id="places-autocomplete-input"
    ref="inputRef"
    v-model="inputText"
    type="text"
    :placeholder="props.placeholder"
    :label="props.label"
    :rules="[rule]"
    @input="
      () => {
        isDirty = true
        touched = true
      }
    "
  />
</template>

<style lang="scss">
  .pac-container {
    border: none;
    border-radius: 6px;
    background-color: #2f334a;
    font-family: Montserrat;
    padding-block: 7px;
    padding-inline: 10px;

    .pac-item {
      border: none;
      border-radius: 6px;
      color: #dbddeb;
      font-size: 0.8rem;
      padding-block: 4px;
      padding-inline: 7px;

      .pac-item-query {
        color: #dbddeb;
        font-size: 0.9rem;
      }

      &:hover {
        background-color: #35394f;
      }
    }

    &::after {
      display: none;
      content: unset;
    }
  }

  .v-theme--light .pac-container {
    background-color: white;

    .pac-item {
      color: #213946;

      .pac-item-query {
        color: #213946;
      }

      &:hover {
        background-color: #f8f8f9;
      }
    }
  }
</style>
