<script setup lang="ts">
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
  import { useJobActions } from '@/composables/JobActions/useJobActions'
  import { useAuthStore } from '@/stores/authStore'
  import { useJobAdsStore } from '@/stores/jobAdsStore'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useRoute, useRouter } from 'vue-router'
  import { computed, Ref } from 'vue'
  import { useTheme } from 'vuetify'
  import { calcAge, paginationMeta } from '@/utils/utils'
  import { useJobLikes } from '@/composables/JobAdverts/useJobLikes'
  import { matchedApplicantHeaders } from '@/composables/JobAdverts/tableHeaders'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { inlineTranslate } from '@/utils/utils'
  import avatar from '@images/avatars/avatar-0.png'

  const route = useRoute()
  const router = useRouter()
  const subStore = useSubscriptionStore()
  const authStore = useAuthStore()
  const jobAdsStore = useJobAdsStore()
  const searchQuery = ref('')
  const itemsPerPage = ref(10)
  const page = ref(1)

  const isSuper = authStore.isSuperUser

  const {
    state: {
      isPremium,
      jobAdvertTitle,
      jobAdId,
      jobAdvertApproved,
      jobAdvertDeclined,
      jobAdvertDeclineReason,
      loadingJobAdvert,
      loadingDeclineAction,
    },
    actions: { handleRowClicked, initiateJobLikesListen, stopJobLikesListen },
  } = useJobLikes()

  const { colors, dark } = useTheme().current.value

  const jobAdvertId = route.params?.id as string

  const {
    state: { jobAdvertLikes, loadingLikes: loadingJobLikes },
    actions: { refetchLikes },
  } = useJobAdverts()

  const pageTitle = computed(() => {
    return inlineTranslate('Overview Of Applicants')
  })

  const {
    state: { isDecliningMatchedApplicant },
  } = useJobActions()

  const {
    actions: { handleApplicantClicked },
  } = useApplicantProfile()

  const subscriptionStore = useSubscriptionStore()

  const hasBeenDeclined = computed(
    () =>
      !loadingJobLikes.value &&
      !jobAdvertApproved.value &&
      jobAdvertDeclined.value &&
      !isSuper,
  )

  const isUnderReview = computed(
    () =>
      !loadingJobLikes.value &&
      !jobAdvertApproved.value &&
      !jobAdvertDeclined.value &&
      !isSuper,
  )

  const isBasic = computed(
    () => !loadingJobLikes.value && !isPremium.value && !isSuper,
  )

  const applicantsList = computed(() => {
    return jobAdsStore.jobAdLikes[jobAdvertId]
  })

  const totalLikes: Ref<number> = computed(() =>
    applicantsList.value ? (applicantsList.value as any[]).length : 0,
  )

  const paginatedJobLikes = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return applicantsList.value?.slice(start, end) || []
  })

  const handleEditJobAd = async () => {
    await router.push({
      name: 'company-edit-job-ad-id',
      params: { id: jobAdId.value },
    })
  }

  const setAdvertCheckout = async () => {
    const jobAdWithTitle = `${jobAdId.value}|${jobAdvertTitle.value}`
    const checkoutObj = {
      jobAdId: jobAdId.value,
      jobAdTitle: jobAdvertTitle.value,
      jobAdText: jobAdWithTitle,
    }
    const objString = JSON.stringify(checkoutObj)
    localStorage.setItem('checkout', objString)
    await subStore.updateJobAdverts([jobAdWithTitle])
    if (subStore?.getJobAdverts && subStore.getJobAdverts.length > 0) {
      await router.push({
        path: '/company/subscription/checkout',
        query: {
          ad: jobAdId.value,
          tit: jobAdvertTitle.value,
        },
      })
    }
  }

  const onRowClicked = async (val: any) => {
    await handleRowClicked(val, dark)
  }

  onMounted(async () => {
    await refetchLikes()
    await subStore.updateJobAdverts([])
    localStorage.removeItem('checkout')
    await initiateJobLikesListen(jobAdvertId)
  })

  watchEffect(async () => {
    if (jobAdvertLikes.value) {
      await jobAdsStore.updateJobLikes(jobAdvertLikes.value, jobAdvertId)
    }
  })

  onUnmounted(() => {
    stopJobLikesListen(jobAdvertId)
  })

  const loadingLikes = loadingJobAdvert || loadingJobLikes
</script>

<template>
  <v-skeleton-loader
    v-if="loadingLikes"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20">
    <template v-slot:title>
      <VBtn color="primary" variant="text" @click="$router.push('/company')">
        <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
      </VBtn>
      {{ pageTitle }}
    </template>
    <v-card
      v-if="isBasic && !subscriptionStore.isCompanyUnlimited"
      elevation="0"
      class="mx-auto subtitle-class"
      prepend-icon="mdi-lock"
      rel="noopener"
      target="_blank"
    >
      <template v-slot:subtitle>
        <div>
          Führen Sie ein Upgrade auf Premium durch, um Ihre Matches <br />
          anzuzeigen und mit ihnen zu kommunizieren.
        </div>
      </template>
      <template v-slot:title>
        <v-row no-gutters v-if="!loadingLikes">
          <v-col cols="12" sm="8">
            <div class="text-h6">
              Diese Stellenanzeige ist derzeit im Basistarif
            </div>
          </v-col>
          <v-col cols="12" sm="4">
            <v-btn append-icon="mdi-crown" @click="setAdvertCheckout">
              Upgrade auf Premium
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </v-card>

    <v-card
      v-if="hasBeenDeclined"
      elevation="0"
      class="mx-auto"
      prepend-icon="mdi-lock"
      rel="noopener"
      target="_blank"
      :subtitle="`Begründung: ${jobAdvertDeclineReason}. Wenn Sie die Stellenanzeige bearbeitet haben, wird sie automatisch erneut geprüft.`"
    >
      <template v-slot:title>
        <v-row no-gutters v-if="!loadingLikes">
          <v-col cols="12" sm="8">
            <div class="text-h6">Die Anzeige wurde vorerst abgelehnt!</div>
          </v-col>
          <v-col cols="12" sm="4">
            <v-btn append-icon="mdi-edit" @click="handleEditJobAd">
              Stellenanzeige bearbeiten
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </v-card>
    <v-card
      v-if="isUnderReview"
      elevation="0"
      class="mx-auto"
      prepend-icon="mdi-check-circle"
      rel="noopener"
      target="_blank"
      subtitle="Diese Stellenanzeige wird derzeit geprüft und freigegeben."
    >
      <template v-slot:title>
        <v-row no-gutters v-if="!loadingLikes">
          <v-col cols="12" sm="8">
            <div class="text-h6">Stellenanzeige wird geprüft</div>
          </v-col>
        </v-row>
      </template>
    </v-card>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Bewerber suchen..."
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>

    <VDataTable
      :headers="matchedApplicantHeaders"
      :items="paginatedJobLikes"
      :loading="
        loadingLikes || isDecliningMatchedApplicant || loadingDeclineAction
      "
      :search="searchQuery"
      no-data-text="Noch keine Bewerber vorhanden"
      class="px-2"
      hover
      @click:row="
        (event: Event, value: any) =>
          isSuper
            ? handleApplicantClicked(value.item)
            : onRowClicked(value.item)
      "
    >
      <template #[`item.profileImageUrl`]="{ item }">
        <VAvatar
          :image="(item as any).image || avatar"
          size="55"
          class="my-3"
        />
      </template>

      <template #[`item.age`]="{ item }">
        {{ calcAge((item as any).birthday) }}
      </template>
      <template #[`item.graduation`]="{ item }">
        {{ (item as any).graduation }}
      </template>

      <!-- Status column. Generated dynamically based on like status -->
      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            v-if="(item as any).status === 'Anfrage'"
            color="warning"
            text="Anfrage"
            label
          />
          <VChip
            v-else-if="(item as any).status === 'MATCHED'"
            color="success"
            text="Match"
            label
          />
          <VChip
            v-else-if="(item as any).status === 'DELETED'"
            color="error"
            text="Gelöscht"
            label
          />
          <VChip v-else color="info" :text="(item as any).status" label />
        </div>
      </template>

      <template #no-data>
        <VCardText>
          <p>
            Bisher sind noch keine Bewerber vorhanden. <br />Sie werden per
            EMail benachrichtigt, sobald sich jemand auf Ihre Stellenanzeige
            bewirbt
          </p>
        </VCardText>
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalLikes as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalLikes / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalLikes / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
  </VCard>
</template>

<style lang="scss">
  .subtitle-class {
    white-space: pre-line;
  }
</style>
