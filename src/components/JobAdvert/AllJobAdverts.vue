<script setup lang="ts">
  import { Ausbildung, <PERSON>raktikum } from '@/api/apiTypes'
  import MoreBtn from '@/components/MoreBtn.vue'
  import { JobAdvertHeaders } from '@/composables/JobAdverts/tableHeaders'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { JobAdvert } from '@/gql/graphql'
  import { useAuthStore } from '@/stores/authStore'
  import { useJobAdsStore } from '@/stores/jobAdsStore'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import JobAd from '@/types/job-ad/job-ad'
  import { inlineTranslate, paginationMeta } from '@/utils/utils'
  import AppSelect from '@core/components/AppSelect.vue'
  import { useTheme } from 'vuetify'
  import { downloadJobAdPdf } from '@/libs/pdf-creation'
  import { useDebounceFn } from '@vueuse/core'

  const searchQuery = ref('')
  const selectedStatus = ref<string>('Pending')
  const selectedRows = ref<string[]>([])
  const subStore = useSubscriptionStore()
  const { colors } = useTheme().current.value
  const isDarkLayout = useTheme().global.current

  const {
    state: { paginatedJobAdsList, paginatedJobAds, loadingPaginatedJobAds },
    actions: {
      handleRowClicked,
      handleDeleteJobAd,
      handlePauseResume,
      loadPaginatedJobAds,
      updatePaginationParams,
    },
  } = useJobAdverts()

  const authStore = useAuthStore()

  const jobAds = computed(() => {
    return paginatedJobAdsList.value
  })

  const itemsPerPage = ref(10)
  const page = ref(1)
  const openLinkDialog = ref(false)
  const activeDynamicLink = ref('')
  const activeJobAdId = ref('')

  const totalJobAds = computed(
    () => paginatedJobAds.value?.meta?.totalItems || 0,
  )

  onMounted(async () => {
    await loadPaginatedJobAds()
  })

  const computedMoreList = computed(() => {
    return (raw: Ausbildung | Praktikum) => [
      {
        title: raw.paused
          ? inlineTranslate('Activate Ad')
          : inlineTranslate('Pause Ad'),
        value: 'pause',
        prependIcon: raw.paused ? 'mdi-play' : 'mdi-pause',
        action: () => () =>
          handlePauseResume(
            raw.id,
            isDarkLayout.value.dark,
            raw?.paused as boolean,
          ),
      },
      {
        title: inlineTranslate('Edit Ad'),
        value: 'edit',
        prependIcon: 'tabler-pencil',
        to: { name: 'company-edit-job-ad-id', params: { id: raw?.id } },
      },
      {
        title: inlineTranslate('Delete Ad'),
        value: 'delete',
        prependIcon: 'tabler-trash',
        action: () => () => {
          handleDeleteJobAd(raw, isDarkLayout.value.dark)
        },
      },
      {
        title: inlineTranslate('Generate PDF'),
        value: 'pdf',
        prependIcon: 'tabler-file-type-pdf',
        action: () => {
          return () => {
            downloadJobAdPdf(raw.id, raw.title, raw.description)
          }
        },
      },
      {
        title: inlineTranslate('Copy Link'),
        value: 'link',
        prependIcon: 'tabler-link',
        action: () => () => {
          openLinkDialog.value = true
          activeDynamicLink.value = raw.dynamicLink
          activeJobAdId.value = raw.id
        },
      },
    ]
  })

  const handleMoreAction = (item: any) => {
    if (item && item.action) {
      item.action()()
    }
  }

  const handlePaginationChange = async (newPage: number) => {
    page.value = newPage
    await updatePaginationParams(
      newPage,
      itemsPerPage.value,
      searchQuery.value,
      selectedStatus.value,
    )
  }

  const handleItemsPerPageChange = async (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage
    await updatePaginationParams(
      page.value,
      newItemsPerPage,
      searchQuery.value,
      selectedStatus.value,
    )
  }

  const handleSearch = async () => {
    page.value = 1 // Reset to first page when searching
    await updatePaginationParams(
      1,
      itemsPerPage.value,
      searchQuery.value,
      selectedStatus.value,
    )
  }

  const handleStatusChange = async () => {
    page.value = 1 // Reset to first page when changing status
    await updatePaginationParams(
      1,
      itemsPerPage.value,
      searchQuery.value,
      selectedStatus.value,
    )
  }

  // Add debounce for search
  const debouncedSearch = useDebounceFn(handleSearch, 300)

  onMounted(async () => {
    await subStore.updateJobAdverts([])
    localStorage.removeItem('checkout')
    await loadPaginatedJobAds()
  })

  watch(searchQuery, () => {
    debouncedSearch()
  })

  watch(selectedStatus, () => {
    handleStatusChange()
  })
</script>

<template>
  <JobAdvertLinkDialog
    :open="openLinkDialog"
    :url="activeDynamicLink"
    :jobAdId="activeJobAdId"
    @onClose="() => (openLinkDialog = false)"
  />
  <v-skeleton-loader
    v-if="loadingPaginatedJobAds"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :title="$t('Job Adverts')">
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Stellenanzeigen suchen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
          @keyup.enter="handleSearch"
        />
        <div class="invoice-list-status">
          <AppSelect
            v-model="selectedStatus"
            density="compact"
            placeholder="Status auswählen"
            clearable
            clear-icon="tabler-x"
            :items="['All', 'Approved', 'Blocked', 'Pending']"
            style="inline-size: 12rem"
          />
        </div>
      </div>
    </VCardText>
    <VDataTable
      v-model="selectedRows"
      :headers="JobAdvertHeaders"
      :items="jobAds"
      :loading="loadingPaginatedJobAds"
      :item-selectable="item => item.subscription === 'BASIC'"
      :item-value="item => item.id + '|' + item.title"
      class="px-2"
      hover
      show-select
      @click:row="(event, value) => handleRowClicked(event, value.item)"
      :search-input="searchQuery"
    >
      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <StatusChip :item="item" />
        </div>
      </template>

      <template #[`item.approved`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            v-if="item.approved"
            prepend-icon="mdi-check"
            color="success"
            text="Genehmigt"
          />
          <VChip
            v-if="item.isDeclined"
            prepend-icon="mdi-close"
            color="primary"
            text="Blockiert"
          />
          <VChip
            v-if="!item.isDeclined && !item.approved"
            prepend-icon="mdi-close"
            color="info"
            text="Unter Prüfung"
          />
        </div>
      </template>
      <template #[`item.paused`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip v-if="item.paused" color="primary">
            <v-icon>mdi-pause</v-icon>
          </VChip>
          <VChip v-else color="success" text="Active">
            <v-icon>mdi-play</v-icon>
          </VChip>
        </div>
      </template>

      <template #[`item.subscription`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip v-if="item.subscription === 'BASIC'" color="primary">
            <v-icon>mdi-circle</v-icon>
          </VChip>
          <VChip v-else-if="item.subscription === 'PREMIUM'" color="gold">
            <v-icon>mdi-crown</v-icon>
          </VChip>
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <IconBtn icon="tabler-eye" density="compact" class="mx-1" elevation="0">
          <VIcon />
        </IconBtn>

        <MoreBtn
          :menu-list="computedMoreList(item)"
          item-props
          color="undefined"
          @item-clicked="val => handleMoreAction(val)"
        />
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalJobAds) }}
          </p>

          <VPagination
            v-model="page"
            :length="paginatedJobAds?.meta?.totalPages || 1"
            :total-visible="
              $vuetify.display.xs
                ? 1
                : Math.min(5, paginatedJobAds?.meta?.totalPages || 1)
            "
            @update:model-value="handlePaginationChange"
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>

    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
