<script setup lang="ts">
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
  import PlacesAutocomplete from '@/components/PlacesAutocomplete.vue'
  import {
    dateValidator,
    maxLengthValidator,
    requiredValidator,
  } from '@validators'
  import { formatDateToString } from '@core/utils/formatters'
  import { useRouter } from 'vue-router'
  import { useCategory } from '@/composables/Category/useCategory'
  import { useTheme } from 'vuetify'
  import { useJobAdvertForm } from '@/composables/JobAdverts/useJobAdvertForm'
  import { useCompanyUserListGraph } from '@/api/graphHooks/companyUser/useCompanyUserListGraph'

  const props = defineProps({
    id: {
      type: String,
      required: false,
      default: null,
    },
    isNew: {
      type: Boolean,
      required: false,
      default: false,
    },
  })

  interface Emit {
    (e: 'toggleForm', value: boolean): void
  }

  const emit = defineEmits<Emit>()

  const router = useRouter()

  const formRef = ref()
  const formStore = useJobAdFormStore()
  const authStore = useAuthStore()
  const appStore = useAppStore()
  const appLoader = computed(() => appStore.showLoader)

  const isDarkLayout = useTheme().global.current
  const dark = isDarkLayout.value.dark

  const isSuper = computed(() => authStore.isSuperUser)

  const {
    state: { categoryList },
    actions: { loadCategories },
  } = useCategory()

  const {
    actions: { handleApproveJobAd, handleBlockJobAd, refetchSingleJobAd },
    state: {
      jobAdvert,
      jobAdText,
      jobAdTypeOptions,
      jobAdType,
      loadingSingleJobAdvert,
      loadingBlock,
      loadingApprove,
    },
  } = useJobAdvertForm()

  const {
    state: { companyUsersList },
    actions: { loadCompanyUsers },
  } = useCompanyUserListGraph()

  const jobCategories = computed(() => categoryList.value)
  const companyUsers = computed(() => companyUsersList.value)

  onMounted(async () => {
    await loadCategories()
    await loadCompanyUsers()

    if (props.id) {
      await refetchSingleJobAd({ jobAdvertId: props.id })

      // Wait for job advert data to be available
      setTimeout(async () => {
        if (jobAdvert.value) {
          await formStore.init(jobAdvert)
        }
      }, 500)
    } else {
      formStore.$reset()
      formStore.setDefaultUser(authStore?.claims?.companyUserId as string)
    }
  })

  const goToAddUser = () => {
    router.push({
      name: 'company-users',
    })
  }

  const clearForm = () => {
    formStore.$reset()
  }

  const submitForm = async () => {
    const valid = (await formRef.value.validate()).valid
    if (valid) {
      emit('toggleForm', false)
    }
  }

  const ausbildungJobAdText = computed(() => {
    return props.id
      ? jobAdText.value.editNewTraining
      : jobAdText.value.createNewTraining
  })

  const praktikumJobAdText = computed(() => {
    return props.id
      ? jobAdText.value.editNewInternship
      : jobAdText.value.createNewInternship
  })
</script>

<template>
  <VCard class="elevation-0" v-if="loadingSingleJobAdvert">
    <v-skeleton-loader
      class="mx-auto"
      elevation="12"
      max-height="800px"
      type="table-heading, list-item-two-line, image"
    ></v-skeleton-loader>
  </VCard>
  <VCard
    v-else
    :flat="true"
    :title="
      formStore.jobAdType === 'ausbildung'
        ? ausbildungJobAdText
        : praktikumJobAdText
    "
    :subtitle="props.id ? jobAdText.editJobInfo : jobAdText.createJobInfo"
  >
    <VForm ref="formRef" class="mx-6">
      <VRow>
        <VCol cols="12">
          <VRadioGroup
            v-model="jobAdType"
            inline
            :disabled="props.id !== null"
            :rules="[requiredValidator]"
          >
            <VRadio
              v-for="{ label, value } in jobAdTypeOptions"
              :key="value"
              :label="label"
              :value="value"
            />
          </VRadioGroup>
        </VCol>

        <VCol cols="6">
          <VRow>
            <VCol cols="12">
              <VTextField
                v-model="formStore.title"
                :label="
                  formStore.jobAdType === 'ausbildung'
                    ? jobAdText.jobTitle
                    : jobAdText.internshipTitle
                "
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" v-if="props.isNew">
              <PlacesAutocomplete
                :label="jobAdText.companyAddress"
                :address="formStore.address"
                @place-changed="formStore.placeChanged"
              />
            </VCol>
            <VCol cols="12" v-else>
              <PlacesAutocomplete
                v-if="formStore.address && formStore?.address?.length > 0"
                :label="jobAdText.companyAddress"
                :address="formStore.address"
                @place-changed="formStore.placeChanged"
              />
            </VCol>
            <VCol cols="12">
              <VAutocomplete
                v-model="formStore.selectedCategories"
                :label="
                  formStore.jobAdType === 'ausbildung'
                    ? jobAdText.categoryTraining
                    : jobAdText.categoryInternship
                "
                :items="jobCategories"
                item-value="id"
                item-title="name"
                :rules="[requiredValidator]"
                multiple
                chips
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.workHours"
                type="number"
                :suffix="jobAdText.hours"
                :label="jobAdText.hoursWeek"
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.holidayDays"
                type="number"
                :suffix="jobAdText.days"
                :label="jobAdText.holidaysYear"
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.educationDuration"
                type="number"
                :suffix="jobAdText.years"
                :label="jobAdText.trainingDuration"
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.gehalt"
                type="number"
                suffix="€"
                :label="jobAdText.salary1Year"
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.gehalt2"
                type="number"
                suffix="€"
                :label="jobAdText.year2Optional"
              />
            </VCol>
            <VCol v-if="formStore.jobAdType === 'ausbildung'" cols="6">
              <VTextField
                v-model.number="formStore.gehalt3"
                type="number"
                suffix="€"
                :label="jobAdText.year3Optional"
              />
            </VCol>
            <VCol cols="6">
              <VTextField
                :value="
                  formStore.startDate
                    ? formatDateToString(formStore.startDate)
                    : null
                "
                type="date"
                :rules="[dateValidator]"
                :label="
                  formStore.jobAdType === 'ausbildung'
                    ? jobAdText.startOfTraining
                    : jobAdText.availableFrom
                "
                @update:model-value="
                  (val: any) => (formStore.startDate = new Date(val))
                "
              />
            </VCol>
            <VCol cols="6">
              <VTextField
                :value="formatDateToString(formStore.activeFromDate)"
                type="date"
                :label="jobAdText.adActiveFrom"
                @update:model-value="
                  (val: any) => (formStore.activeFromDate = new Date(val))
                "
              />
            </VCol>
            <VCol cols="10">
              <VAutocomplete
                v-model="formStore.selectedUsers"
                :label="jobAdText.whoShouldHaveAccess"
                :items="companyUsers"
                item-title="name"
                item-value="id"
                :rules="[requiredValidator]"
                :return-object="false"
                multiple
                chips
              />
            </VCol>
            <VCol cols="2">
              <VBtn
                @click="goToAddUser"
                class="mt-2"
                color="primary"
                size="small"
                icon
                fab
              >
                <v-icon>mdi-plus</v-icon>
              </VBtn>
            </VCol>
          </VRow>
        </VCol>
        <VCol cols="6">
          <VRow>
            <VCol :cols="12" class="d-flex flex-column">
              <ImageInputCrop
                :title="jobAdText.banner"
                :min-aspect-ratio="0.5"
                :max-aspect-ratio="10"
                :image-url="formStore.headerSavedUrl"
                @image-cropped="(result: any) => formStore.imageChanged(result)"
              />
            </VCol>
            <VCol cols="12">
              <VTextarea
                v-model="formStore.description"
                :label="
                  formStore.jobAdType === 'ausbildung'
                    ? jobAdText.shortDescriptionTraining
                    : jobAdText.briefDescriptionInternship
                "
                :placeholder="jobAdText.descriptionPlaceholder"
                :counter="200"
                rows="3"
                :rules="[(value: any) => maxLengthValidator(value, 200)]"
              />
            </VCol>
            <VCol cols="12">
              <TextEditor
                v-if="formStore.id === props.id"
                v-model="formStore.detailDescription"
                max-height="450px"
                @update:stale-img-ids="
                  (staleImgIds: any) => (formStore.staleImgIds = staleImgIds)
                "
                @update:images-to-delete="
                  (imagesToDelete: any) =>
                    (formStore.imagesToDelete = imagesToDelete)
                "
              />
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </VForm>
    <v-card-actions class="mt-8">
      <VBtn color="primary" class="mr-8" variant="text" @click="router.go(-1)">
        <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
        Zurück
      </VBtn>
      <VBtn @click="clearForm" color="grey" variant="text">
        <v-icon class="mr-1" icon="tabler-square-rounded-x-filled"></v-icon>
        Felder leeren
      </VBtn>
      <v-spacer></v-spacer>

      <VBtn
        @click="submitForm"
        color="success"
        variant="flat"
        class="justify-center"
      >
        {{
          props.id ? jobAdText.saveChanges : jobAdText.createJobAdvertisement
        }}
      </VBtn>
      <div v-if="isSuper" class="ml-16">
        <VBtn
          @click="handleApproveJobAd(<string>props.id, dark)"
          color="success"
          variant="flat"
          :disabled="formStore.approved || appLoader"
          :loading="loadingApprove"
          class="justify-center"
        >
          {{ jobAdText.approveJobAd }}
        </VBtn>
        <VBtn
          @click="handleBlockJobAd(<string>props.id, dark)"
          color="error"
          :disabled="!formStore.approved || appLoader"
          :loading="loadingBlock"
          variant="flat"
          class="justify-center"
        >
          {{ jobAdText.blockJobAd }}
        </VBtn>
      </div>
    </v-card-actions>
  </VCard>
</template>
