<script setup>
  import { computed } from 'vue'
  import { calcTimeDifference } from '@/utils/utils'

  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
  })

  const isPaused = computed(() => props.item.paused)
  const isDeclined = computed(() => props.item.isDeclined)

  const isPendingReview = computed(() => {
    return !props.item.approved && !props.item?.active && !props.item.declined
  })

  const hasDaysLeft = computed(() => {
    return (
      props.item?.active &&
      props.item.startDate &&
      calcTimeDifference(props.item.startDate)
    )
  })

  const hasExpired = computed(() => {
    return props.item?.startDate && calcTimeDifference(props.item.startDate) > 0
  })

  const isStarting = computed(() => {
    return (
      props.item?.approved &&
      !props.item.active &&
      !props.item.pause &&
      calcTimeDifference(props.item.startDate) > 0
    )
  })
</script>

<template>
  <VChip v-if="isPaused" color="primary" text="Pausiert" />

  <VChip v-if="isPendingReview" color="primary" text="Wird überprüft" />

  <VChip v-if="isDeclined" color="primary" text="Anzeige abgelehnt" />

  <VChip
    v-if="hasDaysLeft"
    color="success"
    :text="`Noch ${365 + calcTimeDifference(item.startDate)} Tage`"
    label
  />
  <VChip v-else-if="hasExpired" color="primary" text="Abgelaufen" label />
  <VChip
    v-if="isStarting"
    color="primary"
    :text="`Startet in ${calcTimeDifference(item.startDate)} Tagen`"
  />
</template>
