<script setup lang="ts">
  import { Ausbildung, <PERSON>raktikum } from '@/api/apiTypes'
  import MoreBtn from '@/components/MoreBtn.vue'
  import { JobAdvertHeaders } from '@/composables/JobAdverts/tableHeaders'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { usePusher } from '@/composables/usePusher'
  import router from '@/router'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { inlineTranslate, paginationMeta } from '@/utils/utils'
  import AppSelect from '@core/components/AppSelect.vue'
  import { useTheme } from 'vuetify'
  import { downloadJobAdPdf } from '@/libs/pdf-creation'

  const searchQuery = ref('')
  const selectedStatus = ref()
  const selectedRows = ref<string[]>([])
  const subStore = useSubscriptionStore()
  const { colors } = useTheme().current.value
  const isDarkLayout = useTheme().global.current

  const {
    state: { loadingJobAds, companyActiveJobs },
    actions: {
      handleRowClicked,
      handleDeleteJobAd,
      handlePauseResume,
      loadJobAdsByCompany,
      refetchJobAds,
    },
  } = useJobAdverts()

  const authStore = useAuthStore()
  const appStore = useAppStore()

  const appLoader = computed(() => appStore.showLoader)

  const itemsPerPage = ref(10)
  const page = ref(1)
  const openLinkDialog = ref(false)
  const activeDynamicLink = ref('')
  const activeJobAdId = ref('')

  const filteredJobAds = computed(() => {
    let filtered = companyActiveJobs.value || []

    // Apply status filter
    if (selectedStatus.value) {
      switch (selectedStatus.value) {
        case 'Approved':
          filtered = filtered.filter(jobAd => jobAd.approved)
          break
        case 'Blocked':
          filtered = filtered.filter(jobAd => jobAd.isDeclined)
          break
        case 'Pending Review':
          filtered = filtered.filter(
            jobAd => !jobAd.approved && !jobAd.isDeclined,
          )
          break
      }
    }

    // Apply search filter if needed
    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase()
      filtered = filtered.filter(jobAd =>
        jobAd.title.toLowerCase().includes(search),
      )
    }

    return filtered
  })

  const totalAds = computed(() =>
    filteredJobAds.value ? filteredJobAds.value.length : 0,
  )

  const paginatedJobAds = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredJobAds.value.slice(start, end)
  })

  const computedMoreList = computed(() => {
    return (raw: Ausbildung | Praktikum) => [
      {
        title: raw.paused
          ? inlineTranslate('Activate Ad')
          : inlineTranslate('Pause Ad'),
        value: 'pause',
        prependIcon: raw.paused ? 'mdi-play' : 'mdi-pause',
        action: () => () =>
          handlePauseResume(
            raw.id,
            isDarkLayout.value.dark,
            raw?.paused as boolean,
          ),
      },
      {
        title: inlineTranslate('Edit Ad'),
        value: 'edit',
        prependIcon: 'tabler-pencil',
        to: { name: 'company-edit-job-ad-id', params: { id: raw?.id } },
      },
      {
        title: inlineTranslate('Delete Ad'),
        value: 'delete',
        prependIcon: 'tabler-trash',
        action: () => () => {
          handleDeleteJobAd(raw, isDarkLayout.value.dark)
        },
      },
      {
        title: inlineTranslate('Generate PDF'),
        value: 'pdf',
        prependIcon: 'tabler-file-type-pdf',
        action: () => {
          return () => {
            downloadJobAdPdf(raw.id, raw.title, raw.description)
          }
        },
      },
      {
        title: inlineTranslate('Copy Link'),
        value: 'link',
        prependIcon: 'tabler-link',
        action: () => () => {
          openLinkDialog.value = true
          activeDynamicLink.value = raw.dynamicLink
          activeJobAdId.value = raw.id
        },
      },
    ]
  })

  const handleMoreAction = (item: any) => {
    if (item && item.action) {
      item.action()()
    }
  }

  const goToBulkPricing = async () => {
    const checkoutObj = {
      companyId: authStore.companyId,
      jobAdverts: selectedRows.value,
    }
    const objString = JSON.stringify(checkoutObj)
    localStorage.setItem('checkout', objString)
    await subStore.updateJobAdverts(selectedRows.value)
    if (subStore?.getJobAdverts && subStore.getJobAdverts.length > 0) {
      await router.push('/company/subscription/checkout')
    }
  }

  const sortBy = ref([{ key: 'title', order: 'asc' }])

  onMounted(async () => {
    await subStore.updateJobAdverts([])
    localStorage.removeItem('checkout')
    await loadJobAdsByCompany()
  })

  watch([searchQuery, selectedStatus], () => {
    page.value = 1
  })
</script>

<template>
  <JobAdvertLinkDialog
    :open="openLinkDialog"
    :url="activeDynamicLink"
    :jobAdId="activeJobAdId"
    @onClose="() => (openLinkDialog = false)"
  />
  <v-skeleton-loader
    v-if="loadingJobAds"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <VCard v-else elevation="20" :title="$t('Job Adverts')" :disabled="appLoader">
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Stellenanzeigen suchen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
        <div class="invoice-list-status">
          <AppSelect
            v-model="selectedStatus"
            density="compact"
            placeholder="Status filtern"
            clearable
            item-title="text"
            item-value="value"
            clear-icon="tabler-x"
            :items="[
              { text: 'Genehmigt', value: 'Approved' },
              { text: 'Blockiet', value: 'Blocked' },
              { text: 'Unter Prüfung', value: 'Pending Review' },
            ]"
            style="inline-size: 12rem"
          />
        </div>
      </div>
    </VCardText>

    <VDivider />
    <v-row>
      <v-col cols="4" class="my-4 ml-6">
        <v-btn
          prepend-icon="mdi-plus"
          @click="goToBulkPricing"
          v-if="selectedRows.length"
        >
          Ausgewählte Stellenanzeigen buchen
        </v-btn>
      </v-col>
    </v-row>

    <VDataTable
      v-model="selectedRows"
      :headers="JobAdvertHeaders"
      :items="paginatedJobAds"
      :sort-by="sortBy"
      :loading="loadingJobAds || appLoader"
      :item-selectable="item => item.subscription === 'BASIC'"
      :item-value="item => item.id + '|' + item.title"
      class="px-2"
      :search="searchQuery"
      hover
      show-select
      @click:row="(event, value) => handleRowClicked(event, value.item)"
    >
      <!--      No item template below-->
      <template #no-data>
        <VCardText>
          <p>Keine Stellenanzeigen vorhanden</p>
        </VCardText>
      </template>

      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <StatusChip :item="item" />
        </div>
      </template>

      <template #[`item.approved`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            v-if="item.approved"
            prepend-icon="mdi-check"
            color="success"
            text="Genehmigt"
          />
          <VChip
            v-if="item.isDeclined"
            prepend-icon="mdi-close"
            color="primary"
            text="Blockiert"
          />
          <VChip
            v-if="!item.isDeclined && !item.approved && !item.isDraft"
            prepend-icon="mdi-close"
            color="info"
            text="Unter Prüfung"
          />
          <VChip
            v-if="!item.isDeclined && !item.approved && item.isDraft"
            prepend-icon="mdi-alert-circle-outline"
            color="grey"
            text="Ist Entwurf"
          />
        </div>
      </template>
      <template #[`item.paused`]="{ item }">
        <div v-if="item.isDraft" class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-alert-circle-outline"
            color="grey"
            text="deaktiviert"
          />
        </div>
        <div v-else class="d-flex flex-wrap chip-wrapper">
          <VChip
            v-if="item.paused"
            prepend-icon="mdi-pause"
            color="primary"
            text="Angehalten"
          />
          <VChip v-else prepend-icon="mdi-play" color="success" text="Aktiv" />
        </div>
      </template>

      <template #[`item.subscription`]="{ item }">
        <div v-if="item.isDraft" class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-alert-circle-outline"
            color="grey"
            text="deaktiviert"
          />
        </div>
        <div v-else class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-circle"
            v-if="item.subscription === 'BASIC'"
            color="primary"
            :text="item.subscription"
          />
          <VChip
            prepend-icon="mdi-crown"
            v-else-if="item.subscription === 'PREMIUM'"
            color="gold"
            :text="`${item.subscription}`"
          />
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <IconBtn icon="tabler-eye" density="compact" class="mx-1" elevation="0">
          <VIcon />
        </IconBtn>

        <MoreBtn
          :menu-list="computedMoreList(item)"
          item-props
          color="undefined"
          @item-clicked="val => handleMoreAction(val)"
        />
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalAds as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalAds / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalAds / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
