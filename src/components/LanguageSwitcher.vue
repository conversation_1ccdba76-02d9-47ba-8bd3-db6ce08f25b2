<script>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Tr from '@/plugins/i18n/translation'

export default {
  setup() {
    const { t, locale } = useI18n()

    const supportedLocales = Tr.supportedLocales

    const router = useRouter()

    const switchLanguage = async event => {
      const newLocale = event.target.value

      await Tr.switchLanguage(newLocale)

      try {
        await router.replace({ params: { locale: newLocale } })
      }
      catch (e) {
        console.log(e)
        await router.push('/')
      }
    }

    const languageObjects = computed(() => {
      return supportedLocales.map(sLocale => {
        const lang = t(`locale.${sLocale}`)

        return { locale: sLocale, lang }
      })
    })

    // const localeObj = reactive({locale: locale, lang: t(`locale.${locale}`)})

    const label = computed(() => {
      return `${t('Choose')} ${t('Language')}`
    })

    return { t, locale, supportedLocales, switchLanguage, languageObjects, label }
  },
}
</script>

<template>
  <!--  <select @change="switchLanguage"> -->
  <!--    <option -->
  <!--      v-for="sLocale in supportedLocales" -->
  <!--      :key="`locale-${sLocale}`" -->
  <!--      :value="sLocale" -->
  <!--      :selected="locale === sLocale" -->
  <!--    > -->
  <!--      {{ t(`locale.${sLocale}`) }} -->
  <!--    </option> -->
  <!--  </select> -->

  <VSelect
    v-model="locale"
    :label="label"
    :items="supportedLocales"
    class="me-3"
    prepend-inner-icon="tabler-language"
    density="compact"
    @update:menu="switchLanguage"
  >
    <template #item="{ props, item }">
      <VListItem
        v-bind="props"
        :title="$t(`locale.${item.raw}`)"
      />
    </template>

    <template #selection="{ props, item }">
      <VListItem
        v-bind="props"
        :title="$t(`locale.${item.raw}`)"
      />
    </template>
  </VSelect>
</template>
