<script setup lang="ts">
  import { useCompany } from '@/composables/Company/useCompany'
  import { confirmCompanyDelete } from '@/composables/useSweetAlert'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { paginationMeta } from '@/utils/utils'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useDebounceFn } from '@vueuse/core'
  import { useSuperCompany } from '@/composables/Company/useSuperCompany'

  const searchQuery = ref('')
  const subStore = useSubscriptionStore()
  const appStore = useAppStore()

  const {
    state: {
      allCompaniesHeader,
      paginatedCompanies,
      loadingPaginatedCompanies,
    },
    actions: {
      handleCompanyClicked,
      removeCompanyById,
      loadPaginatedCompanies,
      updatePaginationParams,
    },
  } = useSuperCompany()

  const router = useRouter()

  const authStore = useAuthStore()
  const showCompanyForm = computed(() => {
    return companyStore.showCompanyForm
  })
  const companyStore = useCompanyStore()

  const itemsPerPage = ref(10)
  const page = ref(1)

  const totalCompanies = computed(
    () => paginatedCompanies.value?.meta?.totalItems || 0,
  )

  const companies = computed(() => paginatedCompanies.value?.items || [])

  const resetActiveCompany = async () => {
    await authStore.resetCompanyId()
    companyStore.resetCompany()
    await appStore.setInCompanyView(false)
  }

  const deleteCompany = async (id: string) => {
    const confirm = await confirmCompanyDelete(true)
    if (confirm.isConfirmed) {
      appStore.showAppLoader()
      await router.push('/super/company')
      await removeCompanyById(id)
      appStore.hideAppLoader()
    } else {
      console.log('delete company not confirmed')
    }
  }

  const toggleCompanyForm = () => {
    companyStore.toggleCompanyForm()
  }

  const handlePaginationChange = async (newPage: number) => {
    page.value = newPage
    await updatePaginationParams(newPage, itemsPerPage.value, searchQuery.value)
  }

  const handleItemsPerPageChange = async (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage
    await updatePaginationParams(page.value, newItemsPerPage, searchQuery.value)
  }

  const handleSearch = async () => {
    if (searchQuery.value.length < 3) return
    page.value = 1 // Reset to first page when searching
    await updatePaginationParams(1, itemsPerPage.value, searchQuery.value)
  }

  // Add debounce for search
  const debouncedSearch = useDebounceFn(handleSearch, 300)

  onMounted(async () => {
    await subStore.updateJobAdverts([])
    localStorage.removeItem('checkout')
    await resetActiveCompany()
    await loadPaginatedCompanies()
  })

  watch(searchQuery, () => {
    debouncedSearch()
  })
</script>

<template>
  <v-row>
    <v-col :cols="showCompanyForm ? 8 : 12">
      <v-skeleton-loader
        v-if="loadingPaginatedCompanies"
        class="mx-auto border py-6"
        type="table"
      ></v-skeleton-loader>
      <VCard v-else elevation="20" :title="$t('All Companies')">
        <template v-slot:title>
          <v-row class="px-2">
            <v-col cols="6">
              {{ $t('All Companies') }}
            </v-col>
            <v-col cols="6" class="d-flex justify-end">
              <VBtn
                color="primary"
                variant="outlined"
                @click="() => toggleCompanyForm(!showCompanyForm)"
              >
                <v-icon class="mr-1" icon="tabler-plus"></v-icon>
                {{
                  showCompanyForm ? $t('Hide Form') : $t('Create New Company')
                }}
              </VBtn>
            </v-col>
          </v-row>
        </template>
        <VCardText>
          <div class="d-flex align-end flex-wrap gap-3">
            <AppTextField
              v-model="searchQuery"
              placeholder="Find Company"
              density="compact"
              prepend-inner-icon="mdi-magnify"
              class="me-3"
              @keyup.enter="handleSearch"
            />
          </div>
        </VCardText>

        <VDataTable
          :headers="allCompaniesHeader"
          :items="companies"
          :loading="loadingPaginatedCompanies || appStore.showLoader"
          class="px-2"
          :disabled="appStore.showLoader"
          hover
          @click:row="
            (event: Event, value: any) =>
              handleCompanyClicked(event, value.item)
          "
          :search-input="searchQuery"
        >
          <template #[`item.logoImageUrl`]="{ item }">
            <div class="ma-4">
              <v-avatar :image="item.logoImageUrl" size="80"></v-avatar>
            </div>
          </template>
          <template #[`item.actions`]="{ item }">
            <VBtn
              icon="tabler-trash"
              density="compact"
              class="mx-1"
              @click="deleteCompany(item.id)"
              elevation="0"
            >
              <VIcon />
            </VBtn>
          </template>

          <template #bottom>
            <VDivider />
            <div
              class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
            >
              <p class="text-sm text-disabled mb-0">
                {{ paginationMeta({ page, itemsPerPage }, totalCompanies) }}
              </p>

              <VPagination
                v-model="page"
                :length="paginatedCompanies?.meta?.totalPages || 1"
                :total-visible="
                  $vuetify.display.xs
                    ? 1
                    : Math.min(5, paginatedCompanies?.meta?.totalPages || 1)
                "
                @update:model-value="handlePaginationChange"
              >
                <template #prev="slotProps">
                  <VBtn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Zurück
                  </VBtn>
                </template>

                <template #next="slotProps">
                  <VBtn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Weiter
                  </VBtn>
                </template>
              </VPagination>
            </div>
          </template>
        </VDataTable>
        <VDivider />
      </VCard>
    </v-col>
    <v-col v-if="showCompanyForm" :cols="showCompanyForm ? 4 : 0">
      <VCard class="px-8 pb-8" elevation="20" :title="$t('New Company')">
        <CompanyForm is-initial-setup is-by-admin />
      </VCard>
    </v-col>
  </v-row>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
