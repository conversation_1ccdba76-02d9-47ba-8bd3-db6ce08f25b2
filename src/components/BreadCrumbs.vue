<script setup lang="ts">
  import { computed } from 'vue'
  import { useAppStore } from '@/stores/appStore'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useFairs } from '@/composables/Fairs/useFairs'

  const appStore = useAppStore()
  const companyStore = useCompanyStore()
  const fairStore = useFairStore()

  const { exitCompany, exitFairCompany, exitFair } = useFairs().actions

  const activeCompany = computed(() => companyStore.getCompany)
  const activeFair = computed(() => fairStore.getFair)

  const fairItems = computed(() => [
    {
      title: 'Messen',
      disabled: false,
      action: exitFair,
    },
    {
      title: activeFair.value?.name,
      disabled: false,
      action: () => console.log('Fair clicked'),
    },
  ])

  const companyItems = computed(() => [
    {
      title: 'Messen',
      disabled: false,
      action: exitFair,
    },
    {
      title: activeFair.value?.name,
      disabled: false,
      action: leaveCompany,
    },
    {
      title: activeCompany.value?.name,
      disabled: false,
      action: () => console.log('Company clicked'),
    },
  ])

  const items = computed(() => {
    if (appStore.isInFairView && !appStore.isInCompanyView) {
      return fairItems.value
    } else if (appStore.isInFairView && appStore.isInCompanyView) {
      return companyItems.value
    }
    return []
  })

  const leaveCompany = async () => {
    if (appStore.isInFairView) {
      await exitFairCompany()
    } else {
      exitCompany()
    }
  }

  const handleClick = item => {
    if (!item.disabled && item.action) {
      item.action()
    }
  }
</script>

<template>
  <v-breadcrumbs :items="items" class="breadbg pa-1">
    <template v-slot:prepend>
      <v-icon icon="mdi-home" size="small" class="mx-1"></v-icon>
    </template>

    <template v-slot:item="{ item }">
      <a @click="handleClick(item)">
        {{ item.title }}
      </a>
    </template>
  </v-breadcrumbs>
</template>

<style scoped>
  a {
    cursor: pointer;
    text-decoration: none;
    color: inherit;
  }

  a:hover {
    text-decoration: underline;
  }

  .breadbg {
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(41, 45, 64, 0.3);
    border-radius: 5px;
  }
</style>
