export const ENVIRONMENT = import.meta.env.VITE_ENVIRONMENT

export const DEFAULT_LOCALE = import.meta.env.VITE_DEFAULT_LOCALE || 'de'
export const FALLBACK_LOCALE = import.meta.env.VITE_FALLBACK_LOCALE || 'en'
export const SUPPORTED_LOCALES = import.meta.env.VITE_SUPPORTED_LOCALES?.split(
  ',',
) || ['de', 'en']
export const FIREBASE_PROJECT =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_FIREBASE_PROJECT
    : import.meta.env.VITE_DEV_FIREBASE_PROJECT
export const PROD_API_URL = import.meta.env.VITE_PROD_API_URL
export const DEV_API_URL = import.meta.env.VITE_DEV_API_URL
export const DEV_API_HOST = import.meta.env.VITE_DEV_API_HOST
export const DEV_WS_URL = import.meta.env.VITE_DEV_WS_URL

export const APIKEY =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_APIKEY
    : import.meta.env.VITE_DEV_APIKEY

export const AUTHDOMAIN =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_AUTHDOMAIN
    : import.meta.env.VITE_DEV_AUTHDOMAIN
export const PROJECTID =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_PROJECTID
    : import.meta.env.VITE_DEV_PROJECTID
export const STORAGEBUCKET =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_STORAGEBUCKET
    : import.meta.env.VITE_DEV_STORAGEBUCKET
export const MESSAGINGSENDERID =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_MESSAGINGSENDERID
    : import.meta.env.VITE_DEV_MESSAGINGSENDERID
export const APPID =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_APPID
    : import.meta.env.VITE_DEV_APPID
export const MEASUREMENTID =
  ENVIRONMENT === 'production'
    ? import.meta.env.VITE_MEASUREMENTID
    : import.meta.env.VITE_DEV_MEASUREMENTID
export const AGORAID = import.meta.env.VITE_AGORA_ID
export const AGORACERT = import.meta.env.VITE_AGORA_CERTIFICATE
export const BRANCH_KEY_LIVE = import.meta.env.VITE_BRANCH_KEY_LIVE
export const BRANCH_SECRET_LIVE = import.meta.env.VITE_BRANCH_SECRET_LIVE
export const BRANCH_APPID_LIVE = import.meta.env.VITE_BRANCH_APP_ID_LIVE
export const BRANCH_KEY_TEST = import.meta.env.VITE_BRANCH_KEY_TEST
export const BRANCH_SECRET_TEST = import.meta.env.VITE_BRANCH_SECRET_TEST
export const BRANCH_APPID_TEST = import.meta.env.VITE_BRANCH_APP_ID_TEST
export const PUSHER_KEY = import.meta.env.VITE_PUSHER_KEY
export const PUSHER_CLUSTER = import.meta.env.VITE_PUSHER_CLUSTER
export const TEST_STRIPE_PRODUCT_PRICE_ID = import.meta.env
  .VITE_TEST_STRIPE_PRODUCT_PRICE_ID

export const PROD_STRIPE_PRODUCT_PRICE_ID = import.meta.env
  .VITE_PROD_STRIPE_PRODUCT_PRICE_ID
