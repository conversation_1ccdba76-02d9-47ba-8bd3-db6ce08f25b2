import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import {
  CREATE_COMPANY_FAIR_JOB,
  DELETE_COMPANY_FAIR_JOB,
  UPDATE_COMPANY_FAIR_JOB,
} from '@/api/graphql/mutations/companyFairJobMutations'
import { GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useRoute } from 'vue-router'

export const useCompanyFairJobGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const { mutate: createCompanyFairJob, loading: loadingCompanyCreateFairJob } =
    useMutation(CREATE_COMPANY_FAIR_JOB, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  const { mutate: updateCompanyFairJob, loading: loadingCompanyUpdateFairJob } =
    useMutation(UPDATE_COMPANY_FAIR_JOB, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  const { mutate: deleteCompanyFairJob, loading: loadingCompanyDeleteFairJob } =
    useMutation(DELETE_COMPANY_FAIR_JOB, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  return {
    state: {
      loadingCompanyDeleteFairJob,
      loadingCompanyCreateFairJob,
      loadingCompanyUpdateFairJob,
    },
    actions: {
      deleteCompanyFairJob,
      createCompanyFairJob,
      updateCompanyFairJob,
    },
  }
}
