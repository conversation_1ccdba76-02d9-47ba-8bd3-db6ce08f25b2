import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import {
  GET_ALL_TIMESLOTS,
  GET_TIMESLOT_BY_ID,
  GET_TIMESLOTS_BY_FAIR_ID,
} from '@/api/graphql/queries/timeslotQueries'
import {
  CREATE_TIMESLOT,
  UPDATE_TIMESLOT,
  REMOVE_TIMESLOT,
  REMOVE_MULTIPLE_TIMESLOTS,
} from '@/api/graphql/mutations/timeslotMutation'
import { useRoute } from 'vue-router'

export const useTimeslotGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const fairId = route.params.fid

  const {
    result: getAllFairTimeslots,
    loading: loadingAllTimeslots,
    refetch: refetchTimeslots,
    load: loadTimeslots,
  } = useLazyQuery(
    GET_TIMESLOTS_BY_FAIR_ID,
    {
      fairId: fairId,
    },
    () => ({
      prefetch: false,
    }),
  )

  const fairTimeslotsList = computed(
    () => getAllFairTimeslots.value?.findTimeslotByFairId ?? [],
  )

  const { mutate: createTimeslot, loading: loadingCreate } =
    useMutation(CREATE_TIMESLOT)

  const { mutate: updateTimeslot, loading: loadingUpdate } =
    useMutation(UPDATE_TIMESLOT)

  const { mutate: removeTimeslot, loading: loadingRemove } =
    useMutation(REMOVE_TIMESLOT)

  const { mutate: removeMultipleTimeslots, loading: loadingBulkRemove } =
    useMutation(REMOVE_MULTIPLE_TIMESLOTS)

  return {
    state: {
      fairTimeslotsList,
      loadingAllTimeslots,
      loadingCreate,
      loadingUpdate,
      loadingRemove,
      loadingBulkRemove,
    },
    actions: {
      refetchTimeslots,
      loadTimeslots,
      createTimeslot,
      updateTimeslot,
      removeTimeslot,
      removeMultipleTimeslots,
    },
  }
}
