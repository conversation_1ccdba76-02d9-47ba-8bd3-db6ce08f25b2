import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import { GET_COMPANIES_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useRoute } from 'vue-router'
import {
  CREATE_COMPANY_FAIR_PARTICIPATION,
  DELETE_COMPANY_FAIR_PARTICIPATION,
  REMOVE_COMPANY_FROM_FAIR,
  UPDATE_COMPANY_FAIR_PARTICIPATION,
} from '@/api/graphql/mutations/companyFairParticipationMutation'
import { GET_COMPANIES_WITH_FAIR_STATUS } from '@/api/graphql/queries/companyQueries'

export const useCompanyFairParticipationGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const { mutate: createParticipation, loading: loadingCreate } = useMutation(
    CREATE_COMPANY_FAIR_PARTICIPATION,
    {
      refetchQueries: [
        {
          query: GET_COMPANIES_IN_FAIR,
          variables: {
            fairId: route.params.fid,
          },
        },
        {
          query: GET_COMPANIES_WITH_FAIR_STATUS,
          variables: {
            fairId: route.params.fid,
          },
        },
      ],
    },
  )

  const { mutate: updateParticipation, loading: loadingUpdate } = useMutation(
    UPDATE_COMPANY_FAIR_PARTICIPATION,
  )

  const { mutate: removeParticipation, loading: loadingRemove } = useMutation(
    DELETE_COMPANY_FAIR_PARTICIPATION,
    {
      refetchQueries: [
        {
          query: GET_COMPANIES_IN_FAIR,
          variables: {
            fairId: route.params.fid,
          },
        },
      ],
    },
  )

  const {
    mutate: removeCompanyFromFair,
    loading: loadingRemoveCompanyFromFair,
  } = useMutation(REMOVE_COMPANY_FROM_FAIR, {
    refetchQueries: [
      {
        query: GET_COMPANIES_IN_FAIR,
        variables: {
          fairId: route.params.fid,
        },
      },
      {
        query: GET_COMPANIES_WITH_FAIR_STATUS,
        variables: {
          fairId: route.params.fid,
        },
      },
    ],
  })

  return {
    state: {
      loadingCreate,
      loadingRemove,
      loadingUpdate,
      loadingRemoveCompanyFromFair,
    },
    actions: {
      updateParticipation,
      removeParticipation,
      createParticipation,
      removeCompanyFromFair,
    },
  }
}
