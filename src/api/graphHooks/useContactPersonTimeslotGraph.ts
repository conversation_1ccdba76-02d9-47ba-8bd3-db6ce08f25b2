import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import {
  CREATE_CONTACT_PERSON_TIMESLOT,
  UPDATE_CONTACT_PERSON_TIMESLOT,
  REMOVE_CONTACT_PERSON_TIMESLOT,
  BULK_DELETE_CONTACT_PERSON_TIMESLOTS,
} from '@/api/graphql/mutations/contactPersonTimeslotMutation'
import { GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useRoute } from 'vue-router'

export const useContactPersonTimeslotGraph = () => {
  provideApolloClient(apolloClient)

  const selectedTimeslot = ref(null)
  const route = useRoute()

  const { mutate: createTimeslotMutation, loading: loadingCreate } =
    useMutation(CREATE_CONTACT_PERSON_TIMESLOT, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  const { mutate: updateTimeslotMutation, loading: loadingUpdate } =
    useMutation(UPDATE_CONTACT_PERSON_TIMESLOT, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  const { mutate: removeTimeslotMutation, loading: loadingRemove } =
    useMutation(REMOVE_CONTACT_PERSON_TIMESLOT, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  const { mutate: bulkDeleteTimeslotsMutation, loading: loadingBulkDelete } =
    useMutation(BULK_DELETE_CONTACT_PERSON_TIMESLOTS, {
      refetchQueries: [
        {
          query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
          variables: {
            fairId: route.params.fid,
            companyId: route.params.cid,
          },
        },
      ],
    })

  return {
    state: {
      selectedTimeslot,
      loadingCreate,
      loadingUpdate,
      loadingRemove,
      loadingBulkDelete,
    },
    actions: {
      createTimeslotMutation,
      updateTimeslotMutation,
      removeTimeslotMutation,
      bulkDeleteTimeslotsMutation,
    },
  }
}
