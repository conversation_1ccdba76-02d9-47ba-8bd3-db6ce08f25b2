import { apolloClient } from '@/api/middleware/apolloClient'
import { useQuery, provideApolloClient } from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import { GET_FAIR_BY_COMPANY_ID } from '@/api/graphql/queries/fairQueries'
import { useAuthStore } from '@/stores/authStore'

export const useCompanyFairGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()

  const companyId = computed(() => authStore.companyId)

  const {
    result: getCompanyFair,
    loading: loadingCompanyFair,
    refetch: refetchCompanyFairs,
  } = useQuery(GET_FAIR_BY_COMPANY_ID, () => ({
    companyId: companyId.value,
  }))

  const allCompanyFairs = computed(
    () => getCompanyFair.value?.findFairsByCompanyId ?? [],
  )

  return {
    state: {
      allCompanyFairs,
      loadingCompanyFair,
    },
    actions: {
      refetchCompanyFairs,
    },
  }
}
