import { apolloClient } from '@/api/middleware/apolloClient'
import { useAuthStore } from '@/stores/authStore'
import { useQuery, provideApolloClient } from '@vue/apollo-composable'
import { GET_COMPANY_BY_ID } from '@/api/graphql/queries/companyQueries'
import { useRoute } from 'vue-router'
import { useCompanyStore } from '@/stores/companyStore'

export const useCompanyDetailsGraphData = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()
  const companyStore = useCompanyStore()
  const authStore = useAuthStore()

  const userDetailsString = localStorage.getItem('userDetails')

  const userDetails = userDetailsString ? JSON.parse(userDetailsString) : null

  const companyId = computed(() => {
    const selectedCompanyId =
      route && route.params ? route.params?.cid : authStore?.companyId
    return selectedCompanyId || userDetails?.companyId
  })

  const isFairRouteWithoutCompany = computed(() => {
    return route && route.path
      ? route.path.includes('/fair/fairs/') && !route.path.includes('/company/')
      : false
  })

  const {
    result: companyResults,
    refetch: refetchCompany,
    error: companyError,
    loading: loadingCompany,
  } = useQuery(
    GET_COMPANY_BY_ID,
    () => ({
      companyId: companyId.value || companyStore?.company?.id,
    }),
    {
      fetchPolicy: 'cache-and-network',
      enabled: computed(() => !isFairRouteWithoutCompany.value),
    },
  )

  const companyDetails = computed(() => {
    if (companyError.value) {
      throw companyError.value
    }
    if (loadingCompany.value) {
      return {}
    }
    companyStore.setCompanyDetails(companyResults.value?.companyById)

    return companyResults.value?.companyById ?? {}
  })

  return {
    state: {
      companyDetails,
    },
    actions: {
      refetchCompany,
    },
  }
}
