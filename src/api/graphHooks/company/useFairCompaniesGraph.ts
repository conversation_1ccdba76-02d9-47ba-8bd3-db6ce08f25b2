import { apolloClient } from '@/api/middleware/apolloClient'
import { useQuery, provideApolloClient } from '@vue/apollo-composable'

import { GET_COMPANIES_WITH_FAIR_STATUS } from '@/api/graphql/queries/companyQueries'
import { useRoute } from 'vue-router'

export const useFairCompaniesGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const fairId = computed(() => route.params.fid)

  const {
    result: companiesForFairListResults,
    loading: loadingCompaniesForFairList,
    refetch: loadCompaniesForFairList,
    onResult: onCompaniesForFairListResult,
    error: companiesForFairListError,
  } = useQuery(
    GET_COMPANIES_WITH_FAIR_STATUS,
    { fairId: fairId.value },
    {
      fetchPolicy: 'network-only',
    },
  )

  onCompaniesForFairListResult(() => {
    console.log({
      companiesForFairListResults: companiesForFairListResults.value,
    })
  })

  const companiesForFairList = computed(() => {
    if (companiesForFairListError.value) {
      throw companiesForFairListError.value
    }
    if (loadingCompaniesForFairList.value) {
      return []
    }
    return companiesForFairListResults.value?.allCompaniesWithFairStatus ?? []
  })

  return {
    state: {
      loadingCompaniesForFairList,
      companiesForFairList,
    },
    actions: {
      loadCompaniesForFairList,
    },
  }
}
