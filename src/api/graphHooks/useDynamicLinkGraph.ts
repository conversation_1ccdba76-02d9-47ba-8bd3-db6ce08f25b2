import {
  GET_COMPANY_DYNAMIC_LINK,
  GET_JOB_AD_DYNAMIC_LINK,
} from '@/api/graphql/mutations/dynamicLinkMutations'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'

export const useDynamicLinkGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: getCompanyDynamicLink, loading: companyLinkLoading } =
    useMutation(GET_COMPANY_DYNAMIC_LINK)

  const { mutate: getJobAdDynamicLink, loading: jobAdLinkLoading } =
    useMutation(GET_JOB_AD_DYNAMIC_LINK)

  return {
    state: {
      companyLinkLoading,
      jobAdLinkLoading,
    },
    actions: {
      getCompanyDynamicLink,
      getJobAdDynamicLink,
    },
  }
}
