import {
  CREATE_STALE_IMAGE,
  REMOVE_MULTIPLE_STALE_IMAGES,
} from '@/api/graphql/mutations/staleImagesMutation'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'

export const useStaleImagesGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: createStaleImage, loading: loadingCreateStaleImage } =
    useMutation(CREATE_STALE_IMAGE)

  const {
    mutate: removeMultipleStaleImgs,
    loading: loadingRemoveMultipleStaleImgs,
  } = useMutation(REMOVE_MULTIPLE_STALE_IMAGES)

  return {
    state: {
      loadingCreateStaleImage,
      loadingRemoveMultipleStaleImgs,
    },
    actions: {
      createStaleImage,
      removeMultipleStaleImgs,
    },
  }
}
