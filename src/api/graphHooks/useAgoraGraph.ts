import { GENERATE_AGORA_TOKEN } from '@/api/graphql/queries/agoraQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useJobActions } from '@/composables/JobActions/useJobActions'
import { provideApolloClient, useLazyQuery } from '@vue/apollo-composable'

export const useAgoraGraph = () => {
  provideApolloClient(apolloClient)

  const {
    state: { jobAdLike },
  } = useJobActions()

  const jobActionId = jobAdLike.value?.id

  const {
    result: agoraToken,
    refetch: refetchAgoraToken,
    error: gettingTokenError,
    loading: loadingAgoraToken,
    load: loadAgoraToken,
  } = useLazyQuery(GENERATE_AGORA_TOKEN, { createAgoraInput: { jobActionId } })

  const token = computed(() => {
    if (gettingTokenError.value) {
      throw gettingTokenError
    }
    if (loadingAgoraToken.value) {
      return ''
    }
    return agoraToken.value?.generateAgoraToken?.token ?? ''
  })

  const uid = computed(() => {
    if (gettingTokenError.value) {
      throw gettingTokenError
    }
    if (loadingAgoraToken.value) {
      return ''
    }
    return agoraToken.value?.generateAgoraToken?.uid ?? ''
  })

  return {
    state: {
      token,
      uid,
      loadingAgoraToken,
    },
    actions: {
      refetchAgoraToken,
      loadAgoraToken,
    },
  }
}
