import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useQuery,
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import {
  GET_ALL_FAIRS,
  GET_FAIR_BY_ID,
} from '@/api/graphql/queries/fairQueries'
import {
  CLONE_FAIR,
  CREATE_FAIR,
  UPDATE_FAIR,
} from '@/api/graphql/mutations/fairMutations'
import { GET_COMPANIES_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useFairStore } from '@/stores/fairs/fairStore'

export const useFairGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const fairStore = useFairStore()

  const fairId = computed(() => route.params.fid || fairStore.getFair?.id)

  const {
    result: getAllFairs,
    loading: loadingAllFairs,
    load: loadFairs,
    stop: stopAllFairs,
  } = useLazyQuery(GET_ALL_FAIRS)

  const allFairsList = computed(() => getAllFairs.value?.allFairs ?? [])

  const {
    result: getFair,
    loading: loadingCurrentFair,
    refetch: refetchCurrentFair,
    stop: stopCurrentFair,
    load: loadCurrentFair,
  } = useLazyQuery(
    GET_FAIR_BY_ID,
    () => ({
      id: fairId.value,
    }),
    {
      prefetch: false,
    },
  )

  const currentFair = computed(() => getFair.value?.fair ?? null)

  const {
    result: getCompaniesInFair,
    loading: loadingCompaniesInFair,
    refetch: refetchCompaniesInFair,
    stop: stopCompaniesInFair,
    load: loadCompaniesInFair,
  } = useLazyQuery(
    GET_COMPANIES_IN_FAIR,
    () => ({
      fairId: fairId.value,
    }),
    {
      prefetch: false,
    },
  )

  const companiesInFair = computed(
    () => getCompaniesInFair.value?.findParticipationByFairId ?? null,
  )

  watch(
    () => !fairId.value,
    shouldStop => {
      if (shouldStop) {
        stopCurrentFair()
        stopCompaniesInFair()
      }
    },
  )

  const { mutate: createFair, loading: loadingCreateFair } = useMutation(
    CREATE_FAIR,
    {
      refetchQueries: [{ query: GET_ALL_FAIRS }],
    },
  )

  const { mutate: updateFair, loading: loadingUpdateFair } = useMutation(
    UPDATE_FAIR,
    {
      refetchQueries: [{ query: GET_ALL_FAIRS }],
    },
  )

  const { mutate: cloneFair, loading: loadingCloneFair } = useMutation(
    CLONE_FAIR,
    {
      refetchQueries: [{ query: GET_ALL_FAIRS }],
    },
  )

  return {
    state: {
      allFairsList,
      companiesInFair,
      currentFair,
      loadingAllFairs,
      loadingCurrentFair,
      loadingUpdateFair,
      loadingCompaniesInFair,
      loadingCreateFair,
      loadingCloneFair,
    },
    actions: {
      loadFairs,
      loadCurrentFair,
      loadCompaniesInFair,
      refetchCurrentFair,
      refetchCompaniesInFair,
      createFair,
      cloneFair,
      updateFair,
    },
  }
}
