import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import { VERIFY_FG_ADMIN } from '@/api/graphql/mutations/fgAdminMutations'

export const useFGAdminGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: verifyFGAdmin, loading: verifyFGAdminLoading } =
    useMutation(VERIFY_FG_ADMIN)

  return {
    state: {
      verifyFGAdminLoading,
    },
    actions: {
      verifyFGAdmin,
    },
  }
}
