import { GET_MATCH_CHAT_ROOM } from '@/api/graphql/queries/chatRoomQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useJobAdsStore } from '@/stores/jobAdsStore'
import { provideApolloClient, useQuery } from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import { useFilterStore } from '@/stores/fairs/filterStore'

export const useChatRoomGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()
  const applicantId = (route?.params?.uid ?? '') as string
  const jobAdvertId = route?.params?.id as string
  const filterStore = useFilterStore()

  const isAppointmentRoute = computed(() =>
    route?.path?.includes('/appointments') ?? false,
  )

  const appointmentId = computed(() => {
    return filterStore.activeAppointment?.id || undefined
  })

  const jobAdsStore = useJobAdsStore()

  const jobActionStateValue = computed(() => {
    return jobAdsStore.jobAdLikes[jobAdvertId]?.find(
      jobAction => jobAction.applicantId === applicantId,
    )?.state
  })

  const jobActionIdValue = computed(() => {
    if (jobActionStateValue.value === 'MATCHED') {
      return (
        jobAdsStore.jobAdLikes[jobAdvertId]?.find(
          jobAction => jobAction.applicantId === applicantId,
        )?.jobActionId || undefined
      )
    } else {
      return undefined
    }
  })

  const {
    result: appointmentMatchChatRoom,
    refetch: loadAppointmentMatchChatRoom,
    error: appointmentMatchChatRoomError,
    loading: loadingAppointmentMatchChatRoom,
  } = useQuery(
    GET_MATCH_CHAT_ROOM,
    computed(() => ({
      criteria: {
        appointmentId: appointmentId.value,
      },
    })),
    {
      enabled: appointmentId.value !== undefined,
    },
  )

  const {
    result: jobActionMatchChatRoom,
    refetch: loadJobActionMatchChatRoom,
    error: jobActionMatchChatRoomError,
    loading: loadingJobActionMatchChatRoom,
  } = useQuery(
    GET_MATCH_CHAT_ROOM,
    computed(() => ({
      criteria: {
        jobActionId: jobActionIdValue.value,
      },
    })),
    {
      enabled: !isAppointmentRoute.value && !!jobActionIdValue.value,
    },
  )

  const matchChatRoom = computed(() => {
    if (isAppointmentRoute.value) {
      return appointmentMatchChatRoom.value
    } else {
      return jobActionMatchChatRoom.value
    }
  })

  const matchChatRoomError = computed(() => {
    if (isAppointmentRoute.value) {
      return appointmentMatchChatRoomError.value
    } else {
      return jobActionMatchChatRoomError.value
    }
  })

  const loadingMatchChatRoom = computed(() => {
    if (isAppointmentRoute.value) {
      return loadingAppointmentMatchChatRoom.value
    } else {
      return loadingJobActionMatchChatRoom.value
    }
  })

  const loadMatchChatRoom = () => {
    if (isAppointmentRoute.value) {
      loadAppointmentMatchChatRoom()
    } else {
      loadJobActionMatchChatRoom()
    }
  }

  const chatRoomDetails = computed(() => {
    if (matchChatRoomError.value) return null
    if (loadingMatchChatRoom.value) {
      return {}
    }
    return matchChatRoom.value?.chatRoomByCriteria ?? {}
  })

  const fetchChatRoom = async () => {
    if (jobActionStateValue.value === 'MATCHED' || appointmentId.value) {
      await loadMatchChatRoom()
    }
  }

  return {
    state: {
      chatRoomDetails,
      loadingMatchChatRoom,
      loadingAppointmentMatchChatRoom,
    },
    actions: {
      fetchChatRoom,
    },
  }
}
