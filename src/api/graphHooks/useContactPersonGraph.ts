import { apolloClient } from '@/api/middleware/apolloClient'
import {
  provideApolloClient,
  useMutation,
  useQuery,
} from '@vue/apollo-composable'
import { GET_CONTACT_PERSON_BY_COMPANY_ID } from '@/api/graphql/queries/contactPersonQueries'
import {
  CREATE_CONTACT_PERSON,
  UPDATE_CONTACT_PERSON,
  REMOVE_CONTACT_PERSON,
} from '@/api/graphql/mutations/contactPersonMutation'
import { useRoute } from 'vue-router'

export const useContactPersonGraph = () => {
  provideApolloClient(apolloClient)

  //add route to get all companyId
  const route = useRoute()
  const companyId = route.params.cid

  //Add query to get contact person by company id
  const {
    result: getContactPersonByCompanyId,
    loading: loadingContactPersonByCompanyId,
    refetch: loadContactPersonByCompanyId,
  } = useQuery(
    GET_CONTACT_PERSON_BY_COMPANY_ID,
    { companyId: companyId },
    () => ({
      prefetch: false,
    }),
  )

  const { mutate: createContactPerson, loading: loadingCreateContactPerson } =
    useMutation(CREATE_CONTACT_PERSON)

  const { mutate: updateContactPerson, loading: loadingUpdateContactPerson } =
    useMutation(UPDATE_CONTACT_PERSON)

  const { mutate: removeContactPerson, loading: loadingRemoveContactPerson } =
    useMutation(REMOVE_CONTACT_PERSON, {
      refetchQueries: [GET_CONTACT_PERSON_BY_COMPANY_ID],
    })

  const contactPersonsByCompanyId = computed(
    () => getContactPersonByCompanyId.value?.contactPersonsByCompanyId ?? [],
  )

  const refetchContactPersonByCompanyId = (data: any) => {
    loadContactPersonByCompanyId(data)
  }

  return {
    state: {
      contactPersonsByCompanyId,
      loadingContactPersonByCompanyId,
      loadingRemoveContactPerson,
      loadingCreateContactPerson,
      loadingUpdateContactPerson,
    },
    actions: {
      refetchContactPersonByCompanyId,
      loadContactPersonByCompanyId,
      createContactPerson,
      updateContactPerson,
      removeContactPerson,
    },
  }
}
