import { apolloClient } from '@/api/middleware/apolloClient'
import { useLazyQuery, provideApolloClient } from '@vue/apollo-composable'
import { GET_CATEGORY_LIST } from '@/api/graphql/queries/categoryQueries'

interface Category {
  id: string
  name: string
}

export const useCategoryGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: categories,
    loading: loadingCategoryList,
    error: categoryError,
    refetch: refetchCategory,
    load: loadCategories,
  } = useLazyQuery<{ allJobCategories: Category[] }>(GET_CATEGORY_LIST)

  const categoryList = computed<Category[]>(() => {
    if (categoryError.value) {
      throw categoryError
    }
    if (loadingCategoryList.value) {
      return []
    }
    return categories.value?.allJobCategories ?? []
  })

  return {
    state: {
      categoryList,
      loadingCategoryList,
    },
    actions: {
      refetchCategory,
      loadCategories,
    },
  }
}
