import { VERIFY_SUPER_USER } from '@/api/graphql/mutations/superUserMutations'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'

export const useSuperUserGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: verifySuperUser, loading: verifySuperUserLoading } =
    useMutation(VERIFY_SUPER_USER)

  return {
    state: {
      verifySuperUserLoading,
    },
    actions: {
      verifySuperUser,
    },
  }
}
