import { useJobAdvertGraph } from '@/api/graphHooks/useJobAdvertGraph'
import {
  GET_JOB_AD_STATISTICS,
  GET_JOB_LIKES_BY_JOB_ADVERT_ID,
} from '@/api/graphql/queries/jobAdQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import {
  DECLINE_MATCHED_APPLICANT,
  SET_JOB_ACTION,
} from '@/api/graphql/mutations/jobActionMutation'

export const useJobActionGraph = () => {
  provideApolloClient(apolloClient)

  const {
    actions: { refetchJobAdStats },
  } = useJobAdvertGraph()

  const { mutate: setJobAction, loading: isSettingJobAction } =
    useMutation(SET_JOB_ACTION)

  const {
    mutate: declineMatchedApplicant,
    loading: isDecliningMatchedApplicant,
    onDone: onDeclineMatchedApplicant,
  } = useMutation(DECLINE_MATCHED_APPLICANT, {
    refetchQueries: ['GetJobLikesByJobAdvertId', 'GetJobAdvertsByCompanyId'],
    update: (cache, { data: { declineApplicant } }) => {
      const cacheId = cache.identify({
        id: declineApplicant.id,
        __typename: 'JobAction',
      })
      cache.evict({ id: cacheId })
      cache.gc()
      refetchJobAdStats()
    },
  })

  return {
    state: {
      isDecliningMatchedApplicant,
      isSettingJobAction,
    },
    actions: {
      setJobAction,
      declineMatchedApplicant,
      onDeclineMatchedApplicant,
    },
  }
}
