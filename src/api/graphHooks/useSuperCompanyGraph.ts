import { apolloClient } from '@/api/middleware/apolloClient'
import { useAuthStore } from '@/stores/authStore'
import {
  useQuery,
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import {
  CREATE_COMPANY,
  CREATE_COMPANY_BY_ADMIN,
  DELETE_COMPANY,
  UPDATE_COMPANY,
} from '@/api/graphql/mutations/companyMutation'
import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
import {
  GET_All_COMPANIES,
  GET_COMPANIES_FOR_FAIR_LIST,
  GET_PAGINATED_COMPANIES,
} from '@/api/graphql/queries/companyQueries'
import { useRoute } from 'vue-router'
import { useCompanyStore } from '@/stores/companyStore'

export const useSuperCompanyGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const companyFormData = useCompanyFormStore()
  const companyStore = useCompanyStore()

  const authStore = useAuthStore()

  const isSuper = authStore?.claims?.isSuperUser
  const isFGAdmin = authStore?.claims?.isFGAdmin
  const isAllowed = isSuper || isFGAdmin

  const userDetailsString = localStorage.getItem('userDetails')

  const userDetails = userDetailsString ? JSON.parse(userDetailsString) : null

  const companyId = computed(() => {
    const selectedCompanyId =
      route && route.params ? route.params?.cid : authStore?.companyId
    return selectedCompanyId || userDetails?.companyId
  })

  const { mutate: saveCompany, loading: saveCompanyLoading } = useMutation(
    CREATE_COMPANY,
    () => ({
      variables: {
        companyInput: {
          companyUserId: userDetails?.companyUserId,
          name: companyFormData.companyName,
          city: companyFormData.city,
          country: companyFormData.address?.split(',').pop(),
          address: companyFormData.address,
          detailContent: companyFormData.detailContent,
          foundingYear: companyFormData?.date,
          headerImageUrl: companyFormData?.headerSavedUrl,
          logoImageUrl: companyFormData?.logoSavedUrl,
          latitude: companyFormData?.position?.lat,
          longitude: companyFormData?.position?.long,
          totalEmployees: companyFormData.mitarbeiter,
        },
      },
    }),
  )
  const { mutate: saveCompanyByAdmin, loading: saveCompanyByAdminLoading } =
    useMutation(
      CREATE_COMPANY_BY_ADMIN,

      () => ({
        refetchQueries: ['GetAllCompanies'],
        variables: {
          companyInput: {
            name: companyFormData.companyName,
            city: companyFormData.city,
            country: companyFormData.address?.split(',').pop(),
            address: companyFormData.address,
            detailContent: companyFormData.detailContent,
            foundingYear: companyFormData?.date,
            headerImageUrl: companyFormData?.headerSavedUrl,
            logoImageUrl: companyFormData?.logoSavedUrl,
            latitude: companyFormData?.position?.lat,
            longitude: companyFormData?.position?.long,
            totalEmployees: companyFormData.mitarbeiter,
          },
        },
      }),
    )
  const { mutate: updateCompany, loading: updateCompanyLoading } =
    useMutation(UPDATE_COMPANY)

  const { mutate: removeCompany, loading: removeCompanyLoading } = useMutation(
    DELETE_COMPANY,
    {
      refetchQueries: ['GetAllCompanies'],
      awaitRefetchQueries: true,
    },
  )

  const {
    result: allCompaniesResult,
    load: loadAllCompanies,
    loading: loadingAllCompanies,
  } = useLazyQuery(GET_All_COMPANIES)

  const paginationParams = ref({ page: 1, limit: 10, search: '' })

  const {
    result: paginatedCompaniesResult,
    load: loadPaginatedCompanies,
    loading: loadingPaginatedCompanies,
    refetch: refetchPaginatedCompanies,
  } = useLazyQuery(
    GET_PAGINATED_COMPANIES,
    () => ({
      paginationInput: paginationParams.value,
    }),
    {
      fetchPolicy: 'network-only',
    },
  )

  const {
    result: companiesForFairListResults,
    loading: loadingCompaniesForFairList,
    load: loadCompaniesForFairList,
    error: companiesForFairListError,
  } = useLazyQuery(GET_COMPANIES_FOR_FAIR_LIST)

  const allCompanies = computed(() => {
    return allCompaniesResult.value?.allCompanies || []
  })

  const paginatedCompanies = computed(() => {
    return (
      paginatedCompaniesResult.value?.paginatedCompanies || {
        items: [],
        meta: {
          totalItems: 0,
          currentPage: 1,
          totalPages: 1,
          itemsPerPage: 10,
          itemCount: 0,
        },
      }
    )
  })

  const updatePaginationParams = async (
    page: number,
    limit: number,
    search: string = '',
  ) => {
    paginationParams.value = { page, limit, search }
    await refetchPaginatedCompanies()
  }

  const companiesForFairList = computed(() => {
    if (companiesForFairListError.value) {
      throw companiesForFairListError.value
    }
    if (loadingCompaniesForFairList.value) {
      return []
    }
    // Return as array to ensure watch in useSuperCompany is triggered
    return companiesForFairListResults.value?.allCompanies ?? []
  })

  return {
    state: {
      saveCompanyLoading,
      updateCompanyLoading,
      loadingAllCompanies,
      loadingPaginatedCompanies,
      allCompanies,
      paginatedCompanies,
      loadingCompaniesForFairList,
      companiesForFairList,
      saveCompanyByAdminLoading,
      removeCompanyLoading,
    },
    actions: {
      updateCompany,
      saveCompany,
      saveCompanyByAdmin,
      loadAllCompanies,
      loadPaginatedCompanies,
      updatePaginationParams,
      loadCompaniesForFairList,
      removeCompany,
    },
  }
}
