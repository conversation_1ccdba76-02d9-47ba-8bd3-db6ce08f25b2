import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useLazyQuery } from '@vue/apollo-composable'
import { GET_USERS_COMPANY_BY_ID } from '@/api/graphql/queries/companyUserQueries'
import { useAuthStore } from '@/stores/authStore'
import { useRoute } from 'vue-router'

export const useCompanyUserListGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()

  const route = useRoute()

  const companyId = authStore.companyId || route.params.cid

  const {
    result: companyUsers,
    refetch: refetchCompanyUsers,
    error: companyUsersError,
    loading: loadingCompanyUsers,
    load: loadCompanyUsers,
  } = useLazyQuery(GET_USERS_COMPANY_BY_ID, {
    companyId: companyId || undefined,
  })

  const companyUsersList = computed(() => {
    if (companyUsersError.value) {
      throw companyUsersError.value
    }
    if (loadingCompanyUsers.value) {
      return []
    }
    return companyUsers.value?.findUsersByCompany ?? []
  })

  return {
    state: {
      companyUsersList,
      loadingCompanyUsers,
    },
    actions: {
      refetchCompanyUsers,
      loadCompanyUsers,
    },
  }
}
