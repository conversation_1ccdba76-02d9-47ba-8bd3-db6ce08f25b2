import {
  MARK_ALL_AS_READ,
  MARK_AS_READ,
} from '@/api/graphql/mutations/notificationMutation'
import { GET_COMPANY_USER_NOTIFICATIONS } from '@/api/graphql/queries/notificationQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useAuthStore } from '@/stores/authStore'
import {
  provideApolloClient,
  useMutation,
  useLazyQuery,
} from '@vue/apollo-composable'

export const useNotificationGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()

  const companyUserId = computed(() => authStore.claims.companyUserId)

  const variables = ref({
    companyUserId: companyUserId.value,
  })

  const isSuper = computed(() => authStore.claims.isSuperUser)
  const isFairAdmin = computed(() => authStore.claims.isFGAdmin)

  const {
    result: userNotifications,
    loading: loadingUserNotifications,
    error: useNotificationsError,
    refetch: refetchUserNotifications,
    load: loadUserNotifications,
  } = useLazyQuery(GET_COMPANY_USER_NOTIFICATIONS, variables.value, {
    enabled: computed(() => !isSuper.value && !!companyUserId.value),
  })

  const userNotificationsList = computed(() => {
    if (isSuper.value) {
      return []
    }
    if (useNotificationsError.value) {
      throw useNotificationsError
    }
    if (loadingUserNotifications.value) {
      return []
    }
    return (userNotifications.value?.companyUserNotifications ?? [])
      .map(
        (notification: {
          id: string
          emailNotification: { emailBody: string; emailSubject: string }
          isNew: boolean
          createdAt: any
        }) => ({
          id: notification.id,
          emailBody: notification.emailNotification?.emailBody,
          isNew: notification?.isNew,
          emailSubject: notification?.emailNotification?.emailSubject,
          createdAt: notification?.createdAt,
          time: notification?.createdAt,
        }),
      )
      .sort(
        (
          a: { createdAt: string | number | Date },
          b: { createdAt: string | number | Date },
        ) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      )
  })

  const { mutate: markAllAsRead, loading: loadingMarkAllAsRead } =
    useMutation(MARK_ALL_AS_READ)

  const { mutate: markAsRead, loading: loadingMarkAsRead } =
    useMutation(MARK_AS_READ)

  return {
    state: {
      userNotificationsList,
      loadingUserNotifications,
      loadingMarkAsRead,
      loadingMarkAllAsRead,
    },
    actions: {
      markAllAsRead,
      markAsRead,
      refetchUserNotifications,
      loadUserNotifications,
    },
  }
}
