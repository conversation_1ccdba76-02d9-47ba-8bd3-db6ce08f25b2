import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import { GET_ALL_FAIR_JOBS } from '@/api/graphql/queries/fairJobQueries'
import {
  CREATE_FAIR_JOB,
  DELETE_FAIR_JOB,
} from '@/api/graphql/mutations/fairJobMutations'

export const useFairJobsGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: getAllFairJobs,
    loading: loadingAllFairJobs,
    refetch: refetchFairJobs,
    load: loadFairJobs,
  } = useLazyQuery(GET_ALL_FAIR_JOBS, undefined, () => ({
    prefetch: false,
  }))

  const allFairJobsList = computed(
    () => getAllFairJobs.value?.allFairJobs ?? [],
  )

  const { mutate: createFairJob, loading: loadingCreateFairJob } = useMutation(
    CREATE_FAIR_JOB,
    {
      refetchQueries: [GET_ALL_FAIR_JOBS],
    },
  )

  const { mutate: deleteFairJob, loading: loadingDeleteFairJob } = useMutation(
    DELETE_FAIR_JOB,
    {
      refetchQueries: [GET_ALL_FAIR_JOBS],
    },
  )

  return {
    state: {
      allFairJobsList,
      loadingAllFairJobs,
      loadingCreateFairJob,
      loadingDeleteFairJob,
    },
    actions: {
      deleteFairJob,
      createFairJob,
      refetchFairJobs,
      loadFairJobs,
    },
  }
}
