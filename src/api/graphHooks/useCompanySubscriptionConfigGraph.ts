import { computed } from 'vue'
import { apolloClient } from '@/api/middleware/apolloClient'
import { ref } from 'vue'
import {
  useQuery,
  useMutation,
  provideApolloClient,
} from '@vue/apollo-composable'
import {
  GET_ALL_COMPANY_SUBSCRIPTION_CONFIGS,
  GET_COMPANY_SUBSCRIPTION_CONFIG,
  GET_COMPANY_SUBSCRIPTION_CONFIG_BY_COMPANY_ID,
} from '@/api/graphql/queries/companySubscriptionConfigQueries'
import {
  CREATE_COMPANY_SUBSCRIPTION_CONFIG,
  UPDATE_COMPANY_SUBSCRIPTION_CONFIG,
  BUL<PERSON>_UPDATE_COMPANY_SUBSCRIPTION_CONFIGS,
  DELETE_COMPANY_SUBSCRIPTION_CONFIG,
} from '@/api/graphql/mutations/companySubscriptionConfigMutation'
import { GET_ALL_PRICING_PLANS } from '@/api/graphql/queries/pricingPlanQueries'
import type { CompanySubscriptionConfig } from '@/composables/CompanySubscriptionConfig/useCompanySubscriptionConfig'

export const useCompanySubscriptionConfigGraph = () => {
  provideApolloClient(apolloClient)

// Query variables
const configsFilter = ref<any>(undefined)
const configId = ref<string | undefined>(undefined)
const companyId = ref<string | undefined>(undefined)

// Query for all subscription configs
const {
  result: allConfigsResult,
  loading: loadingAllConfigs,
  refetch: refetchAllConfigs,
} = useQuery<{
  allCompanySubscriptionConfigs: CompanySubscriptionConfig[]
}>(GET_ALL_COMPANY_SUBSCRIPTION_CONFIGS, () => ({
  filter: configsFilter.value
}), {
  enabled: true,
})

// Query for single config by ID
const {
  result: configByIdResult,
  loading: loadingConfigById,
} = useQuery(GET_COMPANY_SUBSCRIPTION_CONFIG, () => ({
  id: configId.value
}), {
  enabled: computed(() => !!configId.value),
})

// Query for config by company ID
const {
  result: configByCompanyIdResult,
  loading: loadingConfigByCompanyId,
} = useQuery(GET_COMPANY_SUBSCRIPTION_CONFIG_BY_COMPANY_ID, () => ({
  companyId: companyId.value
}), {
  enabled: computed(() => !!companyId.value),
})

// Query for all pricing plans
const {
  result: allPricingPlansResult,
  loading: loadingAllPricingPlans,
  refetch: refetchPricingPlans,
} = useQuery(GET_ALL_PRICING_PLANS)

  // Mutations
  const { mutate: createConfig, loading: creatingConfig } = useMutation(
    CREATE_COMPANY_SUBSCRIPTION_CONFIG,
    {
      refetchQueries: ['GetAllCompanySubscriptionConfigs'],
      awaitRefetchQueries: true,
    }
  )

  const { mutate: updateConfig, loading: updatingConfig } = useMutation(
    UPDATE_COMPANY_SUBSCRIPTION_CONFIG,
    {
      refetchQueries: ['GetAllCompanySubscriptionConfigs'],
      awaitRefetchQueries: true,
    }
  )

  const { mutate: bulkUpdateConfigs, loading: bulkUpdatingConfigs } = useMutation(
    BULK_UPDATE_COMPANY_SUBSCRIPTION_CONFIGS,
    {
      refetchQueries: ['GetAllCompanySubscriptionConfigs'],
      awaitRefetchQueries: true,
    }
  )

  const { mutate: deleteConfig, loading: deletingConfig } = useMutation(
    DELETE_COMPANY_SUBSCRIPTION_CONFIG,
    {
      refetchQueries: ['GetAllCompanySubscriptionConfigs'],
      awaitRefetchQueries: true,
    }
  )

  // Computed values for data access
  const allConfigs = computed(() => {
    return allConfigsResult.value?.allCompanySubscriptionConfigs || []
  })

  const selectedConfig = computed(() => {
    return configByIdResult.value?.companySubscriptionConfig || null
  })

  const configByCompany = computed(() => {
    return configByCompanyIdResult.value?.companySubscriptionConfigByCompanyId || null
  })

  const allPricingPlans = computed(() => {
    return allPricingPlansResult.value?.allPricingPlans || []  
  })

  return {
    // Data
    allConfigs,
    selectedConfig,
    configByCompany,
    allPricingPlans,
    
    // Loading states
    loadingAllConfigs,
    loadingConfigById,
    loadingConfigByCompanyId,
    loadingAllPricingPlans,
    creatingConfig,
    updatingConfig,
    bulkUpdatingConfigs,
    deletingConfig,
    
// Actions
    setFilter: (filter: any) => {
      configsFilter.value = filter
    },
    setConfigId: (id: string | undefined) => {
      configId.value = id
    },
    setCompanyId: (id: string | undefined) => {
      companyId.value = id
    },
    refetchAllConfigs,
    refetchPricingPlans,
    createConfig,
    updateConfig,
    bulkUpdateConfigs,
    deleteConfig,
  }
}
