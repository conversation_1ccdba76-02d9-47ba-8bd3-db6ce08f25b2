import { apolloClient } from '@/api/middleware/apolloClient'
import { useQuery, provideApolloClient } from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import { GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useFairStore } from '@/stores/fairs/fairStore'

export const useCompanyContactPersonGraphData = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const fairStore = useFairStore()

  const fairId = computed(() => route.params.fid || fairStore.getFair?.id)

  const {
    result: getContactPersonsAndJobsInFair,
    loading: loadingContactPersonsAndJobsInFair,
    refetch: refetchContactPersonsAndJobsInFair,
    stop: stopContactPersonsAndJobsInFair,
  } = useQuery(
    GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
    () => ({
      fairId: fairId.value,
      companyId: route.params.cid,
    }),
    {
      enabled: !!fairId.value,
      prefetch: false,
    },
  )

  watch(
    () => !fairId.value,
    shouldStop => {
      if (shouldStop) {
        stopContactPersonsAndJobsInFair()
      }
    },
  )

  watch(
    () => !route.params.cid,
    shouldStop => {
      if (shouldStop) {
        stopContactPersonsAndJobsInFair()
      }
    },
  )

  const fairParticipationByFairAndCompany = computed(() => {
    return (
      getContactPersonsAndJobsInFair.value?.findParticipationByFairAndCompany ??
      null
    )
  })

  const companyContactPersonsInFair = computed(() => {
    const participation =
      getContactPersonsAndJobsInFair.value?.findParticipationByFairAndCompany

    if (!participation) {
      return []
    }

    const { companyFairContactPersons, fair } = participation

    return companyFairContactPersons.map((contactPerson: any) => ({
      ...contactPerson,
      fairDays: fair?.fairDays || [],
    }))
  })

  const companyJobsInFair = computed(
    () =>
      getContactPersonsAndJobsInFair.value?.findParticipationByFairAndCompany
        ?.companyFairJobs ?? [],
  )

  const companyCategories = computed(
    () =>
      getContactPersonsAndJobsInFair.value?.findParticipationByFairAndCompany
        ?.categories ?? [],
  )

  const companyPartnerLinks = computed(
    () =>
      getContactPersonsAndJobsInFair.value?.findParticipationByFairAndCompany
        ?.partnerLinks ?? [],
  )

  const loadContactPersonsAndJobsInFair = async () => {
    await refetchContactPersonsAndJobsInFair()
  }

  return {
    state: {
      companyJobsInFair,
      companyContactPersonsInFair,
      fairParticipationByFairAndCompany,
      loadingContactPersonsAndJobsInFair,
      companyCategories,
      companyPartnerLinks,
    },
    actions: {
      loadContactPersonsAndJobsInFair,
      refetchContactPersonsAndJobsInFair,
    },
  }
}
