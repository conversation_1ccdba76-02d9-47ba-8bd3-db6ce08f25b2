import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'
import { GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR } from '@/api/graphql/queries/companyFairParticipationQueries'
import { useRoute } from 'vue-router'
import {
  CREATE_COMPANY_FAIR_CONTACT_PERSON,
  DELETE_COMPANY_FAIR_CONTACT_PERSON,
} from '@/api/graphql/mutations/companyFairContactPersonMutations'

export const useCompanyFairContactPersonGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const {
    mutate: createCompanyFairContactPerson,
    loading: loadingCompanyCreateFairContactPerson,
  } = useMutation(CREATE_COMPANY_FAIR_CONTACT_PERSON, {
    refetchQueries: [
      {
        query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
        variables: {
          fairId: route.params.fid,
          companyId: route.params.cid,
        },
      },
    ],
  })

  const {
    mutate: deleteCompanyFairContactPerson,
    loading: loadingCompanyDeleteFairContactPersons,
  } = useMutation(DELETE_COMPANY_FAIR_CONTACT_PERSON, {
    refetchQueries: [
      {
        query: GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR,
        variables: {
          fairId: route.params.fid,
          companyId: route.params.cid,
        },
      },
    ],
  })

  return {
    state: {
      loadingCompanyCreateFairContactPerson,
      loadingCompanyDeleteFairContactPersons,
    },
    actions: {
      deleteCompanyFairContactPerson,
      createCompanyFairContactPerson,
    },
  }
}
