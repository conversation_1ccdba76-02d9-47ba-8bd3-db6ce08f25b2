import { apolloClient } from '@/api/middleware/apolloClient'
import { useMutation, provideApolloClient } from '@vue/apollo-composable'
import {
  GET_JOB_ADVERTS_BY_COMPANY_ID,
  GET_PAGINATED_JOB_ADVERTS,
} from '@/api/graphql/queries/jobAdQueries'
import {
  APPROVE_JOB_AD,
  BLOCK_JOB_AD,
  CREATE_JOB_ADVERT,
  EDIT_JOB_ADVERT,
  PAUSE_JOB_AD,
  REMOVE_JOB_AD,
  RESUME_JOB_AD,
} from '@/api/graphql/mutations/jobAdMutations'
import { CreateJobAdvertInput } from '@/gql/graphql'

interface useJobAdvertOptions {
  companyId?: string
  jobAdvertId?: string
}

interface jobAdvertOptions {
  id?: string
  companyId?: string
  categoryId?: string
  editJobAdvertInput?: CreateJobAdvertInput
  createJobAdvertInput?: CreateJobAdvertInput
}

export const useJobAdvertMutationsGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: createJobAdvert } = useMutation(CREATE_JOB_ADVERT, {
    refetchQueries: [GET_PAGINATED_JOB_ADVERTS, GET_JOB_ADVERTS_BY_COMPANY_ID],
  })
  const { mutate: updateJobAdvert } = useMutation(EDIT_JOB_ADVERT)
  const { mutate: deleteJobAdvert } = useMutation(REMOVE_JOB_AD, {
    update: (cache, { data }) => {
      // Get the ID of the deleted jobAd
      const deletedJobAdId = data?.removeJobAdvert?.id

      // Read the current list of jobAds from the cache
      const deleted = cache.readQuery({
        query: GET_JOB_ADVERTS_BY_COMPANY_ID,
      })
    },
  })
  const saveJobAdvert = async (variables: jobAdvertOptions) => {
    const saveAd = await createJobAdvert(variables)
    if (!saveAd?.errors) {
      return saveAd?.data?.createJobAd
    } else {
      throw saveAd?.errors
    }
  }
  const editJobAdvert = async (variables: jobAdvertOptions) => {
    const editJobAd = await updateJobAdvert(variables)
    if (!editJobAd?.errors) {
      return editJobAd?.data?.createJobAd
    } else {
      throw editJobAd?.errors
    }
  }

  const { mutate: approveJobAd } = useMutation(APPROVE_JOB_AD, {
    update: (cache, { data }) => {
      if (data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: pauseJobAd } = useMutation(PAUSE_JOB_AD, {
    update: (cache, { data }) => {
      if (data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: resumeJobAd } = useMutation(RESUME_JOB_AD, {
    update: (cache, { data }) => {
      if (!data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return !data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: blockJobAd } = useMutation(BLOCK_JOB_AD)

  const refreshApolloCache = async () => {
    try {
      await apolloClient.resetStore()
    } catch (e) {
      console.error('Failed to reset Apollo client cache', e)
    }
  }

  return {
    actions: {
      saveJobAdvert,
      editJobAdvert,
      deleteJobAdvert,
      createJobAdvert,
      approveJobAd,
      blockJobAd,
      pauseJobAd,
      resumeJobAd,
      refreshApolloCache,
    },
  }
}
