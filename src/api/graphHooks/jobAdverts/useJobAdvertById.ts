import { apolloClient } from '@/api/middleware/apolloClient'
import { useQuery, provideApolloClient } from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import { GET_JOB_ADVERT_BY_ID } from '@/api/graphql/queries/jobAdQueries'
import { useAppointments } from '@/composables/Appointments/useAppointments'

interface useJobAdvertOptions {
  companyId?: string
  jobAdvertId?: string
}

export const useJobAdvertById = ({
  jobAdvertId = undefined,
}: useJobAdvertOptions = {}) => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const jId = (route && route.params && route.params.id) || jobAdvertId

  const {
    state: { isFairAppointmentModule },
  } = useAppointments()

  const {
    result: singleJobAdvert,
    loading: loadingSingleJobAdvert,
    refetch: refetchSingleJobAd,
  } = useQuery(
    GET_JOB_ADVERT_BY_ID,
    {
      jobAdvertId: jId || '',
    },
    {
      enabled: !!jId && !isFairAppointmentModule,
      fetchPolicy: 'cache-and-network',
      nextFetchPolicy: 'cache-first',
    },
  )

  const jobAdvert = computed(() => {
    return singleJobAdvert.value?.jobAdByIdForCompany || {}
  })

  return {
    state: {
      jobAdvert,
      loadingSingleJobAdvert,
    },
    actions: {
      refetchSingleJobAd,
    },
  }
}
