import {
  CANCEL_SUBSCRIPTION,
  CREATE_BULK_CHECKOUT_SESSION,
  CREATE_CHECKOUT_SESSION,
  CREATE_PORTAL_SESSION,
  CREATE_SETUP_CHECKOUT,
  CREATE_STRIPE_CUSTOMER,
  GENERATE_STRIPE_SUB_INVOICE,
  GET_INVOICE,
  RESUME_SUBSCRIPTION,
  SUBSCRIBE_TO_JOB_ADVERT,
  UPDATE_STRIPE_CUSTOMER,
  GET_COUPON_BY_PROMO_CODE,
  GET_PROMO_CODE_BY_PROMO_CODE,
} from '@/api/graphql/mutations/subscriptionMutation'
import {
  GET_COMPANY_SUBSCRIPTIONS,
  GET_PAYMENT_METHODS,
  GET_STRIPE_CUSTOMER,
} from '@/api/graphql/queries/subscriptionQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import {
  CreateStripeCustomerResponseDto,
  PaymentMethodResponseDto,
  StripeCouponResponseDto,
  StripeCustomerInput,
  StripeSubscription,
} from '@/gql/graphql'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
import {
  provideApolloClient,
  useMutation,
  useLazyQuery,
} from '@vue/apollo-composable'
import { computed, watch } from 'vue'
import {
  GET_JOB_ADVERTS_BY_COMPANY_ID,
  GET_PAGINATED_JOB_ADVERTS,
} from '@/api/graphql/queries/jobAdQueries'
import { useCompanyDetailsGraphData } from '@/api/graphHooks/company/useCompanyDetailsGraphData'

export const useSubscriptionGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()
  const companyStore = useCompanyStore()
  const subStore = useSubscriptionStore()

  const {
    state: { companyDetails },
    actions: { refetchCompany },
  } = useCompanyDetailsGraphData()

  const customerVariables = computed(() => {
    const customerId =
      companyStore?.getCompany?.stripeCustomerId ||
      companyDetails.value.stripeCustomerId
    return {
      customerInput: {
        customerId: customerId || '',
      },
    }
  })

  const { mutate: createCheckoutSession, loading: loadingCheckoutSession } =
    useMutation(CREATE_CHECKOUT_SESSION)

  const { mutate: createSetupCheckout, loading: loadingSetupCheckout } =
    useMutation(CREATE_SETUP_CHECKOUT)

  const {
    mutate: createBulkCheckoutSession,
    loading: loadingBulkCheckoutSession,
  } = useMutation(CREATE_BULK_CHECKOUT_SESSION)

  const { mutate: createPortalSession, loading: loadingPortalSession } =
    useMutation(CREATE_PORTAL_SESSION)

  const { mutate: subscribeToPremium, loading: loadingSubscribeToPremium } =
    useMutation(SUBSCRIBE_TO_JOB_ADVERT, {
      refetchQueries: [
        GET_PAGINATED_JOB_ADVERTS,
        GET_JOB_ADVERTS_BY_COMPANY_ID,
      ],
    })

  const { mutate: generateSubInvoice, loading: loadingGenerateSubInvoice } =
    useMutation(GENERATE_STRIPE_SUB_INVOICE, {
      refetchQueries: [GET_JOB_ADVERTS_BY_COMPANY_ID],
    })

  const { mutate: createStripeCustomer, loading: loadingCreateStripeCustomer } =
    useMutation(CREATE_STRIPE_CUSTOMER, {
      refetchQueries: [GET_STRIPE_CUSTOMER],
      fetchPolicy: 'no-cache',
    })

  const { mutate: updateStripeCustomer, loading: loadingUpdateStripeCustomer } =
    useMutation(UPDATE_STRIPE_CUSTOMER)

  const { mutate: cancelSubscription, loading: loadingCancelSubscription } =
    useMutation(CANCEL_SUBSCRIPTION, {
      refetchQueries: [GET_COMPANY_SUBSCRIPTIONS],
    })

  const { mutate: resumeSubscription, loading: loadingRenewSubscription } =
    useMutation(RESUME_SUBSCRIPTION)

  const { mutate: generateInvoice, loading: loadingGenerateInvoice } =
    useMutation(GET_INVOICE)

  const {
    result: subscription,
    loading: loadingSubscription,
    error: subscriptionError,
    refetch: refetchSubscription,
    load: loadSubscription,
  } = useLazyQuery<{ subscriptionsByCompanyId: StripeSubscription[] }>(
    GET_COMPANY_SUBSCRIPTIONS,
    {
      companyId: authStore.companyId,
    },
    () => ({
      fetchPolicy: 'network-only',
    }),
  )

  const subscriptionList = computed<StripeSubscription[]>(() => {
    if (subscriptionError.value) {
      throw subscriptionError
    }
    if (loadingSubscription.value) {
      return []
    }
    return subscription.value?.subscriptionsByCompanyId ?? []
  })

  // const {
  //   result: coupon,
  //   loading: loadingCoupon,
  //   error: couponError,
  //   refetch: refetchCoupon,
  // } = useQuery<{ retrieveCouponByPromoCode: StripeCouponResponseDto }>(
  //   GET_COUPON_BY_PROMO_CODE,
  //   {
  //     promoCode: subStore.promoCode,
  //   },
  //   () => ({
  //     enabled: !!subStore.promoCode,
  //     fetchPolicy: 'network-only',
  //   }),
  // )

  const { mutate: fetchCoupon, loading: loadingFetchCoupon } = useMutation(
    GET_COUPON_BY_PROMO_CODE,
  )

  const { mutate: fetchPromoCode, loading: loadingFetchPromoCode } =
    useMutation(GET_PROMO_CODE_BY_PROMO_CODE)

  const hasCustomerId = computed(() => {
    return !!(
      companyStore?.getCompany?.stripeCustomerId ||
      companyDetails.value.stripeCustomerId
    )
  })
  if (!hasCustomerId.value) {
    refetchCompany()
  }

  const {
    result: customer,
    loading: loadingCustomer,
    refetch: refetchCustomer,
    load: loadCustomer,
  } = useLazyQuery<{ retrieveStripeCustomer: CreateStripeCustomerResponseDto }>(
    GET_STRIPE_CUSTOMER,
    customerVariables,
    () => ({
      enabled: computed(() => {
        const customerId =
          companyStore?.getCompany?.stripeCustomerId ||
          companyDetails.value.stripeCustomerId
        return !!customerId
      }),
    }),
  )

  const stripeCustomer = computed(() => {
    if (loadingCustomer.value) {
      return {} as StripeCustomerInput
    }
    return customer.value?.retrieveStripeCustomer ?? {}
  })

  // Get the customerId for payment methods
  const paymentMethodsCustomerId = computed(() => {
    return (
      companyStore?.getCompany?.stripeCustomerId ||
      companyDetails.value.stripeCustomerId
    )
  })

  // Create payment methods variables as computed
  const paymentMethodsVariables = computed(() => {
    const customerId = paymentMethodsCustomerId.value
    return {
      customerInput: {
        customerId: customerId || '',
      },
    }
  })

  // Only create the query if we have a valid customerId
  const {
    result: paymentMethodsResult,
    loading: loadingPaymentMethods,
    refetch: refetchPaymentMethods,
    load: loadPaymentMethods,
  } = useLazyQuery<{ retrievePaymentMethods: PaymentMethodResponseDto }>(
    GET_PAYMENT_METHODS,
    paymentMethodsVariables,
    () => ({
      prefetch: false,
      fetchPolicy: 'network-only',
      enabled: computed(() => !!paymentMethodsCustomerId.value),
    }),
  )

  const paymentMethods = computed(() => {
    if (loadingPaymentMethods.value) {
      return []
    }
    subStore.updatePaymentMethod(
      paymentMethodsResult.value?.retrievePaymentMethods ?? {},
    )
    return paymentMethodsResult.value?.retrievePaymentMethods ?? {}
  })

  // Update the loadPaymentMethods function to check for a valid customerId
  const safeLoadPaymentMethods = async () => {
    const customerId = paymentMethodsCustomerId.value
    if (customerId && customerId.trim()) {
      return await loadPaymentMethods()
    }
    console.warn('Cannot load payment methods: customerId is missing or empty')
    return null
  }

  // Safe wrapper for loadCustomer to prevent calling with empty customerId
  const safeLoadCustomer = async () => {
    const customerId =
      companyStore?.getCompany?.stripeCustomerId ||
      companyDetails.value.stripeCustomerId
    if (customerId) {
      return await loadCustomer()
    }
    console.warn('Cannot load customer: customerId is missing or empty')
    return null
  }

  watch(hasCustomerId, async (newValue, oldValue) => {
    if (newValue && !oldValue) {
      await safeLoadCustomer()
      await safeLoadPaymentMethods()
    }
  })

  return {
    state: {
      subscriptionList,
      stripeCustomer,
      paymentMethods,
      loadingFetchCoupon,
      loadingCustomer,
      loadingSubscription,
      loadingCreateStripeCustomer,
      loadingPaymentMethods,
      loadingGenerateInvoice,
      loadingCheckoutSession,
      loadingFetchPromoCode,
      loadingPortalSession,
      loadingSetupCheckout,
      loadingUpdateStripeCustomer,
      loadingSubscribeToPremium,
      loadingCancelSubscription,
      loadingBulkCheckoutSession,
      loadingGenerateSubInvoice,
    },
    actions: {
      subscribeToPremium,
      resumeSubscription,
      createStripeCustomer,
      generateInvoice,
      updateStripeCustomer,
      cancelSubscription,
      createSetupCheckout,
      createCheckoutSession,
      createBulkCheckoutSession,
      createPortalSession,
      generateSubInvoice,
      loadSubscription,
      loadCustomer: safeLoadCustomer,
      loadPaymentMethods: safeLoadPaymentMethods,
      refetchPaymentMethods: async () => {
        const customerId = paymentMethodsCustomerId.value
        if (customerId && customerId.trim()) {
          return await refetchPaymentMethods()
        }
        console.warn(
          'Cannot refetch payment methods: customerId is missing or empty',
        )
        return null
      },
      refetchCustomer,
      fetchCoupon,
      fetchPromoCode,
      refetchSubscription,
    },
  }
}
