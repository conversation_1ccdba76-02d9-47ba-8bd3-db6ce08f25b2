import { computed } from 'vue'
import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useQuery,
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import {
  GET_ACTIVE_PRICING_PLANS,
  GET_PRICING_PLAN,
  GET_ALL_PRICING_PLANS,
} from '@/api/graphql/queries/pricingPlanQueries'
import {
  CREATE_PRICING_PLAN,
  UPDATE_PRICING_PLAN,
  DEACTIVATE_PRICING_PLAN,
} from '@/api/graphql/mutations/pricingPlanMutation'

export const usePricingPlanGraph = () => {
  provideApolloClient(apolloClient)

  // Query for active pricing plans
  const {
    result: activePlansResult,
    loading: loadingActivePlans,
    refetch: refetchActivePlans,
  } = useQuery(GET_ACTIVE_PRICING_PLANS)

  // Query for all pricing plans (admin)
  const {
    load: loadAllPlans,
    result: allPlansResult,
    loading: loadingAllPlans,
    refetch: refetchAllPlans,
  } = useLazyQuery(GET_ALL_PRICING_PLANS)

  // Query for single pricing plan by ID
  const {
    load: loadPlanById,
    result: planByIdResult,
    loading: loadingPlanById,
  } = useLazyQuery(GET_PRICING_PLAN)

  // Mutations
  const { mutate: createPlan, loading: creatingPlan } =
    useMutation(CREATE_PRICING_PLAN, {
      refetchQueries: ['GetActivePricingPlans', 'GetAllPricingPlans'],
      awaitRefetchQueries: true,
    })

  const { mutate: updatePlan, loading: updatingPlan } =
    useMutation(UPDATE_PRICING_PLAN, {
      refetchQueries: ['GetActivePricingPlans', 'GetAllPricingPlans'],
      awaitRefetchQueries: true,
    })

  const { mutate: deactivatePlan, loading: deactivatingPlan } =
    useMutation(DEACTIVATE_PRICING_PLAN, {
      refetchQueries: ['GetActivePricingPlans', 'GetAllPricingPlans'],
      awaitRefetchQueries: true,
    })

  // Computed values for data access

  const activePlans = computed(() => {
    return activePlansResult.value?.activePricingPlans || []
  })

  const allPlans = computed(() => {
    return allPlansResult.value?.allPricingPlans || []
  })

  const selectedPlan = computed(() => {
    return planByIdResult.value?.pricingPlan || null
  })

  return {
    // Data
    activePlans,
    allPlans,
    selectedPlan,
    
    // Loading states
    loadingActivePlans,
    loadingAllPlans,
    loadingPlanById,
    creatingPlan,
    updatingPlan,
    deactivatingPlan,
    
    // Actions
    loadAllPlans,
    loadPlanById,
    refetchActivePlans,
    refetchAllPlans,
    createPlan,
    updatePlan,
    deactivatePlan,
  }
}
