import { CREATE_CHAT_MESSAGE } from '@/api/graphql/mutations/chatMessageMutation'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient, useMutation } from '@vue/apollo-composable'

export const useChatMessageGraph = () => {
  provideApolloClient(apolloClient)

  const { mutate: createChatMessage, loading: isCreatingChatMessage } =
    useMutation(CREATE_CHAT_MESSAGE)

  return {
    state: {
      isCreatingChatMessage,
    },
    actions: {
      createChatMessage,
    },
  }
}
