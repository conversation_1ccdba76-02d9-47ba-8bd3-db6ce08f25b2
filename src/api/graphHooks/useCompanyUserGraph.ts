import {
  UPDATE_COMPANY_USER,
  VERIFY_COMPANY_USER,
} from '@/api/graphql/mutations/companyUserMutations'
import { apolloClient } from '@/api/middleware/apolloClient'
import {
  provideApolloClient,
  useMutation,
  useLazyQuery,
  useQuery,
} from '@vue/apollo-composable'
import {
  GET_COMPANY_USER,
  GET_USERS_COMPANY_BY_ID,
} from '@/api/graphql/queries/companyUserQueries'
import { useAuthStore } from '@/stores/authStore'
import { useRoute } from 'vue-router'
import { useUsersStore } from '@/stores/usersStore'

export const useCompanyUserGraph = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()
  const usersStore = useUsersStore()

  const route = useRoute()

  const companyId = authStore.companyId || route.params.cid
  const companyUserId = authStore.claims.companyUserId
  const isSuperUser = computed(() => authStore.isSuperUser)

  const {
    result: companyUsers,
    refetch: refetchCompanyUsers,
    error: companyUsersError,
    loading: loadingCompanyUsers,
    load: loadCompanyUsers,
  } = useLazyQuery(GET_USERS_COMPANY_BY_ID, {
    companyId: companyId || undefined,
  })

  const companyUsersList = computed(() => {
    if (companyUsersError.value) {
      throw companyUsersError.value
    }
    if (loadingCompanyUsers.value) {
      return []
    }
    return companyUsers.value?.findUsersByCompany ?? []
  })

  const {
    result: companyUser,
    refetch: refetchCompanyUser,
    error: companyUserError,
    loading: loadingCompanyUser,
  } = useQuery(
    GET_COMPANY_USER,
    {
      companyUserId: companyUserId || undefined,
    },
    {
      enabled: computed(() => !isSuperUser.value),
    },
  )

  const companyUserInfo = computed(() => {
    if (isSuperUser.value) {
      return {}
    }
    if (companyUserError.value) {
      throw companyUserError.value
    }
    if (loadingCompanyUser.value) {
      return []
    }
    usersStore.setActiveUser(companyUser.value?.getCompanyUser)
    return companyUser.value?.getCompanyUser ?? {}
  })

  const loadCompanyUser = async () => {
    if (!isSuperUser.value && companyUserId) {
      await refetchCompanyUser()
    }
  }

  const { mutate: updateCompanyUser, loading: updateCompanyUserLoading } =
    useMutation(UPDATE_COMPANY_USER)

  const { mutate: verifyCompanyUser, loading: verifyCompanyUserLoading } =
    useMutation(VERIFY_COMPANY_USER)

  return {
    state: {
      companyUserInfo,
      companyUsersList,
      loadingCompanyUsers,
      loadingCompanyUser,
      updateCompanyUserLoading,
      verifyCompanyUserLoading,
    },
    actions: {
      refetchCompanyUser,
      refetchCompanyUsers,
      loadCompanyUser,
      loadCompanyUsers,
      verifyCompanyUser,
      updateCompanyUser,
    },
  }
}
