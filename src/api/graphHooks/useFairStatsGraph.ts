import { useQuery } from '@vue/apollo-composable'
import { provideApolloClient } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { GET_FAIR_STATS } from '@/api/graphql/queries/fairStatsQueries'
import { useAuthStore } from '@/stores/authStore'

export const useFairStatsGraph = () => {
  provideApolloClient(apolloClient)
  
  const authStore = useAuthStore()
  const isSuperUser = computed(() => authStore.claims?.isSuperUser || false)

  const {
    result: fairStatsResult,
    loading: loadingFairStats,
    error: fairStatsError,
    refetch: refetchFairStats,
  } = useQuery(GET_FAIR_STATS, null, {
    enabled: isSuperUser,
    fetchPolicy: 'cache-and-network',
    nextFetchPolicy: 'cache-first',
  })

  const fairStats = computed(() => {
    if (fairStatsError.value) {
      console.error('Error fetching fair stats:', fairStatsError.value)
      return {
        fairs: 0,
        companies: 0,
        appointments: 0,
        chatRooms: 0,
      }
    }
    
    if (loadingFairStats.value) {
      return {
        fairs: 0,
        companies: 0,
        appointments: 0,
        chatRooms: 0,
      }
    }
    
    return fairStatsResult.value?.fairStats || {
      fairs: 0,
      companies: 0,
      appointments: 0,
      chatRooms: 0,
    }
  })

  return {
    state: {
      fairStats,
      loadingFairStats,
      fairStatsError,
    },
    actions: {
      refetchFairStats,
    },
  }
}
