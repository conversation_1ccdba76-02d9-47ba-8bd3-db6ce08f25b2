import { computed, ref } from 'vue'
import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useQuery,
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import {
  GET_COMPANY_SUBSCRIPTION_STATUS,
  CAN_CREATE_JOB_ADVERT,
  GET_SUBSCRIPTION_METRICS,
  GET_ALL_SUBSCRIPTIONS,
} from '@/api/graphql/queries/companySubscriptionQueries'
import {
  CREATE_COMPANY_CHECKOUT_SESSION,
  CANCEL_COMPANY_SUBSCRIPTION,
  CHANGE_COMPANY_SUBSCRIPTION_PLAN,
  INCREMENT_JOB_ADVERT_USAGE,
} from '@/api/graphql/mutations/companySubscriptionMutation'

export const useCompanySubscriptionGraph = () => {
  provideApolloClient(apolloClient)

  // Queries
  // Make company subscription status lazy so super admin pages don't trigger it
  const {
    load: loadSubscriptionStatus,
    result: subscriptionStatusResult,
    loading: loadingSubscriptionStatus,
    refetch: refetchSubscriptionStatus,
  } = useLazyQuery(GET_COMPANY_SUBSCRIPTION_STATUS)

  const {
    load: checkCanCreateJobAdvert,
    result: canCreateJobAdvertResult,
    loading: loadingCanCreateJobAdvert,
  } = useLazyQuery(CAN_CREATE_JOB_ADVERT)

  const {
    load: loadSubscriptionMetrics,
    result: subscriptionMetricsResult,
    loading: loadingSubscriptionMetrics,
  } = useLazyQuery(GET_SUBSCRIPTION_METRICS)

  // Mutations
  const { mutate: createCheckoutSession, loading: creatingCheckoutSession } =
    useMutation(CREATE_COMPANY_CHECKOUT_SESSION, {
      refetchQueries: ['GetCompanySubscriptionStatus'],
      awaitRefetchQueries: false,
    })

  const { mutate: cancelSubscription, loading: cancelingSubscription } =
    useMutation(CANCEL_COMPANY_SUBSCRIPTION, {
      refetchQueries: [
        'GetCompanySubscriptionStatus',
        'GetSubscriptionMetrics',
      ],
      awaitRefetchQueries: true,
    })

  const { mutate: changePlan, loading: changingPlan } = useMutation(
    CHANGE_COMPANY_SUBSCRIPTION_PLAN,
    {
      refetchQueries: [
        'GetCompanySubscriptionStatus',
        'GetSubscriptionMetrics',
      ],
      awaitRefetchQueries: true,
    },
  )

  const { mutate: incrementUsage, loading: incrementingUsage } = useMutation(
    INCREMENT_JOB_ADVERT_USAGE,
    {
      refetchQueries: [
        'GetCompanySubscriptionStatus',
        'GetSubscriptionMetrics',
        'CanCreateJobAdvert',
      ],
      awaitRefetchQueries: true,
    },
  )

  // Reset billing usage mutation - commented out as not available in backend yet
  // const {
  //   mutate: resetBillingUsage,
  //   loading: resettingBillingUsage,
  // } = useMutation(RESET_BILLING_USAGE, {
  //   refetchQueries: [
  //     'GetCompanySubscriptionStatus',
  //     'GetSubscriptionMetrics',
  //   ],
  //   awaitRefetchQueries: true,
  // })
  const resetBillingUsage = null
  const resettingBillingUsage = ref(false)

  // Super Admin Functions - real data
  // Load all company subscriptions (super admin view)
  const {
    load: loadAllSubscriptions,
    result: allSubscriptionsResult,
    loading: loadingAllSubscriptions,
  } = useLazyQuery(GET_ALL_SUBSCRIPTIONS)

  // Normalized subscriptions for UI consumption
  const allSubscriptions = computed(() => {
    const data = allSubscriptionsResult.value?.allSubscriptions
    if (!data || data.length === 0) return [] as any[]

    return data.map((sub: any) => {
      const company = sub.company || {}

      return {
        id: sub.id,
        companyId: company.id,
        companyName: company.name,
        planId: undefined,
        planName: sub.plan,
        status: sub.status,
        billingPeriod: undefined,
        monthlyAmount: typeof sub.amountTotal === 'number' ? sub.amountTotal : 0,
        jobAdvertLimit: undefined,
        jobAdvertsUsed: undefined,
        createdAt: undefined,
        expiresAt: sub.expiresAt,
        cancelAtPeriodEnd: sub.cancelAtPeriodEnd,
      }
    })
  })

  const reactivateSubscription = async (subscriptionId: string) => {
    // TODO: Implement backend mutation
    console.log('Reactivating subscription:', subscriptionId)
  }

  // Computed values
  const subscriptionStatus = computed(() => {
    return subscriptionStatusResult.value?.companySubscriptionStatus || null
  })

  const canCreateJobAdvert = computed(() => {
    return canCreateJobAdvertResult.value?.canCreateJobAdvert || false
  })

  const subscriptionMetrics = computed(() => {
    return subscriptionMetricsResult.value?.subscriptionMetrics || null
  })

  const hasActiveSubscription = computed(() => {
    return subscriptionStatus.value?.hasActiveSubscription || false
  })

  const isUnlimitedPlan = computed(() => {
    return subscriptionStatus.value?.jobAdvertsLimit === -1
  })

  const remainingJobAdverts = computed(() => {
    const status = subscriptionStatus.value
    if (!status) return 0
    if (status.jobAdvertsLimit === -1) return Infinity
    return Math.max(0, (status.jobAdvertsLimit || 0) - (status.jobAdvertsUsed || 0))
  })

  const usagePercentage = computed(() => {
    const status = subscriptionStatus.value
    if (!status || status.jobAdvertLimit === -1) return 0
    return Math.min(100, (status.jobAdvertsUsed / status.jobAdvertLimit) * 100)
  })

  return {
    state: {
      subscriptionStatus,
      canCreateJobAdvert,
      subscriptionMetrics,
      hasActiveSubscription,
      isUnlimitedPlan,
      remainingJobAdverts,
      usagePercentage,
      loadingSubscriptionStatus,
      loadingCanCreateJobAdvert,
      loadingSubscriptionMetrics,
      creatingCheckoutSession,
      cancelingSubscription,
      changingPlan,
      incrementingUsage,
      resettingBillingUsage: resettingBillingUsage.value,
      // Super Admin States
      allSubscriptions,
      loadingAllSubscriptions,
    },
    actions: {
      checkCanCreateJobAdvert,
      loadSubscriptionMetrics,
      refetchSubscriptionStatus,
      createCheckoutSession,
      cancelSubscription,
      changePlan,
      incrementUsage,
      resetBillingUsage,
      // Super Admin Actions
      loadAllSubscriptions,
      reactivateSubscription,
      // Company Actions
      loadSubscriptionStatus,
    },
  }
}
