import { apolloClient } from '@/api/middleware/apolloClient'
import { useAuthStore } from '@/stores/authStore'
import {
  provideApolloClient,
  useLazyQuery,
  useMutation,
  useQuery,
} from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import {
  ALL_APPLICANTS,
  DELETE_APPLICANT,
  GET_APPLICANT,
  PAGINATED_APPLICANTS,
} from '@/api/graphql/queries/applicant'
import { useAppStore } from '@/stores/appStore'
import { Applicant as GqlApplicant } from '@/gql/graphql'

interface useApplicantOptions {
  applicantId?: string
}

export const useApplicantGraph = ({
  applicantId = '',
}: useApplicantOptions = {}) => {
  provideApolloClient(apolloClient)

  const route = useRoute()

  const authStore = useAuthStore()
  const appStore = useAppStore()

  const isAppointmentRoute = computed(() =>
    route.path.includes('/fair/appointments'),
  )

  const { isSuperUser: isSuper } = authStore
  const { isInCompanyView } = appStore

  const companyAppId = (route.params.uid as string) || applicantId
  const superAppId = route.params.id as string

  const appId = computed(() => {
    if (isAppointmentRoute.value) {
      return route.params.uid as string
    }
    return isSuper && !isInCompanyView ? superAppId : companyAppId
  })

  const {
    result: applicantById,
    loading: loadingApplicantById,
    error: applicantByIdError,
    refetch: refetchApplicantById,
  } = useQuery(
    GET_APPLICANT,
    {
      applicantId: appId.value,
    },
    {
      enabled: !!appId.value,
      fetchPolicy: 'cache-and-network',
      nextFetchPolicy: 'cache-first',
    },
  )

  const applicant = computed<GqlApplicant>(() => {
    if (applicantByIdError.value) {
      throw applicantByIdError
    }
    if (loadingApplicantById.value) {
      return {}
    }
    return applicantById.value?.getApplicant ?? {}
  })

  const {
    result: allApplicants,
    refetch: refetchAllApplicants,
    error: allApplicantErrors,
    loading: loadingAllApplicants,
    load: loadAllApplicants,
  } = useLazyQuery(ALL_APPLICANTS)

  const allApplicantsList = computed(() => {
    if (allApplicantErrors.value) {
      throw allApplicantErrors.value
    }
    if (loadingAllApplicants.value) {
      return []
    }
    console.log({ allApplicants: allApplicants.value?.allApplicants })
    return allApplicants.value?.allApplicants ?? []
  })

  const paginationParams = ref({ page: 1, limit: 10, search: '' })

  const {
    result: paginatedApplicantsResult,
    loading: loadingPaginatedApplicants,
    load: loadPaginatedApplicants,
    refetch: refetchPaginatedApplicants,
  } = useLazyQuery(
    PAGINATED_APPLICANTS,
    () => ({
      paginationInput: paginationParams.value,
    }),
    {
      fetchPolicy: 'network-only',
    },
  )

  const paginatedApplicants = computed(() => {
    return (
      paginatedApplicantsResult.value?.paginatedApplicants || {
        items: [],
        meta: {
          totalItems: 0,
          currentPage: 1,
          totalPages: 1,
          itemsPerPage: 10,
          itemCount: 0,
        },
      }
    )
  })

  const updatePaginationParams = async (
    page: number,
    limit: number,
    search: string = '',
  ) => {
    paginationParams.value = { page, limit, search }
    await refetchPaginatedApplicants()
  }

  const refetchApplicant = async () => {
    if (!appId.value) return
    await refetchApplicantById()
  }

  const { mutate: deleteApplicant, loading: deleteApplicantLoading } =
    useMutation(DELETE_APPLICANT)

  return {
    state: {
      applicant,
      deleteApplicantLoading,
      loadingApplicantById,
      loadingAllApplicants,
      loadingPaginatedApplicants,
      allApplicantsList,
      paginatedApplicants,
    },
    actions: {
      deleteApplicant,
      refetchApplicant,
      refetchAllApplicants,
      loadAllApplicants,
      loadPaginatedApplicants,
      updatePaginationParams,
    },
  }
}
