export interface BaseJobAd {
  id: string
  title: string
  city: string
  address: string
  district: string
  responsibleUsersIds: string[]
  description: string
  dynamicLink: string
  detailDescription: string
  categories: string[]
  coordinates: { lat: number; lng: number }
  startDate: string
  activeFromDate: string
  headerImage?: string
  jobAdType: string
  paused?: boolean
}

export interface Ausbildung extends BaseJobAd {
  educationDuration: string
  workHours: string
  holidayDays: string
  gehalt: number
  gehalt2: number
  gehalt3: number
}
export type Praktikum = BaseJobAd

export interface AppointmentFilterOption {
  id: string
  text: string
}

export interface AppointmentFilterOptions {
  applicants: AppointmentFilterOption[]
  companies: AppointmentFilterOption[]
  contactPersons: AppointmentFilterOption[]
  fairs: AppointmentFilterOption[]
}

export interface AppointmentTimeRange {
  startTime: Date | null
  endTime: Date | null
}

export interface AppointmentFilterInput {
  applicantId?: string | null
  contactPersonId?: string | null
  companyId?: string | null
  fairId?: string | null
  status?: string | null
  timeRange?: AppointmentTimeRange | null
  date?: Date | string | null
}

export interface AppointmentFilterApiInput {
  applicantId?: string | null
  contactPersonId?: string | null
  companyId?: string | null
  fairId?: string | null
  status?: string | null
  timeRange?: {
    startTime?: Date | null
    endTime?: Date | null
  } | null
  date?: Date | string | null
}
