import gql from 'graphql-tag'

export const CREATE_COMPANY_CHECKOUT_SESSION = gql`
  mutation CreateCompanyCheckoutSession($input: CreateCompanyCheckoutSessionInput!) {
    createCompanyCheckoutSession(input: $input) {
      sessionId
      url
    }
  }
`

export const CANCEL_COMPANY_SUBSCRIPTION = gql`
  mutation CancelCompanySubscription($atPeriodEnd: Boolean!) {
    cancelCompanySubscription(atPeriodEnd: $atPeriodEnd)
  }
`

export const CHANGE_COMPANY_SUBSCRIPTION_PLAN = gql`
  mutation ChangeCompanySubscriptionPlan($planId: String!, $immediate: Boolean!) {
    changeCompanySubscriptionPlan(planId: $planId, immediate: $immediate)
  }
`

export const INCREMENT_JOB_ADVERT_USAGE = gql`
  mutation IncrementJobAdvertUsage($jobAdvertId: String!) {
    incrementJobAdvertUsage(jobAdvertId: $jobAdvertId)
  }
`

export const ADMIN_UPDATE_SUBSCRIPTION = gql`
  mutation AdminUpdateSubscription($companyId: String!, $planId: String!) {
    adminUpdateSubscription(companyId: $companyId, planId: $planId)
  }
`

export const ADMIN_CANCEL_SUBSCRIPTION = gql`
  mutation AdminCancelSubscription($companyId: String!, $reason: String) {
    adminCancelSubscription(companyId: $companyId, reason: $reason)
  }
`
