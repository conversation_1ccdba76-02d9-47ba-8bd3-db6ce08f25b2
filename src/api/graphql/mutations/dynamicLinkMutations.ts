import gql from 'graphql-tag'

export const GET_COMPANY_DYNAMIC_LINK = gql`
  mutation GetCompanyLink(
    $createDynamicLinkInput: CreateCompanyDynamicLinkInput!
  ) {
    createCompanyDynamicLink(createDynamicLinkInput: $createDynamicLinkInput) {
      url
    }
  }
`

export const GET_JOB_AD_DYNAMIC_LINK = gql`
  mutation GetJobAdLink($createDynamicLinkInput: CreateJobAdDynamicLinkInput!) {
    createJobAdDynamicLink(createDynamicLinkInput: $createDynamicLinkInput) {
      url
    }
  }
`
