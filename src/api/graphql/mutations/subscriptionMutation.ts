import gql from 'graphql-tag'

export const CREATE_CHECKOUT_SESSION = gql`
  mutation CreateCheckoutSession(
    $customer: String
    $companyId: String
    $createCheckoutSessionInput: CreateCheckoutSessionInput!
  ) {
    createCheckoutSession(
      customer: $customer
      companyId: $companyId
      createCheckoutSessionInput: $createCheckoutSessionInput
    ) {
      url
    }
  }
`

export const CREATE_SETUP_CHECKOUT = gql`
  mutation CreateSetupCheckout(
    $customer: String
    $companyId: String
    $type: String
  ) {
    createSetupCheckoutDetails(
      customer: $customer
      companyId: $companyId
      type: $type
    ) {
      url
    }
  }
`

export const CREATE_BULK_CHECKOUT_SESSION = gql`
  mutation CreateBulkCheckoutSession(
    $customer: String
    $companyId: String
    $createCheckoutSessionInput: [CreateCheckoutSessionInput!]!
  ) {
    createBulkCheckoutSession(
      customer: $customer
      companyId: $companyId
      createCheckoutSessionInput: $createCheckoutSessionInput
    ) {
      url
    }
  }
`

export const CREATE_PORTAL_SESSION = gql`
  mutation CreatePortalSession(
    $createCheckoutSessionInput: CreatePortalSessionInput!
  ) {
    createPortalSession(
      createCheckoutSessionInput: $createCheckoutSessionInput
    ) {
      url
    }
  }
`

export const CANCEL_SUBSCRIPTION = gql`
  mutation CancelSubscription($subscriptionIdInput: SubscriptionIdInput!) {
    cancelSubscription(subscriptionIdInput: $subscriptionIdInput) {
      id
      status
      current_period_end
      cancel_at_period_end
    }
  }
`

export const RESUME_SUBSCRIPTION = gql`
  mutation ResumeSubscription($subscriptionIdInput: SubscriptionIdInput!) {
    resumeSubscription(subscriptionIdInput: $subscriptionIdInput) {
      id
      status
      current_period_end
      cancel_at_period_end
    }
  }
`

export const GET_INVOICE = gql`
  mutation GetInvoice($invoiceIdInput: InvoiceIdInput!) {
    retrieveSubscriptionInvoice(invoiceIdInput: $invoiceIdInput) {
      id
      hosted_invoice_url
      invoice_pdf
      amount_due
      amount_paid
      amount_remaining
      currency
      customer
      status
    }
  }
`

export const SUBSCRIBE_TO_JOB_ADVERT = gql`
  mutation SubscribeToJobAdvert(
    $createStripeSubscriptionInput: CreateStripeSubscriptionInput!
    $paymentMethodId: String!
  ) {
    subscribeToPremium(
      createStripeSubscriptionInput: $createStripeSubscriptionInput
      paymentMethodId: $paymentMethodId
    ) {
      id
      status
    }
  }
`

export const GENERATE_STRIPE_SUB_INVOICE = gql`
  mutation GenerateSubInvoice(
    $createStripeSubscriptionInput: CreateStripeSubscriptionInput!
    $customerDetails: UpdateStripeCustomerInput!
  ) {
    generateSubscriptionInvoice(
      createStripeSubscriptionInput: $createStripeSubscriptionInput
      customerDetails: $customerDetails
    ) {
      id
    }
  }
`

export const UPDATE_STRIPE_CUSTOMER = gql`
  mutation UpdateStripeCustomer(
    $customerId: String!
    $createStripeCustomerInput: StripeCustomerInput!
  ) {
    updateStripeCustomer(
      customerId: $customerId
      createStripeCustomerInput: $createStripeCustomerInput
    ) {
      id
      email
      name
    }
  }
`

export const CREATE_STRIPE_CUSTOMER = gql`
  mutation CreateStripeCustomer(
    $companyId: String!
    $createStripeCustomerInput: StripeCustomerInput!
  ) {
    createStripeCustomer(
      companyId: $companyId
      createStripeCustomerInput: $createStripeCustomerInput
    ) {
      id
      email
      name
    }
  }
`

export const GET_COUPON_BY_PROMO_CODE = gql`
  mutation GetCouponByPromoCode($promoCode: String!) {
    retrieveStripeCoupon(promoCode: $promoCode) {
      id
      object
      amount_off
      currency
      duration
      duration_in_months
      max_redemptions
      percent_off
      valid
      redeem_by
      times_redeemed
    }
  }
`

export const GET_PROMO_CODE_BY_PROMO_CODE = gql`
  mutation GetPromoCodeByPromoCode($promoCode: String!) {
    retrieveStripePromoCode(promoCode: $promoCode) {
      id
      object
      max_redemptions
      times_redeemed
      coupon {
        id
        object
        amount_off
        currency
        duration
        duration_in_months
        max_redemptions
        percent_off
        valid
        redeem_by
        times_redeemed
      }
    }
  }
`
