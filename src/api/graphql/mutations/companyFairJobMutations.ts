import gql from 'graphql-tag'

export const CREATE_COMPANY_FAIR_JOB = gql`
  mutation CreateCompanyFairJob($input: CreateCompanyFairJobInput!) {
    createCompanyFairJob(createCompanyFairJobInput: $input) {
      id
      companyFairParticipationId
      description
      fairJob {
        id
        title
      }
      fairJobId
      id
    }
  }
`

export const UPDATE_COMPANY_FAIR_JOB = gql`
  mutation UpdateCompanyFairJob($input: UpdateCompanyFairJobInput!) {
    updateCompanyFairJob(updateCompanyFairJobInput: $input) {
      id
      companyFairParticipationId
      description
      fairJob {
        id
        title
      }
      fairJobId
    }
  }
`

export const DELETE_COMPANY_FAIR_JOB = gql`
  mutation DeleteCompanyFairJob($id: String!) {
    removeCompanyFairJob(id: $id) {
      id
    }
  }
`
