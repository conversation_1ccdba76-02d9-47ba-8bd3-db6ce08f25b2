import gql from 'graphql-tag'

export const CREATE_JOB_ADVERT = gql`
  mutation CreateJobAdvert(
    $responsibleUsersIds: [String!]!
    $categoryIds: [String!]!
    $companyId: String!
    $createJobAdvertInput: CreateJobAdvertInput!
  ) {
    createJobAd(
      responsibleUsersIds: $responsibleUsersIds
      categoryIds: $categoryIds
      companyId: $companyId
      createJobAdvertInput: $createJobAdvertInput
    ) {
      id
      title
      address
      approved
      categories {
        name
      }
      city
      latitude
      longitude
      isDeclined
      isDeleted
      activeFromDate
      description
      declineReason
      detailDescription
      district
      educationDuration
      headerImageUrl
      holidayDays
      companyUserId
      startDate
      status
      type
      createdAt
      updatedAt
      companyId
    }
  }
`
export const EDIT_JOB_ADVERT = gql`
  mutation UpdateJobAdvert(
    $id: String!
    $categoryIdsToConnect: [String!]
    $categoryIdsToDisconnect: [String!]
    $responsibleUsersIdsToConnect: [String!]
    $responsibleUsersIdsToDisconnect: [String!]
    $companyId: String!
    $updateJobAdvertInput: UpdateJobAdvertInput!
  ) {
    updateJobAdvert(
      id: $id
      categoryIdsToConnect: $categoryIdsToConnect
      categoryIdsToDisconnect: $categoryIdsToDisconnect
      responsibleUsersIdsToConnect: $responsibleUsersIdsToConnect
      responsibleUsersIdsToDisconnect: $responsibleUsersIdsToDisconnect
      companyId: $companyId
      updateJobAdvertInput: $updateJobAdvertInput
    ) {
      id
      title
      address
      approved
      categories {
        id
        name
      }
      city
      latitude
      longitude
      isDeclined
      responsibleUsers {
        id
        name
        email
      }
      isDeleted
      description
      activeFromDate
      declineReason
      detailDescription
      district
      educationDuration
      headerImageUrl
      holidayDays
      companyUserId
      startDate
      status
      type
      createdAt
      updatedAt
      companyId
    }
  }
`
export const APPROVE_JOB_AD = gql`
  mutation ApproveJobAdvert($jobAdId: String!, $superUserId: String) {
    approveJobAdvert(jobAdId: $jobAdId, superUserId: $superUserId) {
      id
      approved
      isDeclined
      declineReason
    }
  }
`

export const PAUSE_JOB_AD = gql`
  mutation PauseJobAdvert($id: String!) {
    pauseJobAdvert(id: $id) {
      id
      title
      paused
    }
  }
`

export const RESUME_JOB_AD = gql`
  mutation ResumeJobAdvert($id: String!) {
    resumeJobAdvert(id: $id) {
      id
      title
      paused
    }
  }
`

export const REMOVE_JOB_AD = gql`
  mutation RemoveJobAdvert($id: String!) {
    removeJobAdvert(id: $id) {
      id
      title
      isDeleted
    }
  }
`

export const BLOCK_JOB_AD = gql`
  mutation BlockJobAdvert(
    $jobAdId: String!
    $superUserId: String
    $declineReason: String!
  ) {
    blockJobAdvert(
      jobAdId: $jobAdId
      superUserId: $superUserId
      declineReason: $declineReason
    ) {
      id
      approved
      isDeclined
      declineReason
    }
  }
`
