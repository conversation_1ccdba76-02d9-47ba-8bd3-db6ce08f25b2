import gql from 'graphql-tag'

export const CREATE_CONTACT_PERSON = gql`
  mutation CreateContactPerson($input: CreateContactPersonInput!) {
    createContactPerson(createContactPersonInput: $input) {
      id
      name
      position
      email
      phone
      companyId
      company {
        id
        name
      }
      companyFairContactPersons {
        id
        companyFairParticipation {
          id
          fair {
            id
            name
          }
        }
        ContactPersonTimeslot {
          id
          startTime
          endTime
        }
      }
    }
  }
`

export const UPDATE_CONTACT_PERSON = gql`
  mutation UpdateContactPerson($input: UpdateContactPersonInput!) {
    updateContactPerson(updateContactPersonInput: $input) {
      id
      name
      position
      email
      phone
      companyId
      company {
        id
        name
      }
      companyFairContactPersons {
        id
        companyFairParticipation {
          id
          fair {
            id
            name
          }
        }
        ContactPersonTimeslot {
          id
          startTime
          endTime
        }
      }
    }
  }
`

export const REMOVE_CONTACT_PERSON = gql`
  mutation RemoveContact<PERSON>erson($id: String!) {
    removeContact<PERSON>erson(id: $id) {
      id
    }
  }
`
