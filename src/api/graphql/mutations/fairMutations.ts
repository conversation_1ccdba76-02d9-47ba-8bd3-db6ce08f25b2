import gql from 'graphql-tag'

export const CREATE_FAIR = gql`
  mutation CreateFair(
    $input: CreateFairInput!
    $fairDays: [CreateFairDayInput!]!
  ) {
    createFair(createFairInput: $input, fairDays: $fairDays) {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`

export const CLONE_FAIR = gql`
  mutation CloneFair($input: CreateFairInput!, $id: String!) {
    cloneFair(cloneFairInput: $input, id: $id) {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`

export const UPDATE_FAIR = gql`
  mutation UpdateFair($id: String!, $input: UpdateFairInput!) {
    updateFair(id: $id, updateFairInput: $input) {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`
