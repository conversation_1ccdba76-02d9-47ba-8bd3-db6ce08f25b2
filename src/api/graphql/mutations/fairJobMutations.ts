import gql from 'graphql-tag'

export const CREATE_FAIR_JOB = gql`
  mutation CreateFairJob($input: CreateFairJobInput!) {
    createFairJob(createFairJobInput: $input) {
      companyFairJobs {
        companyFairParticipationId
        description
        fairJobId
        id
      }
      id
      title
    }
  }
`

export const DELETE_FAIR_JOB = gql`
  mutation DeleteFairJob($id: String!) {
    removeFairJob(id: $id) {
      id
    }
  }
`
