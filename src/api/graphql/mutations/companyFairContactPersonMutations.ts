import gql from 'graphql-tag'

export const CREATE_COMPANY_FAIR_CONTACT_PERSON = gql`
  mutation CreateCompanyFairContactPerson(
    $input: CreateCompanyFairContactPersonInput!
  ) {
    createCompanyFairContactPerson(
      createCompanyFairContactPersonInput: $input
    ) {
      companyFairParticipation {
        id
      }
      companyFairParticipationId
      contactPerson {
        companyId
        email
        id
        name
        phone
        position
      }
      contactPersonId
      id
    }
  }
`

export const DELETE_COMPANY_FAIR_CONTACT_PERSON = gql`
  mutation DeleteCompanyFairContactPerson($id: String!) {
    removeCompanyFairContactPerson(id: $id) {
      id
    }
  }
`
