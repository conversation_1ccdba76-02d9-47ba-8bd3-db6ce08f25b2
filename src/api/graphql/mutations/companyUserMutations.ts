import gql from 'graphql-tag'

export const CREATE_COMPANY_USER = gql`
  mutation CreateCompanyUser(
    $userId: String!
    $createCompanyUserInput: CreateCompanyUserInput!
  ) {
    createCompanyUser(
      userId: $userId
      createCompanyUserInput: $createCompanyUserInput
    ) {
      id
      userId
      name
      email
      userRights {
        createJobAd
        createUser
        superAdmin
        editCompany
      }
      createdAt
      updatedAt
    }
  }
`

export const CREATE_FIREBASE_AND_COMPANY_USER = gql`
  mutation CreateFirebaseCompanyUser(
    $companyId: String
    $firebaseUser: FirebaseCreateUserDto!
    $userRights: CreateUserRightInput
  ) {
    createFirebaseBridgeUser(
      companyId: $companyId
      firebaseUser: $firebaseUser
      userRights: $userRights
    ) {
      id
      userId
      name
      userRights {
        createJobAd
        createUser
        editCompany
        superAdmin
      }
      email
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_COMPANY_USER = gql`
  mutation UpdateCompanyUser(
    $updateCompanyUserInput: UpdateCompanyUserInput!
    $userRights: CreateUserRightInput
  ) {
    updateCompanyUser(
      updateCompanyUserInput: $updateCompanyUserInput
      userRights: $userRights
    ) {
      id
      userId
      activeCompanyId
      name
      userRights {
        createJobAd
        createUser
        editCompany
        superAdmin
      }
      email
      createdAt
      updatedAt
    }
  }
`

export const VERIFY_COMPANY_USER = gql`
  mutation VerifyCompanyUser($email: String!) {
    verifyCompanyUser(email: $email) {
      id
      email
    }
  }
`
