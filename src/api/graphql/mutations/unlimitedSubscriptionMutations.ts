import gql from 'graphql-tag'

export const CANCEL_SUBSCRIPTION = gql`
  mutation CancelSubscription($subscriptionId: String!) {
    cancelSubscription(
      subscriptionIdInput: { subscriptionId: $subscriptionId }
    ) {
      id
      status
      cancel_at_period_end
    }
  }
`

export const RESUME_SUBSCRIPTION = gql`
  mutation ResumeSubscription($subscriptionId: String!) {
    resumeSubscription(
      subscriptionIdInput: { subscriptionId: $subscriptionId }
    ) {
      id
      status
      cancel_at_period_end
    }
  }
`
