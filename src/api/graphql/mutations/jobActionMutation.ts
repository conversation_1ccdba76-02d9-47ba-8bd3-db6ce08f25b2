import gql from 'graphql-tag'

export const SET_JOB_ACTION = gql`
  mutation SetJobAction(
    $createOrUpdateJobActionInput: CreateUpdateJobActionInput!
  ) {
    setJobAction(createOrUpdateJobActionInput: $createOrUpdateJobActionInput) {
      id
      state
      status
      chatRoomId
      deletedFromApplicant
      deletedFromApplicant
    }
  }
`

export const DECLINE_MATCHED_APPLICANT = gql`
  mutation DeclineMatchedApplicant(
    $jobActionId: String!
    $declineReason: String
  ) {
    declineApplicant(jobActionId: $jobActionId, declineReason: $declineReason) {
      id
      state
      status
      applicant {
        id
      }
    }
  }
`
