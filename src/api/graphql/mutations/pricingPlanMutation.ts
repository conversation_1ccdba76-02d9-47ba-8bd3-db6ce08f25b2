import gql from 'graphql-tag'

export const CREATE_PRICING_PLAN = gql`
  mutation CreatePricingPlan($input: CreatePricingPlanInput!) {
    createPricingPlan(input: $input) {
      id
      name
      displayName
      description
      price
      currency
      billingPeriod
      durationDays
      isPopular
      isCustom
      isActive
      displayOrder
      unlimitedJobAdverts
      hasCustomBranding
      planType
      stripeProductId
      stripePriceId
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_PRICING_PLAN = gql`
  mutation UpdatePricingPlan($input: UpdatePricingPlanInput!) {
    updatePricingPlan(input: $input) {
      id
      name
      displayName
      description
      price
      currency
      billingPeriod
      durationDays
      isPopular
      isCustom
      isActive
      displayOrder
      unlimitedJobAdverts
      hasCustomBranding
      planType
      stripeProductId
      stripePriceId
      createdAt
      updatedAt
    }
  }
`

export const DEACTIVATE_PRICING_PLAN = gql`
  mutation DeactivatePricingPlan($id: ID!) {
    deactivatePricingPlan(id: $id) {
      id
      name
      displayName
      isActive
    }
  }
`
