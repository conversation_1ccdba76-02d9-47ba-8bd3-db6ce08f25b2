import gql from 'graphql-tag'

export const CREATE_TIMESLOT = gql`
  mutation CreateTimeslot($input: CreateTimeslotInput!) {
    createTimeslot(createTimeslotInput: $input) {
      id
      startTime
      endTime
    }
  }
`

export const UPDATE_TIMESLOT = gql`
  mutation UpdateTimeslot($input: UpdateTimeslotInput!) {
    updateTimeslot(updateTimeslotInput: $input) {
      id
      startTime
      endTime
    }
  }
`

export const REMOVE_TIMESLOT = gql`
  mutation RemoveTimeslot($id: String!) {
    removeTimeslot(id: $id) {
      id
    }
  }
`

export const REMOVE_MULTIPLE_TIMESLOTS = gql`
  mutation RemoveMultipleTimeslots($ids: [String!]!) {
    removeMultipleTimeslots(ids: $ids)
  }
`
