import gql from 'graphql-tag'

export const CREATE_JOB_ACTION_HISTORY = gql`
  mutation CreateJobActionHistory(
    $jobActionId: String!
    $createJobActionHistoryInput: CreateJobActionHistoryInput!
  ) {
    createJobActionHistory(
      createJobActionHistoryInput: $createJobActionHistoryInput
      jobActionId: $jobActionId
    ) {
      createdAt
      id
      newState
      prevState
      updatedAt
    }
  }
`
