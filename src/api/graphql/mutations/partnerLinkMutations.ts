import gql from 'graphql-tag'

export const CREATE_PARTNER_LINK = gql`
  mutation CreatePartnerLink($input: CreatePartnerLinkInput!) {
    createPartnerLink(createPartnerLinkInput: $input) {
      id
      name
      url
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_PARTNER_LINK = gql`
  mutation UpdatePartnerLink($input: UpdatePartnerLinkInput!) {
    updatePartnerLink(updatePartnerLinkInput: $input) {
      id
      name
      url
      createdAt
      updatedAt
    }
  }
`

export const DELETE_PARTNER_LINK = gql`
  mutation DeletePartnerLink($id: String!) {
    removePartnerLink(id: $id) {
      id
    }
  }
` 