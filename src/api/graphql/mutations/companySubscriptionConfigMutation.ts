import gql from 'graphql-tag'

export const CREATE_COMPANY_SUBSCRIPTION_CONFIG = gql`
  mutation CreateCompanySubscriptionConfig($input: CreateCompanySubscriptionConfigInput!) {
    createCompanySubscriptionConfig(input: $input) {
      id
      companyId
      company {
        id
        name
        city
        country
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        billingPeriod
      }
      notes
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_COMPANY_SUBSCRIPTION_CONFIG = gql`
  mutation UpdateCompanySubscriptionConfig($input: UpdateCompanySubscriptionConfigInput!) {
    updateCompanySubscriptionConfig(input: $input) {
      id
      companyId
      company {
        id
        name
        city
        country
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        billingPeriod
      }
      notes
      createdAt
      updatedAt
    }
  }
`

export const BULK_UPDATE_COMPANY_SUBSCRIPTION_CONFIGS = gql`
  mutation BulkUpdateCompanySubscriptionConfigs($input: BulkUpdateCompanySubscriptionConfigInput!) {
    bulkUpdateCompanySubscriptionConfigs(input: $input) {
      id
      companyId
      company {
        id
        name
        city
        country
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        billingPeriod
      }
      notes
      updatedAt
    }
  }
`

export const DELETE_COMPANY_SUBSCRIPTION_CONFIG = gql`
  mutation DeleteCompanySubscriptionConfig($id: ID!) {
    deleteCompanySubscriptionConfig(id: $id)
  }
`
