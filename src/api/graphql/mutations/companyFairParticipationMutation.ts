import gql from 'graphql-tag'

export const CREATE_COMPANY_FAIR_PARTICIPATION = gql`
  mutation CreateCompanyFairParticipation(
    $companyId: String!
    $fairId: String!
    $categoryIds: [String!]!
  ) {
    createCompanyFairParticipation(
      createCompanyFairParticipationInput: {
        companyId: $companyId
        fairId: $fairId
        categoryIds: $categoryIds
      }
    ) {
      id
    }
  }
`

export const UPDATE_COMPANY_FAIR_PARTICIPATION = gql`
  mutation UpdateCompanyFairParticipation(
    $input: UpdateCompanyFairParticipationInput!
  ) {
    updateCompanyFairParticipation(
      updateCompanyFairParticipationInput: $input
    ) {
      id
      company {
        id
        name
      }
      fair {
        id
        name
      }
      categories {
        id
        name
      }
      partnerLinks {
        id
        name
        url
      }
    }
  }
`

export const DELETE_COMPANY_FAIR_PARTICIPATION = gql`
  mutation DeleteCompanyFairParticipation($id: String!) {
    removeCompanyFairParticipation(id: $id) {
      id
    }
  }
`

export const REMOVE_COMPANY_FROM_FAIR = gql`
  mutation RemoveCompanyFromFair($companyId: String!, $fairId: String!) {
    removeCompanyFromFair(companyId: $companyId, fairId: $fairId) {
      id
    }
  }
`
