import gql from 'graphql-tag'

export const CREATE_CHAT_MESSAGE = gql`
  mutation CreateChatMessage(
    $applicantId: String
    $chatRoomId: String!
    $companyUserId: String
    $companyFairContactPersonId: String
    $createMessageInput: CreateMessageInput!
  ) {
    createMessage(
      applicantId: $applicantId
      chatRoomId: $chatRoomId
      companyUserId: $companyUserId
      companyFairContactPersonId: $companyFairContactPersonId
      createMessageInput: $createMessageInput
    ) {
      id
      content
      isCompany
      isApplicant
      isSent
      authorName
      isDelivered
      isSeen
      deletedAt
      deletedBy
      deletedById
      isDeleted
    }
  }
`

export const UPDATE_MESSAGE = gql`
  mutation UpdateMessage(
    $applicantId: String!
    $chatRoomId: String!
    $companyUserId: String!
    $id: String!
    $updateMessageInput: UpdateMessageInput!
  ) {
    updateMessage(
      applicantId: $applicantId
      chatRoomId: $chatRoomId
      companyUserId: $companyUserId
      id: $id
      updateMessageInput: $updateMessageInput
    ) {
      id
      content
      isCompany
      isApplicant
      isSent
      authorName
      isDelivered
      isSeen
      deletedAt
      deletedBy
      deletedById
      isDeleted
    }
  }
`

export const MARK_MESSAGES_AS_SEEN = gql`
  mutation MarkMessagesAsSeen($markMessagesAsSeenInput: MarkMessagesAsSeenInput!) {
    markMessagesAsSeen(markMessagesAsSeenInput: $markMessagesAsSeenInput) {
      count
    }
  }
`
