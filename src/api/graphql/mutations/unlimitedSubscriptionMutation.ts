import gql from 'graphql-tag'

// Create unlimited subscription with company subscription model
export const CREATE_UNLIMITED_SUBSCRIPTION = gql`
  mutation CreateUnlimitedSubscription(
    $customerId: String!
    $priceId: String!
    $companyId: String!
    $durationDays: Float!
    $monthlyPrice: Float!
    $paymentMethodId: String
    $promoCode: String
    $couponId: String
    $currency: String
  ) {
    createUnlimitedSubscription(
      createUnlimitedSubscriptionInput: {
        customerId: $customerId
        priceId: $priceId
        companyId: $companyId
        durationDays: $durationDays
        monthlyPrice: $monthlyPrice
        promoCode: $promoCode
        couponId: $couponId
        currency: $currency
      }
      paymentMethodId: $paymentMethodId
    ) {
      id
      hosted_invoice_url
      invoice_pdf
    }
  }
`

// Generate invoice for unlimited subscription
export const GENERATE_UNLIMITED_SUBSCRIPTION_INVOICE = gql`
  mutation GenerateUnlimitedSubscriptionInvoice(
    $customerId: String
    $priceId: String!
    $companyId: String!
    $durationDays: Float!
    $monthlyPrice: Float!
    $customerDetails: UpdateStripeCustomerInput
    $promoCode: String
    $couponId: String
    $currency: String
  ) {
    generateUnlimitedSubscriptionInvoice(
      createUnlimitedSubscriptionInput: {
        customerId: $customerId
        priceId: $priceId
        companyId: $companyId
        durationDays: $durationDays
        monthlyPrice: $monthlyPrice
        promoCode: $promoCode
        couponId: $couponId
        currency: $currency
      }
      customerDetails: $customerDetails
    ) {
      id
      hosted_invoice_url
      invoice_pdf
    }
  }
`

// Activate all job adverts for company with unlimited subscription
export const ACTIVATE_ALL_COMPANY_JOB_ADVERTS = gql`
  mutation ActivateAllJobAdvertsForCompany($companyId: String!) {
    activateAllJobAdvertsForCompany(companyId: $companyId)
  }
`

// Check and sync unlimited subscription status
export const SYNC_UNLIMITED_SUBSCRIPTION = gql`
  mutation SyncUnlimitedSubscription($companyId: String!) {
    syncUnlimitedSubscription(companyId: $companyId)
  }
`
