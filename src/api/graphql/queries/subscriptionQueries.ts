import gql from 'graphql-tag'

export const GET_COMPANY_SUBSCRIPTIONS = gql`
  query GetSubscriptions($companyId: String!) {
    subscriptionsByCompanyId(companyId: $companyId) {
      amountTotal
      stripeCustomerId
      stripeSubscriptionId
      checkoutSessionId
      cancelAtPeriodEnd
      currency
      invoiceId
      percent_off
      isActive
      expiresAt
      paymentStatus
      jobAdvert {
        title
      }
    }
  }
`

export const GET_STRIPE_CUSTOMER = gql`
  query GetStripeCustomer($customerInput: CustomerIdInput!) {
    retrieveStripeCustomer(customerInput: $customerInput) {
      name
      email
      id
      object
    }
  }
`
export const GET_PAYMENT_METHODS = gql`
  query GetPaymentMethods($customerInput: CustomerIdInput!) {
    retrievePaymentMethods(customerInput: $customerInput) {
      data {
        id
        customer
        billing_details {
          address {
            city
            country
            line1
            line2
            postal_code
            state
          }
          email
          name
          phone
        }
        card {
          brand
          country
          exp_month
          exp_year
          last4
        }
        sepa_debit {
          bank_code
          last4
          country
          branch_code
          fingerprint
        }
      }
    }
  }
`
