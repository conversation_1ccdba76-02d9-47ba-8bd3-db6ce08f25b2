import gql from 'graphql-tag'

export const GET_UNLIMITED_SUBSCRIPTIONS = gql`
  query GetUnlimitedSubscriptions($companyId: String!) {
    subscriptionsByCompanyId(companyId: $companyId) {
      id
      subscriptionType
      isActive
      status
      stripeSubscriptionId
      stripePriceId
      amountTotal
      currency
      percent_off
      invoiceId
      createdAt
      expiresAt
      currentPeriodStart
      currentPeriodEnd
      cancelAtPeriodEnd
      hostedInvoiceUrl
      invoicePdf
      metadata
    }
  }
`
