import gql from 'graphql-tag'

const APPLICANT_FRAGMENT = gql`
  fragment ApplicantFields on Applicant {
    id
    firstName
    lastName
    birthDate
    phoneNumber
    profileImageUrl
  }
`

const CONTACT_PERSON_FRAGMENT = gql`
  fragment ContactPersonFields on Contact<PERSON>erson {
    id
    name
    position
    phone
    email
  }
`

const FAIR_FRAGMENT = gql`
  fragment FairFields on Fair {
    id
    name
  }
`

const TIMESLOT_FRAGMENT = gql`
  fragment TimeslotFields on ContactPersonTimeslot {
    id
    startTime
    endTime
    companyFairContactPerson {
      id
      contactPerson {
        ...ContactPersonFields
      }
      companyFairParticipation {
        id
        company {
          name
          id
        }
        fair {
          ...FairFields
        }
      }
    }
  }
  ${CONTACT_PERSON_FRAGMENT}
  ${FAIR_FRAGMENT}
`

const APPOINTMENT_FRAGMENT = gql`
  fragment AppointmentFields on Appointment {
    id
    applicant {
      ...ApplicantFields
    }
    contactPersonTimeslot {
      ...TimeslotFields
    }
    status
    applicantIsNew
    companyIsNew
    reservationDate
  }
  ${APPLICANT_FRAGMENT}
  ${TIMESLOT_FRAGMENT}
`

// Define queries using the fragments
export const GET_ALL_APPOINTMENTS = gql`
  query GetAllAppointments {
    findAllAppointments {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_APPOINTMENT_BY_ID = gql`
  query GetAppointment($id: String!) {
    findAppointment(id: $id) {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_APPOINTMENTS_BY_FAIR = gql`
  query GetAppointmentsByFair($fairId: String!) {
    findAppointmentsByFair(fairId: $fairId) {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_APPOINTMENTS_BY_COMPANY = gql`
  query GetAppointmentsByCompany($companyId: String!) {
    findAppointmentsByCompany(companyId: $companyId) {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_APPOINTMENTS_BY_CONTACT_PERSON = gql`
  query GetAppointmentsByContactPerson($contactPersonId: String!) {
    findAppointmentsByContactPerson(contactPersonId: $contactPersonId) {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_APPOINTMENTS_BY_APPLICANT = gql`
  query GetAppointmentsByApplicant($applicantId: String!) {
    findAppointmentsByApplicant(applicantId: $applicantId) {
      ...AppointmentFields
    }
  }
  ${APPOINTMENT_FRAGMENT}
`

export const GET_FILTER_OPTIONS = gql`
  query GetFilterOptions {
    getFilterOptions {
      applicants {
        id
        text
      }
      companies {
        id
        text
      }
      contactPersons {
        id
        text
      }
      fairs {
        id
        text
      }
    }
  }
`

export const FIND_APPOINTMENTS_WITH_FILTERS = gql`
  query FindAppointmentsWithFilters(
    $filter: AppointmentFilterInput
    $skip: Float
    $take: Float
  ) {
    findAppointmentWithFilters(filter: $filter, skip: $skip, take: $take) {
      items {
        chatRoom {
          messages {
            id
            isSeen
          }
        }
        id
        applicant {
          id
          city
          graduation
          firstName
          environment
          personality
          lastName
          birthDate
          phoneNumber
          profileImageUrl
        }
        contactPersonTimeslot {
          id
          startTime
          endTime
          companyFairContactPerson {
            id
            contactPerson {
              name
              position
              phone
              email
            }
          }
          companyFairContactPerson {
            id
            companyFairParticipation {
              id
              company {
                id
                name
              }
              fair {
                id
                name
              }
            }
          }
        }
        status
        applicantIsNew
        companyIsNew
        rejectReason
        reservationDate
      }
      count
    }
  }
`
