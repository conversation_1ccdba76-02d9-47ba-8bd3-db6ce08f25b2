import gql from 'graphql-tag'

export const GET_USERS_COMPANY_BY_ID = gql`
  query FindUsersByCompany($companyId: String) {
    findUsersByCompany(companyId: $companyId) {
      name
      id
      avatarImageUrl
      activeCompanyId
      email
      companies {
        id
        name
      }
      userRights {
        createUser
        createJobAd
        editCompany
        superAdmin
      }
      createdAt
      updatedAt
    }
  }
`

export const GET_COMPANY_USER = gql`
  query GetCompanyUser($companyUserId: String!) {
    getCompanyUser(companyUserId: $companyUserId) {
      name
      id
      avatarImageUrl
      activeCompanyId
      email
      companies {
        id
        name
      }
      userRights {
        createUser
        createJobAd
        editCompany
        superAdmin
      }
      createdAt
      updatedAt
    }
  }
`
