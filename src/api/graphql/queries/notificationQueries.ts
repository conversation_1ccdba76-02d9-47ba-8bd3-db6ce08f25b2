import gql from 'graphql-tag'
export const GET_COMPANY_USER_NOTIFICATIONS = gql`
  query companyUserNotifications($companyUserId: String!) {
    companyUserNotifications(companyUserId: $companyUserId) {
      createdAt
      id
      updatedAt
      emailNotification {
        emailBody
        emailSubject
      }
      isNew
    }
  }
`
export const GET_APPLICANT_NOTIFICATIONS = gql`
  query companyUserNotifications($applicantId: String!) {
    applicantNotifications(applicantId: $applicantId) {
      companyUser {
        activeCompanyId
        avatarImageUrl
        createdAt
        email
        id
        name
        updatedAt
        userId
      }
      createdAt
      id
      pushNotification {
        body
        title
      }
      updatedAt
    }
  }
`
