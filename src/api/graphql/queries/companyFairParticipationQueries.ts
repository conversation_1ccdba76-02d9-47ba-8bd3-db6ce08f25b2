import gql from 'graphql-tag'

export const GET_COMPANIES_IN_FAIR = gql`
  query GetCompaniesInFair($fairId: String!) {
    findParticipationByFairId(fairId: $fairId) {
      id
      categories {
        id
        imageUrl
        name
      }
      companyFairContactPersons {
        contactPerson {
          email
          id
          name
          phone
        }
      }
      company {
        address
        city
        companyUserId
        detailContent
        dynamicLink
        foundingYear
        headerImageUrl
        logoImageUrl
        id
        name
        totalEmployees
        updatedAt
      }
    }
  }
`

export const GET_COMPANY_CONTACT_PERSONS_AND_JOBS_IN_FAIR = gql`
  query GetContactPersonsAndJobsInFair($fairId: String!, $companyId: String!) {
    findParticipationByFairAndCompany(fairId: $fairId, companyId: $companyId) {
      id
      companyFairContactPersons {
        contactPerson {
          id
          name
          phone
          email
        }
        ContactPersonTimeslot {
          id
          startTime
          endTime
          available
        }
        id
      }
      companyFairJobs {
        fairJobId
        id
        description
        fairJob {
          title
        }
        id
      }
      categories {
        id
        imageUrl
        name
      }
      partnerLinks {
        id
        name
        url
      }
      fair {
        fairDays {
          startTime
          endTime
        }
      }
    }
  }
`
