import gql from 'graphql-tag'
export const GET_APPLICANT = gql`
  query GetApplicant($applicantId: String!) {
    getApplicant(applicantId: $applicantId) {
      id
      firstName
      lastName
      birthDate
      city
      description
      graduation
      phoneNumber
      profileImageUrl
      availableFrom
      personality
      environment
      profileImageUrl
      schoolName
      subjects
      weaknesses
      strengths
      applicantDocuments {
        storage
        name
        url
        documentPreviewUrl
      }
    }
  }
`

export const ALL_APPLICANTS = gql`
  query AllApplicants {
    allApplicants {
      id
      firstName
      lastName
      birthDate
      city
      description
      graduation
      profileImageUrl
      availableFrom
      personality
      environment
      profileImageUrl
      schoolName
      subjects
      strengths
      weaknesses
      applicantDocuments {
        storage
        name
        url
        documentPreviewUrl
      }
    }
  }
`

export const PAGINATED_APPLICANTS = gql`
  query PaginatedApplicants($paginationInput: PaginationInput) {
    paginatedApplicants(paginationInput: $paginationInput) {
      items {
        id
        firstName
        lastName
        birthDate
        city
        description
        graduation
        profileImageUrl
        availableFrom
        personality
        environment
        schoolName
        subjects
        strengths
        weaknesses
      }
      meta {
        totalItems
        itemCount
        itemsPerPage
        totalPages
        currentPage
      }
    }
  }
`

export const DELETE_APPLICANT = gql`
  mutation DeleteApplicant($applicantId: String!) {
    removeApplicant(id: $applicantId) {
      firstName
    }
  }
`
