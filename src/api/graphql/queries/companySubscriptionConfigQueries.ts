import gql from 'graphql-tag'

export const GET_ALL_COMPANY_SUBSCRIPTION_CONFIGS = gql`
  query GetAllCompanySubscriptionConfigs(
    $filter: CompanySubscriptionConfigFilterInput
  ) {
    allCompanySubscriptionConfigs(filter: $filter) {
      id
      companyId
      company {
        id
        name
        city
        country
        address
        totalEmployees
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        currency
        billingPeriod
        durationDays
        stripePriceId
        stripeProductId
        planType
        isActive
      }
      notes
      createdBy
      createdAt
      updatedAt
    }
  }
`

export const GET_COMPANY_SUBSCRIPTION_CONFIG = gql`
  query GetCompanySubscriptionConfig($id: ID!) {
    companySubscriptionConfig(id: $id) {
      id
      companyId
      company {
        id
        name
        city
        country
        address
        totalEmployees
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        currency
        billingPeriod
        durationDays
        stripePriceId
        stripeProductId
        planType
        isActive
      }
      notes
      createdBy
      createdAt
      updatedAt
    }
  }
`

export const GET_COMPANY_SUBSCRIPTION_CONFIG_BY_COMPANY_ID = gql`
  query GetCompanySubscriptionConfigByCompanyId($companyId: ID!) {
    companySubscriptionConfigByCompanyId(companyId: $companyId) {
      id
      companyId
      company {
        id
        name
        city
        country
      }
      subscriptionType
      availablePricingPlans {
        id
        name
        displayName
        price
        currency
        billingPeriod
        durationDays
        stripePriceId
        stripeProductId
        planType
        isActive
      }
      notes
      createdAt
      updatedAt
    }
  }
`
