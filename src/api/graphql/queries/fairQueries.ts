import gql from 'graphql-tag'

export const GET_ALL_FAIRS = gql`
  query GetAllFairs {
    allFairs {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`

export const GET_FAIR_BY_COMPANY_ID = gql`
  query GetFairByCompanyId($companyId: String!) {
    findFairsByCompanyId(companyId: $companyId) {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`

export const GET_FAIR_BY_ID = gql`
  query GetFairById($id: String!) {
    getFair(id: $id) {
      id
      name
      startDate
      endDate
      registrationStartDate
      registrationEndDate
      city
      location
      locationName
      status
      description
      logoImageUrl
      contactPersonName
      contactPersonEmail
      publisherLogoImageUrl
      publisherName
    }
  }
`
