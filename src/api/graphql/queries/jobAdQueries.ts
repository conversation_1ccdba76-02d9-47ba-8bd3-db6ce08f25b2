import gql from 'graphql-tag'

export const GET_JOB_ADVERTS_BY_COMPANY_ID = gql`
  query GetJobAdvertsByCompanyId($companyId: String!) {
    findJobAdsByCompanyIdForCompany(companyId: $companyId) {
      id
      title
      district
      status
      isDeclined
      isDeleted
      isDraft
      dynamicLink
      declineReason
      activeFromDate
      detailDescription
      description
      startDate
      startDate
      paused
      approved
      jobAction {
        state
        deletedFromCompany
      }
      subscriptions {
        id
        plan
        status
        expiresAt
      }
      createdAt
    }
  }
`

export const GET_ALL_JOB_ADVERTS = gql`
  query GetAllJobAdverts {
    allJobAds {
      id
      title
      district
      status
      isDeclined
      isDeleted
      dynamicLink
      companyId
      isDraft
      declineReason
      activeFromDate
      startDate
      paused
      approved
      jobAction {
        state
      }
      subscriptions {
        id
        plan
      }
      createdAt
    }
  }
`

export const GET_JOB_LIKES_BY_JOB_ADVERT_ID = gql`
  query GetJobLikesByJobAdvertId($jobAdvertId: String!) {
    jobLikesByAdvertId(advertId: $jobAdvertId) {
      id
      status
      state
      deletedFromApplicant
      deletedFromCompany
      applicant {
        id
        firstName
        lastName
        birthDate
        graduation
        city
        profileImageUrl
      }
    }
  }
`

export const GET_JOB_AD_STATISTICS_BY_COMPANY = gql`
  query GetJobAdStatisticsByCompany($companyId: String!) {
    jobAdStatsByCompanyId(id: $companyId) {
      bookmarks
      likes
      impressions
      matches
    }
  }
`
export const GET_JOB_AD_STATISTICS = gql`
  query GetJobAdStatisticsByCompany($advertId: String!) {
    jobAdStatsById(id: $advertId) {
      bookmarks
      likes
      impressions
      matches
    }
  }
`

export const GET_JOB_ADVERT_BY_ID = gql`
  query GetJobAdvertById($jobAdvertId: String!) {
    jobAdByIdForCompany(id: $jobAdvertId) {
      id
      address
      city
      description
      detailDescription
      district
      approved
      declineReason
      educationDuration
      headerImageUrl
      holidayDays
      workHours
      gehalt
      activeFromDate
      startDate
      companyUserId
      companyId
      categories {
        id
        name
      }
      headerImageUrl
      holidayDays
      id
      isDeclined
      isDeleted
      isDraft
      responsibleUsers {
        id
        name
        email
      }
      latitude
      longitude
      startDate
      status
      title
      type
      subscriptions {
        id
        plan
      }
      updatedAt
    }
  }
`

export const GET_PAGINATED_JOB_ADVERTS = gql`
  query GetPaginatedJobAdverts($paginationInput: PaginationInput) {
    paginatedJobAds(paginationInput: $paginationInput) {
      items {
        id
        title
        district
        status
        isDeclined
        isDeleted
        dynamicLink
        companyId
        isDraft
        declineReason
        activeFromDate
        startDate
        paused
        approved
        jobAction {
          state
        }
        subscriptions {
          id
          plan
        }
        createdAt
      }
      meta {
        totalItems
        itemCount
        itemsPerPage
        totalPages
        currentPage
      }
    }
  }
`
