import gql from 'graphql-tag'

export const GET_ALL_TIMESLOTS = gql`
  query GetAllTimeslots {
    allTimeslots {
      id
      startTime
      endTime
    }
  }
`

export const GET_TIMESLOT_BY_ID = gql`
  query GetTimeslot($id: String!) {
    getTimeslot(id: $id) {
      id
      startTime
      endTime
    }
  }
`

export const GET_TIMESLOTS_BY_FAIR_ID = gql`
  query GetTimeslotsByFairId($fairId: String!) {
    findTimeslotByFairId(fairId: $fairId) {
      id
      startTime
      endTime
    }
  }
`
