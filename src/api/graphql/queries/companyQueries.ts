import gql from 'graphql-tag'

export const GET_COMPANY_BY_ID = gql`
  query GetCompanyById($companyId: String) {
    companyById(id: $companyId) {
      name
      id
      address
      city
      country
      detailContent
      isFairManaged
      foundingYear
      headerImageUrl
      stripeCustomerId
      logoImageUrl
      latitude
      longitude
      totalEmployees
      dynamicLink
      subscriptionConfig {
        subscriptionType
        availablePricingPlans {
          id
          name
          displayName
          price
          currency
          billingPeriod
          durationDays
          stripePriceId
          stripeProductId
          planType
          isActive
        }
      }
      subscriptions {
        id
        plan
        checkoutSessionId
        amountTotal
        paymentStatus
        status
        currency
        invoiceId
        stripeCustomerId
        stripeSubscriptionId
        stripePriceId
        percent_off
        isActive
        cancelAtPeriodEnd
        canceledAt
        currentPeriodStart
        currentPeriodEnd
        lastPaymentDate
        lastPaymentAmount
        companyId
        jobAdvertId
        pricingPlanId
        pricingPlan {
          id
        }
      }
      createdAt
      updatedAt
      __typename
    }
  }
`
export const GET_All_COMPANIES = gql`
  query GetAllCompanies {
    allCompanies {
      name
      id
      address
      city
      country
      detailContent
      foundingYear
      headerImageUrl
      stripeCustomerId
      logoImageUrl
      latitude
      longitude
      totalEmployees
      dynamicLink
      createdAt
      updatedAt
    }
  }
`

export const GET_PAGINATED_COMPANIES = gql`
  query GetPaginatedCompanies($paginationInput: PaginationInput) {
    paginatedCompanies(paginationInput: $paginationInput) {
      items {
        name
        id
        address
        city
        country
        detailContent
        foundingYear
        headerImageUrl
        stripeCustomerId
        isFairManaged
        logoImageUrl
        latitude
        longitude
        totalEmployees
        dynamicLink
        createdAt
        updatedAt
      }
      meta {
        totalItems
        itemCount
        itemsPerPage
        totalPages
        currentPage
      }
    }
  }
`

export const GET_COMPANIES_FOR_FAIR_LIST = gql`
  query GetCompaniesForFairList {
    allCompanies {
      id
      name
      city
      address
      logoImageUrl
    }
  }
`

export const GET_COMPANIES_WITH_FAIR_STATUS = gql`
  query GetCompaniesWithFairStatus($fairId: String!) {
    allCompaniesWithFairStatus(fairId: $fairId) {
      id
      name
      city
      address
      logoImageUrl
      isInFair
      isFairManaged
    }
  }
`

export const GET_COMPANY_SUBSCRIPTION_PLANS = gql`
  query GetCompanySubscriptionPlans($companyId: String) {
    companySubscriptionPlans(companyId: $companyId) {
      subscriptionType
      availablePlans {
        id
        name
        displayName
        description
        price
        currency
        billingPeriod
        durationDays
        isPopular
        isActive
        unlimitedJobAdverts
        hasCustomBranding
        displayOrder
        planType
        stripeProductId
        stripePriceId
      }
      activeSubscriptions {
        id
        plan
        stripeSubscriptionId
        isActive
        cancelAtPeriodEnd
        currentPeriodStart
        currentPeriodEnd
        status
        pricingPlanId
        pricingPlan {
          id
          name
          displayName
        }
      }
    }
  }
`
