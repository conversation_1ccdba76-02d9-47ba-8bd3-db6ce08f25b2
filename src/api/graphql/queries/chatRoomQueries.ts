import gql from 'graphql-tag'
export const GET_MATCH_CHAT_ROOM = gql`
  query chatRoomByCriteria($criteria: ChatRoomCriteriaInput!) {
    chatRoomByCriteria(criteria: $criteria) {
      id
      messages {
        id
        content
        isCompany
        isApplicant
        isSent
        authorName
        isDelivered
        isSeen
        createdAt
        deletedAt
        deletedBy
        deletedById
        isDeleted
      }
      jobAction {
        id
        applicant {
          id
          firstName
          lastName
          profileImageUrl
        }
      }
      appointment {
        applicant {
          firstName
          lastName
          profileImageUrl
        }
      }
      companyUsers {
        id
        name
        email
        avatarImageUrl
      }
      status
    }
  }
`
