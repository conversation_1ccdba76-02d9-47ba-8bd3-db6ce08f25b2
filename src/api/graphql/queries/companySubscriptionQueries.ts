import gql from 'graphql-tag'

export const GET_COMPANY_SUBSCRIPTION_STATUS = gql`
  query GetCompanySubscriptionStatus($companyId: String!) {
    companySubscriptionStatus(companyId: $companyId) {
      hasActiveSubscription
      expiresAt
      currentPlan {
        id
        name
        displayName
        description
        features
        priceInCents
        currency
        billingPeriod
        maxJobAdverts
        maxUsers
        maxCandidates
      }
      usage {
        currentJobAdverts
        maxJobAdverts
        currentUsers
        maxUsers
        currentCandidates
        maxCandidates
      }
      subscriptionEndDate
      jobAdvertsUsed
      jobAdvertsLimit
      daysRemaining
      autoRenew
      subscriptionType
    }
  }
`

export const CAN_CREATE_JOB_ADVERT = gql`
  query CanCreateJobAdvert($companyId: String!) {
    canCreateJobAdvert(companyId: $companyId)
  }
`

export const GET_SUBSCRIPTION_METRICS = gql`
  query GetSubscriptionMetrics {
    subscriptionMetrics
  }
`

export const GET_COMPANY_SUBSCRIPTION_USAGE = gql`
  query GetCompanySubscriptionUsage {
    companySubscriptionUsage {
      jobAdvertsCreated
      maxJobAdverts
      periodStart
      periodEnd
      remainingJobAdverts
      usagePercentage
    }
  }
`

export const GET_ALL_SUBSCRIPTIONS = gql`
  query GetAllSubscriptions {
    allSubscriptions {
      id
      status
      amountTotal
      currency
      cancelAtPeriodEnd
      expiresAt
      paymentStatus
      plan
      company {
        id
        name
        city
        logoImageUrl
      }
    }
  }
`

export const GET_COMPANY_SUBSCRIPTION_METRICS = gql`
  query GetCompanySubscriptionMetrics {
    companySubscriptionMetrics {
      totalActiveSubscriptions
      totalRevenue
      averageRevenuePerCompany
      topPlans {
        planId
        planName
        subscriberCount
        revenue
      }
      revenueByPeriod {
        period
        revenue
        subscriberCount
      }
    }
  }
`
