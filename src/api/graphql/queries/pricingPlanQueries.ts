import gql from 'graphql-tag'

export const GET_ACTIVE_PRICING_PLANS = gql`
  query GetActivePricingPlans {
    activePricingPlans {
      id
      name
      displayName
      description
      price
      currency
      billingPeriod
      durationDays
      isPopular
      isCustom
      displayOrder
      isActive
      unlimitedJobAdverts
      hasCustomBranding
      planType
      stripeProductId
      stripePriceId
      createdAt
      updatedAt
    }
  }
`

export const GET_PRICING_PLAN = gql`
  query GetPricingPlan($id: ID!) {
    pricingPlan(id: $id) {
      id
      name
      displayName
      description
      price
      currency
      billingPeriod
      durationDays
      isPopular
      isCustom
      displayOrder
      isActive
      unlimitedJobAdverts
      hasCustomBranding
      planType
      stripeProductId
      stripePriceId
      createdAt
      updatedAt
    }
  }
`

// Recommended plan query - to be implemented in backend if needed
// export const GET_RECOMMENDED_PLAN = gql`
//   query GetRecommendedPlan($companySize: Float!) {
//     recommendedPlan(companySize: $companySize) {
//       id
//       name
//       displayName
//       description
//       price
//       currency
//       billingPeriod
//       durationDays
//       isPopular
//       isCustom
//       displayOrder
//       unlimitedJobAdverts
//       hasCustomBranding
//       planType
//       stripeProductId
//       stripePriceId
//     }
//   }
// `

export const GET_ALL_PRICING_PLANS = gql`
  query GetAllPricingPlans {
    allPricingPlans {
      id
      name
      displayName
      description
      price
      currency
      billingPeriod
      durationDays
      isPopular
      isCustom
      displayOrder
      isActive
      unlimitedJobAdverts
      hasCustomBranding
      planType
      stripeProductId
      stripePriceId
      createdAt
      updatedAt
    }
  }
`
