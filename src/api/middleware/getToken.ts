import { auth } from '@/libs/firebase/config'
import router from '@/router'

export async function getIdToken(): Promise<string> {
  try {
    const token = await auth?.currentUser?.getIdToken()
    localStorage.setItem('txid', token || '')

    if (!token) {
      await router.push('/login')
      throw new Error('User is not authenticated')
    }

    return token
  } catch (error) {
    return Promise.reject(error)
  }
}
