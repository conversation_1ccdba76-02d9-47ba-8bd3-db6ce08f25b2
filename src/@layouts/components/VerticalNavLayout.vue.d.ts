import type { PropType } from 'vue';
import type { VerticalNavItems } from '@layouts/types';
declare const _default: import("vue").DefineComponent<{
    navItems: {
        type: PropType<VerticalNavItems>;
        required: true;
    };
    verticalNavAttrs: {
        type: PropType<Record<string, unknown>>;
        default: () => {};
    };
}, () => globalThis.VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    navItems: {
        type: PropType<VerticalNavItems>;
        required: true;
    };
    verticalNavAttrs: {
        type: PropType<Record<string, unknown>>;
        default: () => {};
    };
}>>, {
    verticalNavAttrs: Record<string, unknown>;
}>;
export default _default;
