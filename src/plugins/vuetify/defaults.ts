export default {
  VAlert: {
    density: 'comfortable',
  },
  VAvatar: {
    // ℹ️ Remove after next release
    variant: 'flat',
  },
  VBadge: {
    // set v-badge default color to primary
    color: 'primary',
  },
  VBtn: {
    // set v-btn default color to primary
    color: 'primary',
  },
  VChip: {
    size: 'small',
  },
  VMenu: {
    VList: {
      density: 'compact',
    },
  },
  VPagination: {
    color: 'primary',
    density: 'comfortable',
    variant: 'tonal',
  },
  VTabs: {
    // set v-tabs default color to primary
    color: 'primary',
    density: 'comfortable',
    VSlideGroup: {
      showArrows: true,
    },
  },
  VTooltip: {
    // set v-tooltip default location to top
    location: 'top',
  },
  VList: {
    VListItem: {
      color: 'primary',
    },
  },
  VCheckbox: {
    // set v-checkbox default color to primary
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VRadioGroup: {
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VRadio: {
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VSelect: {
    variant: 'solo',
    color: 'primary',
    hideDetails: 'auto',
  },
  VRangeSlider: {
    // set v-range-slider default color to primary
    color: 'primary',
    thumbSize: 14,
    density: 'comfortable',
    thumbLabel: true,
    hideDetails: 'auto',
  },
  VRating: {
    // set v-rating default color to primary
    color: 'warning',
  },
  VProgressCircular: {
    // set v-progress-circular default color to primary
    color: 'primary',
  },
  VSlider: {
    // set v-slider default color to primary
    color: 'primary',
    hideDetails: 'auto',
    thumbSize: 14,
  },
  VTextField: {
    variant: 'solo',

    // density: 'compact',
    color: 'primary',
    hideDetails: 'auto',
  },
  VAutocomplete: {
    variant: 'solo',
    color: 'primary',
    hideDetails: 'auto',
    noDataText: 'Keine Einträge vorhanden',
  },
  VCombobox: {
    variant: 'solo',
    color: 'primary',
    hideDetails: 'auto',
  },
  VFileInput: {
    variant: 'solo',
    color: 'primary',
    hideDetails: 'auto',
  },
  VTextarea: {
    variant: 'solo',
    density: 'compact',
    color: 'primary',
    hideDetails: 'auto',
  },
  VSwitch: {
    // set v-switch default color to primary
    color: 'primary',
    hideDetails: 'auto',
  },
  VTimeline: {
    lineThickness: 1,
  },
}
