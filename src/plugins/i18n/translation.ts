import { nextTick } from 'vue'
import i18n from '@/plugins/i18n'

interface Trans {
  defaultLocale: string
  supportedLocales: string[]
  currentLocale: string
  switchLanguage(newLocale: string): Promise<void>
  loadLocaleMessages(locale: string): Promise<void>
  isLocaleSupported(locale: string): boolean
  getUserLocale(): { locale: string; localeNoRegion: string }
  getPersistedLocale(): string | null
  guessDefaultLocale(): string
  routeMiddleware(to: any, from: any, next: any): Promise<void>
  i18nRoute(to: any): any
}

const Trans: Trans = {
  get defaultLocale() {
    return import.meta.env.VITE_DEFAULT_LOCALE
  },

  get supportedLocales() {
    return import.meta.env.VITE_SUPPORTED_LOCALES.split(',')
  },

  get currentLocale() {
    return i18n.global.locale.value
  },

  set currentLocale(newLocale: string) {
    i18n.global.locale.value = newLocale
  },

  async switchLanguage(newLocale: string) {
    await this.loadLocaleMessages(newLocale)
    this.currentLocale = newLocale
    document.querySelector('html')!.setAttribute('lang', newLocale)
    localStorage.setItem('locale', newLocale)
  },

  async loadLocaleMessages(locale: string) {
    if (!i18n.global.availableLocales.includes(locale)) {
      const messages = await import(`@/plugins/i18n/locales/${locale}.json`)

      i18n.global.setLocaleMessage(locale, messages.default)
    }

    return nextTick()
  },

  isLocaleSupported(locale: string) {
    return this.supportedLocales.includes(locale)
  },

  getUserLocale() {
    const locale = window.navigator.language || this.defaultLocale

    return {
      locale,
      localeNoRegion: locale.split('-')[0],
    }
  },

  getPersistedLocale() {
    const persistedLocale = localStorage.getItem('locale')

    if (this.isLocaleSupported(persistedLocale as string))
      return persistedLocale
    else return null
  },

  guessDefaultLocale() {
    const userPersistedLocale = this.getPersistedLocale()
    if (userPersistedLocale) return userPersistedLocale

    const userPreferredLocale = this.getUserLocale()

    if (this.isLocaleSupported(userPreferredLocale.locale))
      return userPreferredLocale.locale

    if (this.isLocaleSupported(userPreferredLocale.localeNoRegion))
      return userPreferredLocale.localeNoRegion

    return this.defaultLocale
  },

  async routeMiddleware(to: any, _from: any, next: any) {
    const paramLocale = to.params.locale

    if (!this.isLocaleSupported(paramLocale))
      return next(this.guessDefaultLocale())

    await this.switchLanguage(paramLocale)

    return next()
  },

  i18nRoute(to: any) {
    return {
      ...to,
      params: {
        locale: this.currentLocale,
        ...to.params,
      },
    }
  },
}

export default Trans
