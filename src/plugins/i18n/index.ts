import { createI18n } from 'vue-i18n'

const locale = localStorage.getItem('locale') || 'de'

const messages = Object.fromEntries(
  Object.entries(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    import.meta.glob<{ default: any }>('./locales/*.json', { eager: true }),
  ).map(([key, value]) => [key.slice(10, -5), value.default]),
)

export default createI18n({
  legacy: false,
  locale: locale || import.meta.env.VITE_DEFAULT_LOCALE,
  fallbackLocale: import.meta.env.VITE_FALLBACK_LOCALE || 'en',
  messages,
  missing: (locale, key) => {
    // return the key itself when a translation is missing
    return key
  },
})
