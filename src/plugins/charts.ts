import 'chart.js/auto'
import { Chart as ChartJS } from 'chart.js'
import { Plugin } from 'vue'

const plugin: Plugin = {
  install: () => {
    // Global chart defaults
    ChartJS.defaults.responsive = true
    ChartJS.defaults.maintainAspectRatio = false
    ChartJS.defaults.color = '#666666'
    ChartJS.defaults.font.family = 'Inter'

    // Default animations
    ChartJS.defaults.animation = {
      duration: 1000,
      easing: 'easeInOutQuart',
    }

    // Scale configurations
    ChartJS.defaults.scale.grid.color = '#f0f0f0'
    ChartJS.defaults.scale.grid.borderColor = '#e0e0e0'
  },
}

export default plugin
