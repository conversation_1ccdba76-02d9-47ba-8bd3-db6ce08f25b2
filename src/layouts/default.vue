<script lang="ts" setup>
  import { useSkins } from '@core/composable/useSkins'
  import DefaultLayoutWithVerticalNav from '@/layouts/components/DefaultLayoutWithVerticalNav.vue'

  const { layoutAttrs, injectSkinClasses } = useSkins()

  injectSkinClasses()
</script>

<template>
  <DefaultLayoutWithVerticalNav v-bind="layoutAttrs" />
</template>

<style lang="scss">
  // As we are using `layouts` plugin we need its styles to be imported
  @use '@layouts/styles/default-layout';
</style>
