<script setup lang="ts">
  import {
    superNav,
    companyNav,
    superCompanyNav,
    fgAdminNav,
  } from '@/navigation/vertical'
  import { useAuthStore } from '@/stores/authStore'
  import { useThemeConfig } from '@core/composable/useThemeConfig'
  import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
  import { VerticalNavLayout } from '@layouts'
  import logo from '@images/bridge-logo.png'
  import { useAppStore } from '@/stores/appStore'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useFairs } from '@/composables/Fairs/useFairs'

  const authStore = useAuthStore()
  const appStore = useAppStore()
  const companyStore = useCompanyStore()

  const {
    actions: { exitCompany, exitFairCompany },
  } = useFairs()

  const activeCompany = computed(() => companyStore.getCompany)
  const { appRouteTransition, isLessThanOverlayNavBreakpoint } =
    useThemeConfig()
  const { width: windowWidth } = useWindowSize()

  const isSuper = authStore.isSuperUser
  const isFGAdmin = authStore.isFGAdmin

  const navItems = computed(() => {
    if (isSuper && !appStore.isInCompanyView) {
      return superNav.value
    } else if (isSuper && appStore.isInCompanyView) {
      return superCompanyNav.value
    } else if (isFGAdmin) {
      return fgAdminNav.value
    }
    return companyNav.value
  })

  const leaveCompany = async () => {
    if (appStore.isInFairView) {
      await exitFairCompany()
    } else {
      exitCompany()
    }
  }
</script>

<template>
  <VerticalNavLayout :nav-items="navItems">
    <template #vertical-nav-header>
      <VImg max-width="140" :src="logo" />
    </template>

    <!-- 👉 navbar -->
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div v-if="authStore.isLoggedIn" class="d-flex h-100 align-center">
        <VBtn
          v-if="isLessThanOverlayNavBreakpoint(windowWidth)"
          icon
          variant="text"
          color="default"
          class="ms-n3"
          size="small"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon icon="tabler-menu-2" size="24" />
        </VBtn>
        <NavbarThemeSwitcher />

        <VSpacer />

        <v-chip
          v-if="isSuper && appStore.isInCompanyView"
          link
          pill
          size="x-large"
          close-icon="mdi-delete"
        >
          <v-avatar start>
            <v-img
              v-if="activeCompany?.logoImageUrl"
              :src="activeCompany.logoImageUrl"
            ></v-img>
          </v-avatar>

          {{ activeCompany?.name }}
          <VBtn color="primary" variant="text" @click="leaveCompany">
            <v-icon class="mr-1" icon="tabler-door-exit"></v-icon>
            Verlassen
          </VBtn>
        </v-chip>

        <VSpacer />
        <NavBarNotifications class="mr-6 pt-2" />
        <UserProfile />
      </div>
      <BreadCrumbs v-if="appStore.isInFairView" class="mb-4" />
    </template>

    <!-- 👉 Pages -->
    <RouterView v-slot="{ Component }">
      <Transition :name="appRouteTransition" :duration="300" mode="out-in">
        <Component
          :is="Component"
          :class="appStore.isInFairView ? 'mt-4' : ''"
        />
      </Transition>
    </RouterView>

    <!-- 👉 Footer -->
    <template #footer />

    <!-- 👉 Customizer -->
    <!--    <TheCustomizer />-->
  </VerticalNavLayout>
</template>
