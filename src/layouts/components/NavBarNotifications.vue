<script lang="ts" setup>
  import Notifications from '@core/components/Notifications.vue'
  import type { Notification } from '@layouts/types'

  const notifications: Notification[] = [
    {
      text: 'Bridge',
      message: 'Bridge',
      title: 'Congratulation Flora! 🎉',
      subtitle: 'Won the monthly best seller badge',
      time: 'Today',
    },
  ]
</script>

<template>
  <Notifications :notifications="notifications" />
</template>
