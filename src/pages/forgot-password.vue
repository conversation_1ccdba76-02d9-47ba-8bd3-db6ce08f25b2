<script setup lang="ts">
  import { useAuth } from '@/composables/Auth/useAuth'
  import {
    passwordResetEmailSent,
    passwordResetSuccess,
  } from '@/composables/useSweetAlert'
  import { emailValidator, requiredValidator } from '@validators'
  import { useRoute } from 'vue-router'
  import logo from '@images/bridge-logo.png'
  import { useAuthStore } from '@/stores/authStore'
  import router from '@/router'

  /**
   * Form Data - credentials
   */
  const credentials = ref({
    email: '',
  })

  const formRef = ref()

  const route = useRoute()

  let loading = ref(false)

  const authStore = useAuthStore()

  const {
    state: { resetPasswordLoading },
    actions: { handleResetPassword },
  } = useAuth()

  const resetPassword = async () => {
    const valid = (await formRef.value.validate()).valid
    if (valid) {
      loading.value = true
      await handleResetPassword(credentials.value.email)
      loading.value = false
      await passwordResetEmailSent(true)
      await router.push({ name: 'login' })
    }
  }
</script>

<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <div class="position-relative my-sm-16">
      <VCard class="auth-card pa-4" max-width="460">
        <VCardItem class="justify-center">
          <VImg :src="logo" width="120" />
        </VCardItem>
        <VCardText class="pt-2">
          <p class="custom-card-title mb-2">Reset Password</p>
        </VCardText>

        <VCardText>
          <VForm ref="formRef" @submit.prevent="resetPassword">
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="credentials.email"
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VBtn
                  block
                  type="submit"
                  :loading="resetPasswordLoading"
                  :disabled="resetPasswordLoading"
                >
                  Passwort zurücksetzen
                </VBtn>
              </VCol>

              <VCol cols="6" class="text-center text-base">
                <br />
                <RouterLink class="text-primary ms-2" :to="{ name: 'login' }">
                  Login
                </RouterLink>
              </VCol>
              <VCol cols="6" class="text-center text-base">
                <br />
                <RouterLink
                  class="text-primary ms-2"
                  :to="{ name: 'company-register' }"
                >
                  Register
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  action: read
  redirectIfLoggedIn: true
</route>
