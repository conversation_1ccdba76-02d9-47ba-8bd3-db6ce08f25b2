<script setup lang="ts">
  import { useDynamicLinks } from '@/composables/useDynamicLinks'
  import { downloadCompanyPdf } from '@/libs/pdf-creation'

  import { useCompanyStore } from '@/stores/companyStore'

  const {
    actions: { getDynamicLinkForActiveCompany },
  } = useDynamicLinks()

  const companyStore = useCompanyStore()

  const dynamicCompanyLink = computed(() => companyStore.company?.dynamicLink)
  const companyPdfText = ref('')
  const copyButtonIcon = ref('mdi-content-copy')
  const dynamicLinkLoading = ref(false)

  /**
   * Calls generateCompanyPdf which creates a pdf file on the fly, which
   * can than be downloaded by the user
   */
  const handlePdfClick = async () => {
    if (companyStore.company)
      await downloadCompanyPdf(companyStore.company?.name, companyPdfText.value)
  }

  /**
   * Calls getDynamicLinkForActiveCompany which generates dynamic link
   * for company and saves it in DB
   */
  const handleGenerateDynamicLink = async () => {
    dynamicLinkLoading.value = true

    const startTime = Date.now()

    try {
      await getDynamicLinkForActiveCompany()
    } catch (e) {
      dynamicLinkLoading.value = false
      console.error(e)
    }

    const endTime = Date.now()
    const timeDiff = endTime - startTime
    if (timeDiff < 1000)
      await new Promise(resolve => setTimeout(resolve, 1000 - timeDiff))

    dynamicLinkLoading.value = false
  }

  /**
   * Copies the dynamic link from the text field to clipboard
   */
  const copyToClipboard = async () => {
    const copyText = document.getElementById(
      'dynamicLinkInput',
    ) as HTMLInputElement
    if (copyText !== null) {
      copyText.select()

      copyText.setSelectionRange(0, 99999) // For mobile devices
      await navigator.clipboard.writeText(copyText.value)

      const olpCopyButtonIcon = copyButtonIcon.value

      copyButtonIcon.value = 'mdi-checkbox-marked-circle-outline'
      setTimeout(() => {
        copyButtonIcon.value = olpCopyButtonIcon
      }, 700)
    }
  }
</script>

<template>
  <VRow>
    <VCol cols="6">
      <VCard>
        <VCardItem>
          <VCardTitle>PDF-Aushang erstellen</VCardTitle>
          <VCardSubtitle
            >Machen Sie mit einem Aushang auf Ihre Ausbildungsangebote
            aufmerksam!</VCardSubtitle
          >
        </VCardItem>
        <VCardText>
          <p>
            Hier können Sie einen Aushang (als PDF-Datei) erzeugen lassen, den
            Sie im Anschluss herunterladen und ausdrucken können, um
            Ausbildungssuchende auf die Ausbildungsstellen Ihres Unternehmens
            aufmerksam zu machen.
          </p>
          <p>
            Der Aushang enthält neben Ihrem Unternehmensnamen und der
            Unternehmensbeschreibung auch einen QR-Code. Wird dieser QR-Code mit
            einem Smartphone gescannt, öffnet sich automatisch die Bridge-App
            und alle Ausbildungsstellen Ihres Unternehmens werden dem Benutzer
            ausgespielt. Dies funktioniert auch dann, wenn die App noch nicht
            auf dem Smartphone installiert ist – in diesem Fall wird zunächst
            der AppStore geöffnet und der Benutzer dazu aufgefordert die
            Bridge-App zu installieren. Im Anschluss öffnet sich automatisch die
            Bridge-App und es werden ebenfalls alle Ausbildungsstellen Ihres
            Unternehmens ausgespielt.
          </p>
          <p>
            Solche Aushänge können Sie auch für individuelle Ausbildungsstellen
            erzeugen. Klicken Sie dafür auf das Zahnrad-Symbol einer
            Ausbildungsstelle und wählen dann "PDF erstellen" aus.
          </p>
          <VForm>
            <VTextarea
              v-model="companyPdfText"
              placeholder="Dieser Text wird auf dem generierten Aushang zu lesen sein."
              label="Kurze Unternehmensbeschreibung"
            />
            <VBtn class="mt-3" type="submit" @click.prevent="handlePdfClick">
              PDF erstellen
            </VBtn>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
    <VCol cols="6">
      <VCard>
        <VCardItem>
          <VCardTitle>Link zum Unternehmen kopieren</VCardTitle>
          <VCardSubtitle
            >Verlinken Sie Ihr Unternehmen im Internet überall, wo Sie
            möchten</VCardSubtitle
          >
        </VCardItem>
        <VCardText>
          <p>
            Möchten Sie Ihr Unternehmen lediglich verlinken (z.B. auf anderen
            Plattformen), können Sie sich den Link hier kopieren. Wird dieser
            Link auf einem Smartphone angeklickt, öffnet sich die Bridge-App und
            es werden alle Stellenanzeigen von Ihrem Unternehmen geladen. Dies
            funktioniert auch, wenn die App noch nicht installiert ist.
          </p>
          <VTextField
            v-if="dynamicCompanyLink"
            id="dynamicLinkInput"
            :model-value="dynamicCompanyLink"
          >
            <template #append-inner>
              <VIcon :icon="copyButtonIcon" @click="copyToClipboard" />
            </template>
          </VTextField>
          <VBtn
            v-else-if="!dynamicLinkLoading"
            @click="handleGenerateDynamicLink"
          >
            Link generieren
          </VBtn>

          <VProgressCircular v-else indeterminate size="38" />
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
