<script setup>
  import {
    confirmedValidator,
    emailValidator,
    passwordValidator,
    requiredValidator,
  } from '@validators'

  import logo from '@images/bridge-logo.png'
  import { useAuth } from '@/composables/Auth/useAuth'
  import { useAuthStore } from '@/stores/authStore'

  const {
    actions: { handleRegisterUser },
  } = useAuth()

  const authStoreData = useAuthStore()

  const credentials = authStoreData.$state.credentials

  /**
   * Reference to register form
   */
  const formRef = ref()

  const isPasswordVisible = ref(false)

  const loading = ref(false)

  /**
   * Store
   */

  /**
   * Calls registerUser from authStore to register new User
   */
  const register = async () => {
    const valid = (await formRef.value.validate()).valid
    if (valid) {
      loading.value = true
      await handleRegisterUser()
      loading.value = false
    }
  }
</script>

<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <div class="position-relative">
      <!-- 👉 Auth card -->
      <VCard class="auth-card pa-4" max-width="460">
        <VCardItem class="justify-center">
          <VImg :src="logo" width="120" />
        </VCardItem>

        <VCardText class="pt-2">
          <p class="mb-2 custom-card-title">Registrieren</p>
          <p class="mb-0">
            Jetzt kostenlos anmelden und mit Bridge AzuBis, sowie Praktikanten
            finden.
          </p>
        </VCardText>

        <VCardText>
          <VForm ref="formRef" @submit.prevent="register">
            <VRow>
              <!-- Username -->
              <VCol cols="12">
                <VTextField
                  v-model="credentials.adminName"
                  label="Vor- und Nachname"
                  :rules="[requiredValidator]"
                  :disabled="loading"
                />
              </VCol>
              <!-- email -->
              <VCol cols="12">
                <VTextField
                  v-model="credentials.email"
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                  :disabled="loading"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="credentials.password"
                  label="Passwort"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'
                  "
                  :rules="[passwordValidator]"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                  :disabled="loading"
                />
              </VCol>

              <!-- password -->
              <VCol cols="12">
                <VTextField
                  v-model="credentials.confirmedPassword"
                  label="Passwort wiederholen"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :rules="[
                    confirmedValidator(
                      credentials.confirmedPassword,
                      credentials.password,
                    ),
                  ]"
                  :disabled="loading"
                />

                <VCheckbox
                  id="privacy-policy"
                  v-model="credentials.confirmedPolicies"
                  :rules="[requiredValidator]"
                  class="mt-2 mb-4"
                >
                  <template #label>
                    <div>
                      Ich stimme den
                      <a href="https://bridge-app.de/agb" class="text-primary"
                        >AGB</a
                      >
                      und dem
                      <a
                        href="https://bridge-app.de/datenschutz"
                        class="text-primary"
                        >Datenschutz</a
                      >
                      zu.
                    </div>
                  </template>
                </VCheckbox>

                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                  :disabled="loading"
                >
                  Registrieren
                </VBtn>
              </VCol>

              <!-- login instead -->
              <VCol cols="12" class="text-center text-base">
                <span>Bereits angemeldet?</span>
                <RouterLink class="text-primary ms-2" :to="{ name: 'login' }">
                  Zum Login
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  action: read
  redirectIfLoggedIn: true
</route>
