<script setup lang="ts">
  import NotAuthorized from '@/components/NotAuthorized.vue'
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import { computed, onMounted } from 'vue'
  import { useAuthStore } from '@/stores/authStore'
  import { useSubscriptionGuard } from '@/composables/Subscription/useSubscriptionGuard'
  import { useCompanyStore } from '@/stores/companyStore'

  const authStore = useAuthStore()
  const companyStore = useCompanyStore()
  const {
    state: { permissions, permissionsReady, isLoading },
  } = usePermissions()
  
  const {
    actions: { checkSubscriptionForJobAdvert },
  } = useSubscriptionGuard()

  const emailVerified = computed(() => authStore.claims.email_verified)

  const showForm = ref(true)
  const canCreateJobAd = ref(false)

  const toggleForm = () => {
    showForm.value = !showForm.value
  }
  
  onMounted(async () => {
    // Wait for company data to load
    if (!companyStore.getCompany) {
      await companyStore.init()
    }
    
    // Check if user can create job ad based on subscription
    canCreateJobAd.value = await checkSubscriptionForJobAdvert()
  })
</script>

<template>
  <VCard v-if="isLoading">
    <v-skeleton-loader
      class="mx-auto"
      elevation="12"
      max-height="800px"
      type="table-heading, list-item-two-line, image"
    ></v-skeleton-loader>
  </VCard>
  <VCard v-else>
    <VCard v-if="permissions.canCreateJobAd || permissions.isSuperAdmin">
      <template v-if="emailVerified">
        <VCardText v-if="showForm">
          <JobAdForm :isNew="true" @toggleForm="toggleForm" />
        </VCardText>
        <VCardText v-else>
          <JobAdPreview @toggleForm="toggleForm" />
        </VCardText>
      </template>
      <VerifyEmail v-else />
    </VCard>
    <VCard v-else class="pa-4">
      <NotAuthorized />
    </VCard>
  </VCard>
</template>

<route lang="yaml">
meta:
  action: edit
  subject: JobAds
</route>
