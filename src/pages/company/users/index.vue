<script setup lang="ts">
  import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
  import { UserTableHeader } from '@/composables/CompanyUsers/userTableHeader'
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import { useAppStore } from '@/stores/appStore'
  import ImageCropperResult from '@/types/image-cropper-result'
  import { paginationMeta } from '@/utils/utils'
  import Swal from 'sweetalert2'
  import { useTheme } from 'vuetify'
  import { useUsersStore } from '@/stores/usersStore'
  import avatar from '@images/avatars/avatar-0.png'

  interface User {
    id: string | null
    name: string
    avatarImage: string
    avatarFile: File | null
    email: string
    imageResult: ImageCropperResult
    rights: {
      superAdmin: boolean
      createJobAd: boolean
      editCompany: boolean
      createUser: boolean
    }
  }

  const usersStore = useUsersStore()
  const appStore = useAppStore()

  const appLoader = computed(() => appStore.showLoader)

  const {
    state: { companyUsers, loadingCompanyUsers },
    actions: {
      createNewCompanyUser,
      handleUpdateCompanyUser,
      loadCompanyUsers,
    },
  } = useCompanyUser()

  onMounted(async () => {
    await loadCompanyUsers()
  })

  const {
    state: { permissions },
  } = usePermissions()

  const colors = useTheme().current.value.colors
  const isDarkLayout = useTheme().global.current
  const searchQuery = ref('')

  const itemsPerPage = ref(10)
  const page = ref(1)

  const paginatedCompanyUsers = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return companyUsers.value.slice(start, end)
  })

  const totalUsers: Ref<number> = computed(() =>
    companyUsers.value ? (companyUsers.value as any[]).length : 0,
  )

  !loadingCompanyUsers &&
    console.log({ companyUsersList: companyUsers.value.rights })

  const getUserRightsStrings = (rights: {
    superAdmin: any
    createJobAd: any
    editCompany: any
    createUser: any
  }) => {
    const strings = []

    if (rights?.superAdmin) strings.push('Administrator')

    if (rights?.createJobAd) strings.push('Anzeigen erstellen')

    if (rights?.editCompany) strings.push('Unternehmen bearbeiten')

    if (rights?.createUser) strings.push('Benutzer verwalten')

    return strings || []
  }

  const loading = ref(false)

  const handleRowClicked = (user: any) => {
    if (permissions.value.isSuperAdmin || permissions.value.canCreateUser) {
      selectedUser.value = user
      isUserDrawerOpen.value = true
      usersStore.updateUserForm(user)
    }
  }

  const openNewUser = () => {
    if (permissions.value.isSuperAdmin || permissions.value.canCreateUser) {
      selectedUser.value = blankUser
      usersStore.updateUserForm(blankUser)
      isUserDrawerOpen.value = true
    }
  }
  const handleUserAddEdit = async (user: User) => {
    loading.value = true
    if (usersStore.userForm.id != null) await handleUpdateCompanyUser(user)
    else await createNewCompanyUser(user)
    loading.value = false
  }

  const handleDeleteUser = async (uid: string) => {
    const result = await Swal.fire({
      titleText: 'Sind Sie sicher?',
      text: 'Diese Aktion kann nicht rückgängig gemacht werden. Nach dem Löschen können Sie allerdings einen neuen Benutzer mit derselben EMail Adresse anlegen.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Benutzer löschen',
      confirmButtonColor: colors.success,
      cancelButtonText: 'Abbrechen',
      cancelButtonColor: colors.primary,
      customClass: isDarkLayout.value.dark ? 'dark' : 'light',
    })

    if (result.isConfirmed) {
      appStore.showAppLoader()

      const success = await usersStore.deleteUser(uid)

      appStore.hideAppLoader()
      if (!success) {
        await Swal.fire({
          titleText: 'Das hat nicht geklappt',
          text: 'Um einen Benutzer löschen zu können, muss dieser zuerst aus allen Stellenanzeigen entfernt werden. Der Benutzer darf außerdem auch kein Admin sein. Probieren Sie es danach bitte erneut.',
          icon: 'warning',
          confirmButtonText: 'OK',
          confirmButtonColor: colors.success,
          customClass: isDarkLayout.value.dark ? 'dark' : 'light',
        })
      }
    }
  }

  const clearUser = () => {
    selectedUser.value = blankUser
  }

  const blankUser = {
    id: null,
    avatarImage: null,
    avatarFile: null,
    name: '',
    email: '',
    rights: {
      createJobAd: false,
      createUser: false,
      editCompany: false,
      superAdmin: false,
    },
  }

  const isUserDrawerOpen = ref(false)
  const selectedUser = ref(blankUser)
</script>

<template>
  <section>
    <v-skeleton-loader
      v-if="loadingCompanyUsers"
      class="mx-auto border py-6"
      type="table"
    ></v-skeleton-loader>
    <VCard
      v-else
      title="Benutzerverwaltung"
      subtitle="Hier können Sie bestehende Benutzer bearbeiten und neue anlegen"
      :disabled="loadingCompanyUsers || appLoader"
    >
      <template #append>
        <VBtn
          v-if="permissions.isSuperAdmin || permissions.canCreateUser"
          variant="elevated"
          color="primary"
          @click="openNewUser"
        >
          Benutzer hinzufügen
        </VBtn>
      </template>
      <VCardText>
        <div class="d-flex align-end flex-wrap gap-3">
          <AppTextField
            v-model="searchQuery"
            placeholder="Firmenbenutzer suchen"
            density="compact"
            prepend-inner-icon="mdi-magnify"
            class="me-3"
          />
        </div>
      </VCardText>

      <VDivider />
      <VDataTable
        :items="paginatedCompanyUsers"
        :headers="UserTableHeader"
        :search="searchQuery"
        :loading="loadingCompanyUsers || appLoader"
        class="px-2"
        hover
        @click:row="(event, value) => handleRowClicked(value.item)"
      >
        <template #[`item.avatarImageUrl`]="{ item }">
          <VAvatar
            :image="item.avatarImageUrl || avatar"
            size="55"
            class="my-3"
          />
        </template>

        <template #[`item.rights`]="{ item }">
          <VChip
            v-for="string in getUserRightsStrings(item.rights[0])"
            :key="string"
            :text="string"
            color="info"
            class="mr-1"
            label
          />
        </template>

        <template #bottom>
          <VDivider />
          <div
            class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
          >
            <p class="text-sm text-disabled mb-0">
              {{ paginationMeta({ page, itemsPerPage }, totalUsers as number) }}
            </p>

            <VPagination
              v-model="page"
              :length="Math.ceil(totalUsers / itemsPerPage)"
              :total-visible="
                $vuetify.display.xs ? 1 : Math.ceil(totalUsers / itemsPerPage)
              "
            >
              <template #prev="slotProps">
                <VBtn
                  variant="tonal"
                  color="default"
                  v-bind="slotProps"
                  :icon="false"
                >
                  Zurück
                </VBtn>
              </template>

              <template #next="slotProps">
                <VBtn
                  variant="tonal"
                  color="default"
                  v-bind="slotProps"
                  :icon="false"
                >
                  Next
                </VBtn>
              </template>
            </VPagination>
          </div>
        </template>
      </VDataTable>
    </VCard>
    <UserDrawer
      v-model:isDrawerOpen="isUserDrawerOpen"
      :user="selectedUser"
      @clearUser="clearUser"
      @userData="handleUserAddEdit"
      @deleteUser="handleDeleteUser"
      :loading="loading || appLoader"
    />
  </section>
</template>

<route lang="yaml">
meta:
  action: read
  subject: auth
</route>
