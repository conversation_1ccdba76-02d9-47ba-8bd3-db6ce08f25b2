<script setup lang="ts">
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'

  const {
    state: { loadingStatistics, statistics },
    actions: { initiateJobAdsListen, stopJobAdsListen, loadJobAdsByCompany },
  } = useJobAdverts()

  const useSubStore = useSubscriptionStore()

  onMounted(async () => {
    await loadJobAdsByCompany()
    await initiateJobAdsListen()
    await useSubStore.fetchSubscriptionConfig()
  })

  onUnmounted(() => {
    stopJobAdsListen()
  })
</script>
<template>
  <VRow>
    <VCol cols="12">
      <v-skeleton-loader
        v-if="loadingStatistics"
        class="mx-auto border py-6"
        type="list-item-three-line"
      ></v-skeleton-loader>
      <JobStatistics v-else :data="statistics" :loading="loadingStatistics" />
    </VCol>
    <VCol cols="12">
      <JobAdvertTable />
    </VCol>
  </VRow>
</template>

<route lang="yaml">
# @formatter:off
meta:
  action: read
  subject: auth
</route>
