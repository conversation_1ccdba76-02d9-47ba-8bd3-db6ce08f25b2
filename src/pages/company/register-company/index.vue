<script setup lang="ts">
  import { useCompanyStore } from '@/stores/companyStore'

  const companyStore = useCompanyStore()
</script>

<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <VCard title="Unternehmen Registrieren" width="1000px">
      <VCardText>
        <CompanyForm is-initial-setup />
      </VCardText>
    </VCard>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  middleware: auth
  subject: auth
  redirectIfLoggedIn: true
</route>
