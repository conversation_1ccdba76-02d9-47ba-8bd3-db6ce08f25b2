<script setup lang="ts">
  import { useJobLikes } from '@/composables/JobAdverts/useJobLikes'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'

  const {
    state: { advertStatistics, jobAdvertTitle, loadingJobAdvert },
  } = useJobLikes()

  const {
    state: { jobAdText },
  } = useJobAdverts()
</script>

<template>
  <VRow>
    <VCol cols="12">
      <v-skeleton-loader
        v-if="loadingJobAdvert"
        class="mx-auto border py-6"
        type="list-item-three-line"
      ></v-skeleton-loader>

      <JobStatistics
        v-else
        :title="`${jobAdText.statisticsOn} : ${jobAdvertTitle}`"
        :data="advertStatistics"
        :loading="loadingJobAdvert"
      />
    </VCol>
    <VCol cols="12">
      <JobLikesTable />
    </VCol>
  </VRow>
</template>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
