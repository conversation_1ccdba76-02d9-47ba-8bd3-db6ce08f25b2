<script setup lang="ts">
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
  import { useReward } from 'vue-rewards'
  import { onMounted } from 'vue'

  import spaceRocket from '@images/misc/3d-space-rocket-with-smoke.png'
  import miscMaskDark from '@images/pages/misc-mask-dark.png'
  import miscMaskLight from '@images/pages/misc-mask-light.png'

  const route = useRoute()

  const subStore = useSubscriptionStore()

  const isSuccessful = route.query.success

  const authThemeMask = useGenerateImageVariant(miscMaskLight, miscMaskDark)

  const config = {
    startVelocity: 10,
    spread: 180,
    elementCount: 100,
  }

  onMounted(() => {
    const { reward, isAnimating } = useReward(
      'confettiBomb',
      'confetti',
      config,
    )

    reward()

    subStore.resetStore()
  })
</script>

<template>
  <div class="misc-wrapper" v-if="isSuccessful">
    <!-- 👉 Image -->
    <div class="misc-avatar w-100 text-center" id="confettiBomb">
      <VImg :src="spaceRocket" alt="Success" :max-width="200" class="mx-auto" />
    </div>

    <VBtn to="/" class="mb-12"> Zu den Stellenanzeigen</VBtn>

    <VImg :src="authThemeMask" class="misc-footer-img d-none d-md-block" />
  </div>

  <div class="misc-wrapper" v-else>
    <!-- 👉 Image -->
    <div class="misc-avatar w-100 text-center">
      <v-icon
        icon="tabler-alert-triangle"
        size="120"
        class="error"
        color="error"
      ></v-icon>
    </div>
    <ErrorHeader
      error-title="Subscription Gekündigt"
      error-description="Subscription to premium was cancelled. Your job advert is still on Basic Plan."
    />
    <VBtn to="/" class="mb-12">Zu den Stellenanzeigen</VBtn>

    <VImg :src="authThemeMask" class="misc-footer-img d-none d-md-block" />
  </div>
</template>

<style lang="scss">
  @use '@core/scss/template/pages/misc';
</style>

<route lang="yaml">
meta:
  layout: blank
  public: false
  name: subscription-landing
</route>
