<script setup lang="ts">
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import router from '@/router'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import spaceRocket from '@images/misc/3d-space-rocket-with-smoke.png'
  import dollarCoinPiggyBank from '@images/misc/dollar-coins-flying-pink-piggy-bank.png'

  interface Pricing {
    title?: string
    xs?: number | string
    sm?: number | string
    md?: string | number
    lg?: string | number
    xl?: string | number
  }

  const route = useRoute()

  const {
    state: { loadingCheckoutSession },
  } = useStripeSubscription()

  const subStore = useSubscriptionStore()

  const props = defineProps<Pricing>()

  const annualMonthlyPlanPriceToggler = ref(true)

  const jobAdTitle = route.query.tit
  const jobAdId = route.query.ad

  const jobTitles = computed(() => {
    if (subStore.getJobAdverts) {
      return subStore.getJobAdverts.map(jobAdvert => {
        const parts = jobAdvert.split('|')
        return parts[1]
      })
    } else {
      return []
    }
  })

  const upgradeToPremium = async () => {
    router.push({
      path: '/company/subscription/checkout',
      query: { ad: jobAdId, tit: jobAdTitle },
    })
  }

  const pricingPlans = [
    {
      name: 'Basic',
      tagLine: 'A simple start for everyone',
      logo: dollarCoinPiggyBank,
      monthlyPrice: 0,
      yearlyPrice: 0,
      isPopular: false,
      current: true,
      action: 'Your Current Plan',
      features: [
        'Personal Job Ad link',
        '1 match per month',
        'Restricted access to applicant profile',
        '24/7 Support',
      ],
    },
    {
      name: 'Premium',
      tagLine: 'For small to medium businesses',
      logo: spaceRocket,
      monthlyPrice: 29,
      yearlyPrice: 87,
      isPopular: true,
      current: false,
      action: upgradeToPremium,
      features: [
        'Unlimited matches',
        'Unlimited access to applicant profile',
        'Personal Job Ad link',
        '24/7 Support',
      ],
    },
    // {
    //   name: 'Enterprise',
    //   tagLine: 'Solution for big organizations',
    //   logo: spaceRocket,
    //   monthlyPrice: 99,
    //   yearlyPrice: 999,
    //   isPopular: false,
    //   current: false,
    //   features: [
    //     'PayPal payments',
    //     'Logic Jumps',
    //     'File upload with 5GB storage',
    //     'Custom domain support',
    //     'Stripe integration',
    //   ],
    // },
  ]
</script>

<template>
  <v-container>
    <div flat>
      <v-chip
        @click="() => $router.go(-1)"
        prepend-icon="mdi-arrow-left"
        color="primary"
        label
      >
        Zurück
      </v-chip>
    </div>
    <!-- 👉 Title and subtitle -->
    <div class="text-center mb-12">
      <h2 class="text-h2 pricing-title mb-8">Pricing Plans</h2>
      <p class="mb-0">
        Subscribe to the premium plan for <b>{{ jobAdTitle }}</b>
      </p>
      <div v-if="jobTitles.length">
        <v-chip v-for="jobTitle in jobTitles" class="ma-1">
          {{ jobTitle }}
        </v-chip>
      </div>
      <p class="mb-2">
        This plan renews every 3 months and can be cancelled at any time.
      </p>
    </div>

    <VRow>
      <VCol
        v-for="plan in pricingPlans"
        :key="plan.logo"
        v-bind="props"
        cols="6"
      >
        <!-- 👉  Card -->
        <VCard
          flat
          border
          :class="plan.isPopular ? 'border-primary border-opacity-100' : ''"
        >
          <VCardText style="block-size: 4.125rem" class="text-end">
            <!-- 👉 Popular -->
            <VChip v-show="plan.isPopular" label color="primary" size="small">
              Recommended
            </VChip>
          </VCardText>

          <!-- 👉 Plan logo -->
          <VCardText>
            <VImg :height="140" :src="plan.logo" class="mx-auto mb-5" />

            <!-- 👉 Plan name -->
            <h3 class="text-h3 mb-1 text-center">
              {{ plan.name }}
            </h3>

            <p class="mb-0 text-center">
              {{ plan.tagLine }}
            </p>

            <!-- 👉 Plan price  -->

            <div class="position-relative">
              <div class="d-flex justify-center align-center py-8">
                <sup class="text-sm text-primary me-1">€</sup>
                <h1 class="text-5xl font-weight-medium text-primary">
                  {{
                    annualMonthlyPlanPriceToggler
                      ? Math.floor(Number(plan.yearlyPrice) / 3)
                      : plan.monthlyPrice
                  }}
                </h1>
                <sub class="text-sm text-disabled ms-1">/month</sub>
              </div>

              <!-- 👉 Annual Price -->
              <span
                v-show="annualMonthlyPlanPriceToggler"
                class="annual-price-text position-absolute text-sm text-disabled"
              >
                {{
                  plan.yearlyPrice === 0
                    ? 'free'
                    : `EUR ${plan.yearlyPrice} / 3 months`
                }}
              </span>
            </div>

            <!-- 👉 Plan features -->

            <VList class="card-list mb-4">
              <VListItem v-for="feature in plan.features" :key="feature">
                <template #prepend>
                  <VIcon :size="14" icon="tabler-circle" />
                </template>

                <VListItemTitle>
                  {{ feature }}
                </VListItemTitle>
              </VListItem>
            </VList>

            <!-- 👉 Plan actions -->
            <VBtn
              :loading="plan.yearlyPrice > 0 ? loadingCheckoutSession : false"
              :disabled="plan.yearlyPrice > 0 ? loadingCheckoutSession : false"
              block
              :color="plan.current ? 'success' : 'primary'"
              :variant="plan.isPopular ? 'elevated' : 'tonal'"
              @click="plan.action"
            >
              {{ plan.yearlyPrice === 0 ? 'Your Current Plan' : 'Upgrade' }}
            </VBtn>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </v-container>

  <!-- SECTION pricing plans -->

  <!-- !SECTION  -->
</template>

<style lang="scss" scoped>
  .card-list {
    --v-card-list-gap: 0.75rem;
  }

  .save-upto-chip {
    inset-block-start: -2rem;
    inset-inline-end: -7rem;
  }

  .annual-price-text {
    inset-block-end: 10%;
    inset-inline-start: 50%;
    transform: translateX(-50%);
  }
</style>

<route lang="yaml">
meta:
  layout: blank
  public: false
</route>
