<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import CompanySubscriptionPlans from '@/components/Subscription/CompanySubscriptionPlans.vue'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { useAppStore } from '@/stores/appStore'
  
  const router = useRouter()
  const appStore = useAppStore()
  const subscriptionStore = useSubscriptionStore()
  
  const isAllowed = ref(true)
  const loading = ref(true)
  
  onMounted(async () => {
    try {
      loading.value = true
      await subscriptionStore.fetchSubscriptionConfig()
      
      // Check if company has COMPANY_UNLIMITED subscription type
      if (!subscriptionStore.isCompanyUnlimited) {
        isAllowed.value = false
        appStore.showSnack('Pricing plans are only available for unlimited subscription plans', 'warning')
        // Redirect to subscription overview
        router.push({ name: 'company-subscription' })
      }
    } catch (error) {
      console.error('Error checking subscription type:', error)
      // Allow access if we can't determine the subscription type
      isAllowed.value = true
    } finally {
      loading.value = false
    }
  })
</script>

<template>
  <div>
    <div v-if="loading" class="text-center py-12">
      <VProgressCircular indeterminate color="primary" :size="60" />
      <p class="mt-4">Loading...</p>
    </div>
    <CompanySubscriptionPlans v-else-if="isAllowed" />
    <VCard v-else class="pa-8 text-center">
      <VIcon size="64" color="warning" class="mb-4">mdi-alert-circle-outline</VIcon>
      <h3 class="text-h5 mb-2">Access Restricted</h3>
      <p class="text-body-1">Pricing plans are only available for unlimited subscription plans.</p>
      <VBtn class="mt-4" color="primary" @click="router.push({ name: 'company-subscription' })">
        Go to Subscription Overview
      </VBtn>
    </VCard>
  </div>
</template>

<route lang="yaml">
meta:
  layout: default
  requiresAuth: true
  public: false
</route>
