<script setup lang="ts">
  import { useUnlimitedSubscriptionStatus } from '@/composables/StripeSubscription/useUnlimitedSubscriptionStatus'
  import UnlimitedSubscriptionTable from '@/components/Subscription/UnlimitedSubscriptionTable.vue'
  import SubscriptionTable from '@/components/Subscription/SubscriptionTable.vue'
  import PaymentMethods from '@/components/Subscription/PaymentMethods.vue'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'

  const { hasActiveUnlimitedSubscription } = useUnlimitedSubscriptionStatus()
  const subscriptionStore = useSubscriptionStore()
</script>

<template>
  <VRow>
    <VCol cols="12">
      <!-- Show notification banner if user has active unlimited subscription -->
      <VAlert
        v-if="hasActiveUnlimitedSubscription"
        type="info"
        variant="tonal"
        class="mb-4"
      >
        <template #prepend>
          <VIcon icon="mdi-infinity" />
        </template>
        <VAlertTitle>Unlimited Subscription Aktiv</VAlertTitle>
        Sie haben ein aktives Unlimited-Abonnement mit unbegrenzten
        Stellenanzeigen.
      </VAlert>
    </VCol>

    <VCol cols="12" md="8">
      <UnlimitedSubscriptionTable v-if="subscriptionStore.isCompanyUnlimited" />
      <SubscriptionTable v-else />
    </VCol>

    <VCol cols="12" md="4">
      <PaymentMethods />
    </VCol>
  </VRow>
</template>

<route lang="yaml">
meta:
  action: read
  subject: auth
</route>
