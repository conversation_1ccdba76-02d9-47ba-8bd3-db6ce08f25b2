<script setup>
  import { ref, onMounted } from 'vue'
  import { loadStripe } from '@stripe/stripe-js'

  import SrMessages from './SrMessages.vue'

  const isLoading = ref(false)
  const messages = ref([])

  let stripe
  let elements

  onMounted(async () => {
    const publishableKey =
      'pk_test_51LBzzjG9yclWrwpcf4vE6g7bESEms6JHNTHkVk0LB9vbjLSAQbmI3vn40ViRl9hgsQoNpMUB9vgtvBdY6fF2dGBs00qt5Rv7Ib'
    stripe = await loadStripe(publishableKey)

    messages.value.push(`Client secret returned.`)
    const options = { mode: 'billing' }
    elements = stripe.elements({ mode: 'setup', currency: 'eur' })
    const cardElement = elements.create('card')
    cardElement.mount('#card-element')
    const linkAuthenticationElement = elements.create('linkAuthentication')
    const addressElement = elements.create('address', options)
    linkAuthenticationElement.mount('#link-authentication-element')
    addressElement.mount('#address-element')
    isLoading.value = false
  })

  const handleSubmit = async () => {
    if (isLoading.value) {
      return
    }

    isLoading.value = true

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/return`,
      },
    })

    if (error.type === 'card_error' || error.type === 'validation_error') {
      messages.value.push(error.message)
    } else {
      messages.value.push('An unexpected error occured.')
    }

    isLoading.value = false
  }
</script>
<template>
  <main>
    <h1>Payment</h1>
    <container>
      <v-row>
        <v-col>
          <v-card>
            <form id="payment-form" @submit.prevent="handleSubmit">
              <!--              <div id="payment-element" />-->
              <v-card-text>
                <div id="link-authentication-element" />
                <div id="card-element" />
                <div id="address-element" />
              </v-card-text>
            </form>
            <v-card-actions>
              <v-btn id="submit" :disabled="isLoading">Pay now</v-btn>
              <!--            <sr-messages :messages="messages" />-->
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </container>
  </main>
</template>
