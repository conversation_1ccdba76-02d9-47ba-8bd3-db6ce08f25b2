<script setup lang="ts">
  import { useCompanySubscriptionGraph } from '@/api/graphHooks/useCompanySubscriptionGraph'
  import { useRouter, useRoute } from 'vue-router'
  import successImage from '@images/misc/3d-space-rocket-with-smoke.png'
  import { useSubscriptionGuard } from '@/composables/Subscription/useSubscriptionGuard'
  import { useCompanyStore } from '@/stores/companyStore'

  const router = useRouter()
  const route = useRoute()
  const companyStore = useCompanyStore()

  const {
    state: { subscriptionStatus },
    actions: { refetchSubscriptionStatus },
  } = useCompanySubscriptionGraph()

  const {
    actions: { handleSubscriptionReturn },
  } = useSubscriptionGuard()

  const loading = ref(true)
  const isNewCompany = computed(() => route.query.isNewCompany === 'true')
  const returnTo = computed(() => route.query.returnTo as string)

  onMounted(async () => {
    setTimeout(async () => {
      await refetchSubscriptionStatus()
      await companyStore.init()
      loading.value = false
    }, 2000)
  })

  const navigateToManagement = () => {
    router.push('/company/subscription/management')
  }

  const navigateToJobAdverts = () => {
    router.push('/company/new-job-ad')
  }

  const handleContinue = () => {
    if (returnTo.value) {
      handleSubscriptionReturn()
    } else {
      navigateToJobAdverts()
    }
  }
</script>

<template>
  <VContainer>
    <VRow justify="center" class="mt-12">
      <VCol cols="12" md="8" lg="6">
        <VCard elevation="8">
          <!-- Loading State -->
          <template v-if="loading">
            <VCardText class="text-center py-12">
              <VProgressCircular
                indeterminate
                color="primary"
                :size="60"
                class="mb-4"
              />
              <h3 class="text-h5 mb-2">Ihr Abonnement wird verarbeitet...</h3>
              <p class="text-body-2 text-medium-emphasis">
                Bitte warten Sie, während wir Ihr Abonnement aktivieren
              </p>
            </VCardText>
          </template>

          <!-- Success State -->
          <template v-else>
            <VCardText class="text-center py-8">
              <VImg :src="successImage" :height="200" class="mx-auto mb-6" />

              <VIcon
                icon="mdi-check-circle"
                color="success"
                size="64"
                class="mb-4"
              />

              <h2 class="text-h4 mb-3 text-success">Abonnement aktiviert!</h2>

              <p class="text-body-1 mb-2" v-if="isNewCompany">
                Willkommen! Ihr Unternehmen wurde erfolgreich erstellt.
              </p>

              <p class="text-body-1 mb-6">Vielen Dank für Ihr Abonnement</p>

              <!--              <VAlert type="success" variant="tonal" class="mb-6">-->
              <!--                <div class="text-left">-->
              <!--                  <p class="mb-2">-->
              <!--                    <strong>Your subscription includes:</strong>-->
              <!--                  </p>-->
              <!--                  <ul class="pl-4">-->
              <!--                    <li v-if="subscriptionStatus?.jobAdvertLimit === -1">-->
              <!--                      Unlimited premium job adverts-->
              <!--                    </li>-->
              <!--                    <li v-else>-->
              <!--                      {{ subscriptionStatus?.jobAdvertLimit }} premium job-->
              <!--                      adverts per-->
              <!--                      {{ subscriptionStatus?.billingPeriod?.toLowerCase() }}-->
              <!--                    </li>-->
              <!--                    <li>Access to all premium features</li>-->
              <!--                    <li>Priority support</li>-->
              <!--                  </ul>-->
              <!--                </div>-->
              <!--              </VAlert>-->

              <VDivider class="mb-6" />

              <div class="d-flex flex-column gap-3">
                <VBtn
                  v-if="returnTo"
                  color="primary"
                  size="large"
                  block
                  @click="handleContinue"
                >
                  <VIcon icon="mdi-arrow-right" class="mr-2" />
                  {{
                    returnTo === 'new-job-ad'
                      ? 'Weiter zur Stellenanzeige erstellen'
                      : 'Weiter'
                  }}
                </VBtn>

                <VBtn
                  v-else
                  color="primary"
                  size="large"
                  block
                  @click="navigateToJobAdverts"
                >
                  <VIcon icon="mdi-briefcase-plus" class="mr-2" />
                  Stellenanzeige erstellen
                </VBtn>

                <VBtn
                  variant="tonal"
                  size="large"
                  block
                  @click="navigateToManagement"
                >
                  <VIcon icon="mdi-cog" class="mr-2" />
                  Abonnement verwalten
                </VBtn>
              </div>
            </VCardText>
          </template>
        </VCard>

        <!-- Additional Information -->
        <VCard elevation="2" class="mt-4" v-if="!loading">
          <VCardText>
            <h4 class="text-h6 mb-3">Was kommt als Nächstes?</h4>

            <VList density="compact">
              <VListItem>
                <template #prepend>
                  <VIcon icon="mdi-email" color="primary" />
                </template>
                <VListItemTitle>Überprüfen Sie Ihre E-Mails</VListItemTitle>
                <VListItemSubtitle>
                  Wir haben Ihnen eine Bestätigungs-E-Mail mit Ihren Abonnement-Details gesendet
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon icon="mdi-receipt" color="primary" />
                </template>
                <VListItemTitle>Rechnungen einsehen</VListItemTitle>
                <VListItemSubtitle>
                  Rechnungen auf Ihrer Abonnement-Verwaltungsseite anzeigen und herunterladen
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon icon="mdi-help-circle" color="primary" />
                </template>
                <VListItemTitle>Benötigen Sie Hilfe?</VListItemTitle>
                <VListItemSubtitle>
                  Kontaktieren Sie unser Support-Team, wenn Sie Fragen haben
                </VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </VContainer>
</template>

<route lang="yaml">
meta:
  layout: default
  requiresAuth: true
  public: false
</route>
