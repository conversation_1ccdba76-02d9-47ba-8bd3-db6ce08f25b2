<script setup lang="ts"></script>

<template>
  <v-container>
    <div flat>
      <v-chip
        @click="() => $router.go(-1)"
        prepend-icon="mdi-arrow-left"
        color="primary"
        label
      >
        Zurück
      </v-chip>
      <v-chip
        @click="() => $router.push('/')"
        prepend-icon="mdi-home"
        color="primary"
        class="ml-8"
        label
      >
        Dashboard
      </v-chip>
    </div>
    <CheckoutDetails />
  </v-container>
</template>

<style lang="scss" scoped>
  .card-list {
    --v-card-list-gap: 0.75rem;
  }

  .save-upto-chip {
    inset-block-start: -2rem;
    inset-inline-end: -7rem;
  }

  .annual-price-text {
    inset-block-end: 10%;
    inset-inline-start: 50%;
    transform: translateX(-50%);
  }
</style>

<route lang="yaml">
meta:
  layout: blank
  public: false
  name: subscription-checkout
</route>
