<script setup lang="ts">
  import { useChatStore } from '@/stores/apiStores/useChatStore'
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
  import { useChatView } from '@/composables/Chat/useChatView'
  import { useAppointmentView } from '@/composables/Appointments/useAppointmentView'

  const route = useRoute()
  const router = useRouter()
  const chatStore = useChatStore()
  const appointmentId = route.params.id as string
  const applicantId = route.params.uid as string

  const {
    state: { applicant, loadingApplicant },
  } = useApplicantProfile()

  const {
    actions: { markChatMessagesAsSeen },
  } = useChatView()

  const {
    actions: { markAppointmentAsViewed },
  } = useAppointmentView()

  const chatRoomId = computed(() => chatStore.chatRoomDetails?.id).value

  onMounted(async () => {
    chatStore.init()

    // Mark chat messages as seen when the page loads
    await markChatMessagesAsSeen(applicantId, chatRoomId)

    // Mark appointment's companyIsNew as false
    await markAppointmentAsViewed(appointmentId)
  })

  // Watch for new messages and mark them as seen if user is on this page
  watch(
    () => chatStore.activeChat?.chat?.messages,
    async (newMessages, oldMessages) => {
      if (
        newMessages &&
        oldMessages &&
        newMessages.length > oldMessages.length
      ) {
        // New message received, mark it as seen
        await markChatMessagesAsSeen(applicantId, chatRoomId)
      }
    },
    { deep: true },
  )

  const showChatWindow = computed(() => {
    return chatStore.showChatWindow && chatStore.activeChat !== null
  })
</script>

<template>
  <div>
    <div v-if="loadingApplicant">
      <v-skeleton-loader
        class="mx-auto border py-6"
        type="list-item-avatar-three-line"
      ></v-skeleton-loader>
    </div>
    <!-- Only render child components when data is available -->
    <div v-else-if="applicant && Object.keys(applicant).length > 0">
      <VRow>
        <v-btn color="primary" variant="text" @click="router.go(-1)">
          <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
          Zurück
        </v-btn>
        <VCol cols="12">
          <ApplicantProfileHeader />
        </VCol>
        <VCol cols="6">
          <ApplicantProfile />
        </VCol>
        <VCol cols="6">
          <VCard class="py-1">
            <ChatWindow
              v-if="showChatWindow"
              :mini="true"
              :isAppointmentChat="true"
            />
            <VideoWindow v-else />
          </VCard>
        </VCol>
      </VRow>
    </div>
    <!-- Show error message if data couldn't be loaded -->
    <div v-else class="text-center pa-4">
      <v-alert
        type="warning"
        title="No Data Available"
        text="Could not load applicant data. Please try again later."
      ></v-alert>
    </div>
  </div>
</template>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
