<script setup lang="ts">
  import { useTheme } from 'vuetify'
  import { useFairExit } from '@/composables/useFairExit'

  useFairExit(true)

  const { colors } = useTheme().current.value
</script>

<template>
  <VRow>
    <VCol cols="12">
      <!--      <AllCompanies />-->
      Company here
    </VCol>
  </VRow>
</template>

<route lang="yml">
# @formatter:off
meta:
  super: true
  subject: auth
# @formatter:on
</route>
