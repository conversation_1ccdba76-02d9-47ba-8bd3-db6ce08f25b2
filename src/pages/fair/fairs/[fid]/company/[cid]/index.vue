<script setup lang="ts">
  import { useCompanyStore } from '@/stores/companyStore'

  const companyStore = useCompanyStore()

  const companyTableCol = computed(() => {
    if (
      companyStore.showContactPersonsList ||
      companyStore.showFairJobsList ||
      companyStore.showContactPersonTimeslots
    ) {
      return 6
    }
    return 12
  })
</script>

<template>
  <VRow>
    <v-col cols="12">
      <CompanyCategoryStrip />
      <CompanyPartnerLinks />
    </v-col>
    <VCol :cols="companyTableCol">
      <ContactPersonsTable class="mb-8" />
      <CompanyFairJobsTable />
    </VCol>
    <VCol :cols="companyTableCol">
      <ContactPersonsList v-if="companyStore.showContactPersonsList" />
      <FairJobsList v-if="companyStore.showFairJobsList" />
      <ContactPersonTimeslot v-if="companyStore.showContactPersonTimeslots" />
    </VCol>
  </VRow>
</template>

<style scoped lang="scss"></style>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
