<script setup lang="ts">
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'
  import FairCompanysTable from '@/components/Fairs/Company/FairCompanysTable.vue'
  import CompanyList from '@/components/Fairs/CompanyList.vue'
  import { useFairs } from '@/composables/Fairs/useFairs'

  const fairStore = useFairStore()
  const fairFormStore = useFairFormStore()
  const {
    state: { currentFair },
    actions: { loadCurrentFair },
  } = useFairs()

  onMounted(async () => {
    await loadCurrentFair()
  })

  watch(
    currentFair,
    newFair => {
      if (newFair && (!fairFormStore.id || fairFormStore.id !== newFair.id)) {
        fairFormStore.setFair(newFair)
        fairStore.updateFair(newFair)
      }
    },
    { immediate: true },
  )

  const companyTableCol = computed(() => {
    if (fairStore.showCompanyList) {
      return 6
    }
    return 12
  })
</script>

<template>
  <v-row>
    <v-col cols="12">
      <FairForm v-if="fairStore.showFairForm" :is-new="false" />
      <v-spacer v-if="fairStore.showFairForm" class="mt-4"></v-spacer>
      <DefaultTimeslots v-if="fairStore.showFairForm" />
      <FairBanner v-if="!fairStore.showFairForm" />
    </v-col>
    <v-col :cols="companyTableCol">
      <FairCompanysTable />
    </v-col>
    <v-col cols="6" v-if="fairStore.showCompanyList">
      <CompanyList />
    </v-col>
  </v-row>
</template>

<style scoped lang="scss"></style>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
