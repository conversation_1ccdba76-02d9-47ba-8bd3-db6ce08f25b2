<script setup lang="ts">
  import { useTheme } from 'vuetify'
  import { useFairFormStore } from '@/stores/fairs/fairFormStore'

  const { colors } = useTheme().current.value

  const fairFormStore = useFairFormStore()

  const showFairList = computed(() => {
    return fairFormStore.showFairCloneList
  })
</script>

<template>
  <VRow>
    <VCol :cols="showFairList ? 8 : 12">
      <FairForm is-new />
    </VCol>
    <VCol :cols="showFairList ? 4 : 0" v-if="showFairList">
      <FairCloneList />
    </VCol>
  </VRow>
</template>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
