<script setup lang="ts">
  import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
  import { useRoute } from 'vue-router'
  import { emailValidator, requiredValidator } from '@validators'
  import logo from '@images/bridge-logo.png'
  import { useAuthStore } from '@/stores/authStore'
  import router from '@/router'

  const credentials = ref({
    email: '',
    password: '',
  })

  const {
    actions: { isUserAvailable },
  } = useCompanyUser()

  const formRef = ref()

  const route = useRoute()

  let loading = ref(false)

  const isPasswordVisible = ref(false)

  const authStore = useAuthStore()

  const signIn = async () => {
    try {
      const valid = await formRef.value.validate()
      if (!valid) {
        throw new Error('Validation failed')
      }

      loading.value = true
      await authStore.signInUser(credentials.value)

      const userExists = await isUserAvailable(credentials.value.email)
      if (!userExists) {
        loading.value = false
        await authStore.signOut()
        throw new Error('User does not exist')
      }
      await router.push({ name: 'company' })
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }
</script>

<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <div class="position-relative my-sm-16">
      <!-- 👉 Auth card -->
      <VCard class="auth-card pa-4" max-width="460">
        <VCardItem class="justify-center">
          <VImg :src="logo" width="120" />
        </VCardItem>

        <VCardText class="pt-2">
          <p class="custom-card-title mb-2">Anmelden</p>
          <p class="mb-0">Bitte anmelden oder einen neuen Account erstellen</p>
        </VCardText>

        <VCardText>
          <VForm ref="formRef" @submit.prevent="signIn">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <VTextField
                  v-model="credentials.email"
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="credentials.password"
                  label="Passwort"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'
                  "
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
                <VCol cols="12" class="">
                  <span>Passwort vergessen?</span>
                  <RouterLink
                    class="text-primary ms-2"
                    :to="{ name: 'forgot-password' }"
                  >
                    Hier klicken
                  </RouterLink>
                </VCol>
              </VCol>

              <VCol cols="12">
                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                  :disabled="loading"
                >
                  Anmelden
                </VBtn>
              </VCol>

              <!-- login instead -->
              <VCol cols="12" class="text-center text-base">
                <span>Noch keinen Account?</span>
                <br />
                <RouterLink
                  class="text-primary ms-2"
                  :to="{ name: 'company-register' }"
                >
                  Unternehmen registrieren
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  redirectIfLoggedIn: true
</route>
