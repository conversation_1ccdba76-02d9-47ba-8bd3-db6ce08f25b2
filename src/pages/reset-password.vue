<script setup lang="ts">
  import { passwordResetSuccess } from '@/composables/useSweetAlert'
  import { useRoute } from 'vue-router'
  import logo from '@images/bridge-logo.png'
  import { useAuthStore } from '@/stores/authStore'
  import router from '@/router'

  /**
   * Form Data - credentials
   */
  const credentials = ref({
    password: '',
    confirmPassword: '',
  })

  const oobCode =
    new URL(window.location.href).searchParams.get('oobCode') || ''

  const formRef = ref()

  const route = useRoute()

  // const getOobCode = route.params.oobCode

  let loading = ref(false)
  const isPasswordVisible = ref(false)

  const authStore = useAuthStore()

  /**
   * Calls signInUser method from authStore.
   * Redirects to original requested route or to home.
   */

  const handlePasswordReset = async () => {
    try {
      const valid = (await formRef.value.validate()).valid

      if (valid) {
        loading.value = true
        await authStore.resetPassword({
          ...credentials.value,
          oobCode: oobCode,
        })
        await passwordResetSuccess(true)
        await router.push({ name: 'company' })
      }
    } catch (error) {
      console.error(error)
      loading.value = false
      // handle the error as needed
    } finally {
      loading.value = false
    }
  }
</script>

<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <div class="position-relative my-sm-16">
      <VCard class="auth-card pa-4" max-width="460">
        <VCardItem class="justify-center">
          <VImg :src="logo" width="120" />
        </VCardItem>
        <VCardText class="pt-2">
          <p class="custom-card-title mb-2">Reset Password</p>
        </VCardText>

        <VCardText>
          <VForm ref="formRef" @submit.prevent="handlePasswordReset">
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="credentials.password"
                  label="Passwort"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'
                  "
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>
              <VCol cols="12">
                <VTextField
                  v-model="credentials.confirmPassword"
                  label="Confirm Passwort"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'
                  "
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>

              <VCol cols="12">
                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                  :disabled="loading"
                >
                  Anmelden
                </VBtn>
              </VCol>

              <VCol cols="6" class="text-center text-base">
                <br />
                <RouterLink class="text-primary ms-2" :to="{ name: 'login' }">
                  Login
                </RouterLink>
              </VCol>
              <VCol cols="6" class="text-center text-base">
                <br />
                <RouterLink
                  class="text-primary ms-2"
                  :to="{ name: 'company-register' }"
                >
                  Register
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  action: read
  redirectIfLoggedIn: true
</route>
