<script setup lang="ts">
  import { useSuperUser } from '@/composables/SuperUsers/useSuperUser'
  import { useAuthStore } from '@/stores/authStore'
  import router from '@/router'
  import logo from '@images/bridge-logo.png'
  import { emailValidator, requiredValidator } from '@validators'

  const credentials = ref({
    email: '',
    password: '',
  })

  const formRef = ref()

  let loading = ref(false)

  const authStore = useAuthStore()

  const isPasswordVisible = ref(false)

  const {
    actions: { isSuperUser },
  } = useSuperUser()

  const superLogin = async () => {
    try {
      const valid = await formRef.value.validate()
      if (!valid) {
        throw new Error('Validation failed')
      }

      loading.value = true
      await authStore.signInUser(credentials.value)
      const userExists = await isSuperUser(credentials.value.email)
      if (!userExists) {
        loading.value = false
        await authStore.signOut()
        throw new Error('Super User does not exist')
      }

      await router.push({ name: 'super' })
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }
</script>
<template>
  <div class="d-flex align-center justify-center pa-4 h-screen">
    <div class="position-relative my-sm-16">
      <!-- 👉 Auth card -->
      <VCard class="auth-card pa-4" width="420">
        <VCardItem class="justify-center">
          <VImg :src="logo" width="120" />
        </VCardItem>

        <VCardText>
          <VForm ref="formRef" @submit.prevent="superLogin">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <VTextField
                  v-model="credentials.email"
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="credentials.password"
                  label="Passwort"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'
                  "
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>

              <VCol cols="12" class="mb-8">
                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                  :disabled="loading"
                >
                  Anmelden
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: blank
  public: true
  redirectIfLoggedIn: true
</route>
