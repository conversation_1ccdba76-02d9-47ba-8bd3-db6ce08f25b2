<script setup lang="ts">
  import router from '@/router'
  import { useTheme } from 'vuetify'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { useRoute } from 'vue-router'

  const { colors } = useTheme().current.value
  const route = useRoute()

  const {
    actions: { loadJobAdsByCompany },
  } = useJobAdverts()

  onMounted(async () => {
    await loadJobAdsByCompany()
  })
</script>
<template>
  <VRow>
    <div>
      <VBtn color="primary" variant="text" @click="router.go(-1)">
        <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
        Zurück
      </VBtn>
    </div>
    <VCol cols="12">
      <JobStatistics />
      <!--      <v-card class="pa-8">-->
      <!--        <CompanyForm />-->
      <!--      </v-card>-->
    </VCol>
    <VCol cols="12">
      <JobAdvertTable :on-super="true" />
    </VCol>
  </VRow>
</template>

<route lang="yaml">
meta:
  super: true
  subject: auth
</route>
