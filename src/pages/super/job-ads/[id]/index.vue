<script setup lang="ts">
  import NotAuthorized from '@/components/NotAuthorized.vue'
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const {
    state: { permissions },
  } = usePermissions()

  const showForm = ref(true)

  const toggleForm = () => {
    showForm.value = !showForm.value
  }
</script>

<template>
  <VCard v-if="permissions.canCreateJobAd || permissions.isSuperAdmin">
    <VCardText v-if="showForm">
      <JobAdForm @toggleForm="toggleForm" :id="route.params.id as string" />
    </VCardText>
    <VCardText v-else>
      <JobAdPreview :id="route.params.id as string" @toggleForm="toggleForm" />
    </VCardText>
  </VCard>
  <VCard v-else class="pa-4">
    <NotAuthorized />
  </VCard>
</template>

<route lang="yaml">
meta:
  super: true
</route>
