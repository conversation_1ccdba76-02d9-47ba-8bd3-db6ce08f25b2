<script setup lang="ts">
  import { useCompanySubscriptionConfig } from '@/composables/CompanySubscriptionConfig/useCompanySubscriptionConfig'
  import type { CompanySubscriptionConfig } from '@/composables/CompanySubscriptionConfig/useCompanySubscriptionConfig'

  const {
    state: { allConfigs, loadingAllConfigs, allPricingPlans },
    actions: {
      fetchAllConfigs,
      fetchAllPricingPlans,
      updateSubscriptionConfig,
      bulkUpdateSubscriptionConfigs,
    },
  } = useCompanySubscriptionConfig()

  console.log({ allConfigs: allConfigs.value })

  const selectedConfigs = ref<string[]>([])
  const searchQuery = ref('')
  const subscriptionTypeFilter = ref<string | null>(null)
  const planFilter = ref<string | null>(null)

  const editDialog = ref(false)
  const editingConfig = ref<CompanySubscriptionConfig | null>(null)
  const editForm = ref({
    subscriptionType: 'COMPANY_UNLIMITED' as
      | 'PER_JOB_ADVERT'
      | 'COMPANY_UNLIMITED',
    availablePricingPlanIds: [] as string[],
    notes: '',
  })

  const bulkEditDialog = ref(false)
  const bulkEditForm = ref({
    updateSubscriptionType: false,
    subscriptionType: 'COMPANY_UNLIMITED' as
      | 'PER_JOB_ADVERT'
      | 'COMPANY_UNLIMITED',
    updatePricingPlans: false,
    availablePricingPlanIds: [] as string[],
  })

  const saving = ref(false)

  const subscriptionTypeOptions = [
    { title: 'Company Unlimited', value: 'COMPANY_UNLIMITED' },
    { title: 'Per Job Advert', value: 'PER_JOB_ADVERT' },
  ]

  const headers = [
    { title: 'Company', key: 'company', sortable: true },
    { title: 'Subscription Type', key: 'subscriptionType', sortable: true },
    { title: 'Available Plans', key: 'availablePlans', sortable: false },
    { title: 'Notes', key: 'notes', sortable: false },
    { title: 'Actions', key: 'actions', sortable: false, align: 'end' },
  ]

  // Computed
  const filteredConfigs = computed(() => {
    let configs = allConfigs.value || []

    // Apply search query filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      configs = configs.filter(
        config =>
          config.company.name.toLowerCase().includes(query) ||
          config.company.city.toLowerCase().includes(query) ||
          config.company.country.toLowerCase().includes(query),
      )
    }

    // Apply subscription type filter
    if (subscriptionTypeFilter.value) {
      configs = configs.filter(
        config => config.subscriptionType === subscriptionTypeFilter.value,
      )
    }

    // Apply pricing plan filter
    if (planFilter.value) {
      configs = configs.filter(config =>
        config.availablePricingPlans.some(plan => plan.id === planFilter.value),
      )
    }

    return configs
  })

  const pricingPlanOptions = computed(() =>
    (allPricingPlans.value || []).filter(plan => plan.isActive === true),
  )

  // Methods
  const formatSubscriptionType = (type: string) => {
    switch (type) {
      case 'COMPANY_UNLIMITED':
        return 'Company Unlimited'
      case 'PER_JOB_ADVERT':
        return 'Per Job Advert'
      default:
        return type
    }
  }

  // Format pricing plan with billing interval
  const formatPricingPlan = (plan: any) => {
    if (!plan) return ''

    const months = plan.durationDays
      ? Math.max(1, Math.round(plan.durationDays / 30))
      : 1
    const total = (plan.price || 0) * months

    let interval = ''
    if (months === 1) interval = '/mo'
    else if (months === 3) interval = '/3mo'
    else if (months === 6) interval = '/6mo'
    else if (months === 12) interval = '/yr'
    else interval = `/${months}mo`

    return `€${total}${interval}`
  }

  // Filters are applied client-side via the filteredConfigs computed property

  const openEditDialog = (config: CompanySubscriptionConfig) => {
    editingConfig.value = config
    editForm.value = {
      subscriptionType: config.subscriptionType,
      availablePricingPlanIds: config.availablePricingPlans.map(p => p.id),
      notes: config.notes || '',
    }
    editDialog.value = true
  }

  const closeEditDialog = () => {
    editDialog.value = false
    editingConfig.value = null
    editForm.value = {
      subscriptionType: 'COMPANY_UNLIMITED',
      availablePricingPlanIds: [],
      notes: '',
    }
  }

  const saveConfiguration = async () => {
    if (!editingConfig.value) return

    saving.value = true
    try {
      await updateSubscriptionConfig({
        id: editingConfig.value.id,
        subscriptionType: editForm.value.subscriptionType,
        availablePricingPlanIds: editForm.value.availablePricingPlanIds,
        notes: editForm.value.notes,
      })

      console.log('Configuration updated successfully')
      await fetchAllConfigs()
      closeEditDialog()
    } catch (error) {
      console.error('Failed to update configuration:', error)
    } finally {
      saving.value = false
    }
  }

  const openBulkEditDialog = () => {
    bulkEditForm.value = {
      updateSubscriptionType: false,
      subscriptionType: 'COMPANY_UNLIMITED',
      updatePricingPlans: false,
      availablePricingPlanIds: [],
    }
    bulkEditDialog.value = true
  }

  const closeBulkEditDialog = () => {
    bulkEditDialog.value = false
    bulkEditForm.value = {
      updateSubscriptionType: false,
      subscriptionType: 'COMPANY_UNLIMITED',
      updatePricingPlans: false,
      availablePricingPlanIds: [],
    }
  }

  const saveBulkConfiguration = async () => {
    saving.value = true
    try {
      const updates: any = {}

      if (bulkEditForm.value.updateSubscriptionType) {
        updates.subscriptionType = bulkEditForm.value.subscriptionType
      }

      if (bulkEditForm.value.updatePricingPlans) {
        updates.availablePricingPlanIds =
          bulkEditForm.value.availablePricingPlanIds
      }

      await bulkUpdateSubscriptionConfigs({
        configIds: selectedConfigs.value,
        ...updates,
      })

      console.log(
        `Updated ${selectedConfigs.value.length} configurations successfully`,
      )
      selectedConfigs.value = []
      await fetchAllConfigs()
      closeBulkEditDialog()
    } catch (error) {
      console.error('Failed to update configurations:', error)
    } finally {
      saving.value = false
    }
  }

  // Lifecycle
  onMounted(async () => {
    try {
      await Promise.all([fetchAllConfigs(), fetchAllPricingPlans()])
    } catch (error) {
      console.error('Error loading initial data:', error)
    }
  })
</script>

<template>
  <VContainer fluid>
    <VRow class="mb-6">
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h2 class="text-h4 mb-2">Subscription Configuration</h2>
            <p class="text-body-2 text-medium-emphasis">
              Manage subscription settings for all companies
            </p>
          </div>
        </div>
      </VCol>
    </VRow>

    <!-- Bulk Actions Bar -->
    <VRow v-if="selectedConfigs.length > 0">
      <VCol cols="12">
        <VAlert
          type="info"
          variant="tonal"
          closable
          @click:close="selectedConfigs = []"
        >
          <div class="d-flex justify-space-between align-center">
            <span>{{ selectedConfigs.length }} companies selected</span>
            <div class="d-flex gap-2">
              <VBtn
                size="small"
                variant="elevated"
                color="primary"
                @click="openBulkEditDialog"
              >
                <VIcon start>mdi-pencil</VIcon>
                Bulk Edit
              </VBtn>
              <VBtn size="small" variant="text" @click="selectedConfigs = []">
                Clear Selection
              </VBtn>
            </div>
          </div>
        </VAlert>
      </VCol>
    </VRow>

    <!-- Filters -->
    <VRow>
      <VCol cols="12" md="4">
        <VTextField
          v-model="searchQuery"
          label="Search companies"
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="comfortable"
          clearable
          hide-details
        />
      </VCol>
      <VCol cols="12" md="3">
        <VSelect
          v-model="subscriptionTypeFilter"
          label="Subscription Type"
          :items="subscriptionTypeOptions"
          variant="outlined"
          density="comfortable"
          clearable
          hide-details
        />
      </VCol>
      <VCol cols="12" md="3">
        <VSelect
          v-model="planFilter"
          label="Pricing Plan"
          :items="pricingPlanOptions"
          item-title="displayName"
          item-value="id"
          variant="outlined"
          density="comfortable"
          clearable
          hide-details
        />
      </VCol>
    </VRow>

    <!-- Data Table -->
    <VRow>
      <VCol cols="12">
        <VCard class="pa-4">
          <VDataTable
            v-model="selectedConfigs"
            :headers="headers"
            :items="filteredConfigs"
            :loading="loadingAllConfigs"
            :items-per-page="10"
            show-select
            class="elevation-0"
          >
            <!-- Company Name -->
            <template #item.company="{ item }">
              <div class="d-flex flex-column">
                <span class="font-weight-medium">{{ item.company.name }}</span>
                <span class="text-caption text-medium-emphasis">
                  {{ item.company.city }}, {{ item.company.country }}
                </span>
              </div>
            </template>

            <!-- Subscription Type -->
            <template #item.subscriptionType="{ item }">
              <VChip
                :color="
                  item.subscriptionType === 'COMPANY_UNLIMITED'
                    ? 'success'
                    : 'primary'
                "
                size="small"
                label
              >
                {{ formatSubscriptionType(item.subscriptionType) }}
              </VChip>
            </template>

            <!-- Available Plans -->
            <template #item.availablePlans="{ item }">
              <div class="d-flex flex-wrap gap-1">
                <VChip
                  v-for="plan in item.availablePricingPlans"
                  :key="plan.id"
                  size="x-small"
                  variant="outlined"
                  :title="plan.displayName"
                >
                  {{ plan.displayName }}
                </VChip>
              </div>
              <div
                v-if="!item.availablePricingPlans?.length"
                class="text-medium-emphasis"
              >
                No plans assigned
              </div>
            </template>

            <!-- Notes -->
            <template #item.notes="{ item }">
              <VTooltip v-if="item.notes" :text="item.notes" location="top">
                <template #activator="{ props }">
                  <span
                    v-bind="props"
                    class="text-truncate d-inline-block"
                    style="max-width: 200px"
                  >
                    {{ item.notes }}
                  </span>
                </template>
              </VTooltip>
              <span v-else class="text-medium-emphasis">—</span>
            </template>

            <!-- Actions -->
            <template #item.actions="{ item }">
              <VBtn
                icon
                variant="text"
                size="small"
                @click="openEditDialog(item)"
              >
                <VIcon>mdi-pencil</VIcon>
                <VTooltip activator="parent" location="top"
                  >Edit Configuration</VTooltip
                >
              </VBtn>
            </template>

            <!-- Loading -->
            <template #loading>
              <VSkeletonLoader type="table-row@10" />
            </template>

            <!-- No Data -->
            <template #no-data>
              <div class="text-center py-8">
                <VIcon size="48" class="mb-4 text-medium-emphasis"
                  >mdi-database-off</VIcon
                >
                <p class="text-h6 mb-2">No configurations found</p>
                <p class="text-body-2 text-medium-emphasis">
                  Try adjusting your filters
                </p>
              </div>
            </template>
          </VDataTable>
        </VCard>
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <VDialog v-model="editDialog" max-width="600" persistent>
      <VCard>
        <VCardTitle class="d-flex justify-space-between align-center">
          <span>Edit Subscription Configuration</span>
          <VBtn icon variant="text" @click="closeEditDialog">
            <VIcon>mdi-close</VIcon>
          </VBtn>
        </VCardTitle>

        <VDivider />

        <VCardText class="pa-6">
          <VRow v-if="editingConfig">
            <VCol cols="12">
              <div class="mb-4">
                <p class="text-subtitle-2 text-medium-emphasis mb-2">Company</p>
                <p class="font-weight-medium">
                  {{ editingConfig.company.name }}
                </p>
              </div>
            </VCol>

            <VCol cols="12">
              <VSelect
                v-model="editForm.subscriptionType"
                label="Subscription Type"
                :items="subscriptionTypeOptions"
                variant="outlined"
                density="comfortable"
              />
            </VCol>

            <VCol cols="12">
              <VSelect
                v-model="editForm.availablePricingPlanIds"
                label="Available Pricing Plans"
                :items="pricingPlanOptions"
                item-title="displayName"
                item-value="id"
                variant="outlined"
                density="comfortable"
                multiple
                chips
                closable-chips
              >
                <template #chip="{ props, item }">
                  <VChip v-bind="props" size="small">
                    {{ item.raw.displayName }}
                  </VChip>
                </template>
              </VSelect>
            </VCol>

            <VCol cols="12">
              <VTextarea
                v-model="editForm.notes"
                label="Notes"
                variant="outlined"
                density="comfortable"
                rows="3"
              />
            </VCol>
          </VRow>
        </VCardText>

        <VDivider />

        <VCardActions class="pa-4">
          <VSpacer />
          <VBtn variant="text" @click="closeEditDialog">Cancel</VBtn>
          <VBtn
            color="primary"
            variant="elevated"
            @click="saveConfiguration"
            :loading="saving"
          >
            Save Changes
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Bulk Edit Dialog -->
    <VDialog v-model="bulkEditDialog" max-width="600" persistent>
      <VCard>
        <VCardTitle class="d-flex justify-space-between align-center">
          <span>Bulk Edit Configuration</span>
          <VBtn icon variant="text" @click="closeBulkEditDialog">
            <VIcon>mdi-close</VIcon>
          </VBtn>
        </VCardTitle>

        <VDivider />

        <VCardText class="pa-6">
          <VAlert type="info" variant="tonal" class="mb-4">
            This will update {{ selectedConfigs.length }} selected companies
          </VAlert>

          <VRow>
            <VCol cols="12">
              <VCheckbox
                v-model="bulkEditForm.updateSubscriptionType"
                label="Update Subscription Type"
                density="comfortable"
              />
              <VSelect
                v-if="bulkEditForm.updateSubscriptionType"
                v-model="bulkEditForm.subscriptionType"
                label="Subscription Type"
                :items="subscriptionTypeOptions"
                variant="outlined"
                density="comfortable"
                class="mt-2"
              />
            </VCol>

            <VCol cols="12">
              <VCheckbox
                v-model="bulkEditForm.updatePricingPlans"
                label="Update Available Pricing Plans"
                density="comfortable"
              />
              <VSelect
                v-if="bulkEditForm.updatePricingPlans"
                v-model="bulkEditForm.availablePricingPlanIds"
                label="Available Pricing Plans"
                :items="pricingPlanOptions"
                item-title="displayName"
                item-value="id"
                variant="outlined"
                density="comfortable"
                multiple
                chips
                closable-chips
                class="mt-2"
              >
                <template #chip="{ props, item }">
                  <VChip v-bind="props" size="small">
                    {{ item.raw.displayName }}
                  </VChip>
                </template>
              </VSelect>
            </VCol>
          </VRow>
        </VCardText>

        <VDivider />

        <VCardActions class="pa-4">
          <VSpacer />
          <VBtn variant="text" @click="closeBulkEditDialog">Cancel</VBtn>
          <VBtn
            color="primary"
            variant="elevated"
            @click="saveBulkConfiguration"
            :loading="saving"
            :disabled="
              !bulkEditForm.updateSubscriptionType &&
              !bulkEditForm.updatePricingPlans
            "
          >
            Apply Changes
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VContainer>
</template>
