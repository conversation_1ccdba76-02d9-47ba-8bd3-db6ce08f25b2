<script setup lang="ts">
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import NotAuthorized from '@/components/NotAuthorized.vue'
  import { useRouter } from 'vue-router'
  import { useCompanySubscriptionGraph } from '@/api/graphHooks/useCompanySubscriptionGraph'
  import { usePricingPlanGraph } from '@/api/graphHooks/usePricingPlanGraph'
  import { useSubscriptionMetrics } from '@/composables/Subscriptions/useSubscriptionMetrics'
  import VChart from '@/components/Charts/VChart.vue'

  const router = useRouter()

  const {
    state: { permissions, isLoading },
  } = usePermissions()

  // Company subscriptions hook
  const {
    state: { allSubscriptions, loadingAllSubscriptions },
    actions: { loadAllSubscriptions },
  } = useCompanySubscriptionGraph()

  // Pricing plans hook
  const { loadingAllPlans, allPlans, loadAllPlans } = usePricingPlanGraph()

  // Subscription metrics hook
  const { metrics, loadingMetrics } = useSubscriptionMetrics()

  // Load data on mount
  onMounted(async () => {
    if (permissions.value.isSuperAdmin) {
      await Promise.all([loadAllSubscriptions(), loadAllPlans()])
    }
  })

  // Calculate subscription statistics
  const subscriptionStats = computed(() => {
    if (!allSubscriptions.value) return null

    const total = allSubscriptions.value.length
    const active = allSubscriptions.value.filter(
      (s: any) => s.status === 'ACTIVE',
    ).length
    const trial = allSubscriptions.value.filter(
      (s: any) => s.status === 'TRIAL',
    ).length
    const canceled = allSubscriptions.value.filter(
      (s: any) => s.status === 'CANCELED',
    ).length
    const pastDue = allSubscriptions.value.filter(
      (s: any) => s.status === 'PAST_DUE',
    ).length

    return {
      total,
      active,
      trial,
      canceled,
      pastDue,
    }
  })

  // Calculate revenue statistics - now using real metrics
  const revenueStats = computed(() => {
    if (!metrics.value) return null

    return {
      monthly: metrics.value.totalRevenue,
      annual: metrics.value.totalRevenue * 12, // Adjust if you have actual annual projections
      average: metrics.value.averageRevenuePerCompany,
    }
  })

  const planRevenue = computed(() => {
    if (!metrics.value?.topPlans) return []
    return metrics.value.topPlans
  })

  // Revenue trend data for charts
  const revenueChartData = computed(() => {
    if (!metrics.value?.revenueByPeriod) return null

    const periods = metrics.value.revenueByPeriod
      .sort(
        (a, b) => new Date(a.period).getTime() - new Date(b.period).getTime(),
      )
      .slice(-6) // Last 6 months

    return {
      labels: periods.map(p => {
        const date = new Date(p.period)
        return date.toLocaleString('default', {
          month: 'short',
          year: '2-digit',
        })
      }),
      datasets: [
        {
          label: 'Monthly Revenue',
          data: periods.map(p => p.revenue),
          borderColor: '#4CAF50',
          backgroundColor: '#4CAF5022',
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Active Subscriptions',
          data: periods.map(p => p.subscriberCount),
          borderColor: '#1976D2',
          backgroundColor: '#1976D222',
          fill: true,
          tension: 0.4,
          yAxisID: 'subscribers',
        },
      ],
    }
  })

  const revenueChartOptions = computed(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Revenue (€)',
        },
        grid: {
          display: true,
        },
      },
      subscribers: {
        beginAtZero: true,
        position: 'right',
        title: {
          display: true,
          text: 'Active Subscriptions',
        },
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: (context: any) => {
            if (context.datasetIndex === 0) {
              return `Revenue: €${context.raw.toFixed(2)}`
            }
            return `Subscriptions: ${context.raw}`
          },
        },
      },
    },
  }))

  const navigateToPricingPlans = () => {
    router.push('/super/subscription/pricing-plans')
  }

  const navigateToCompanySubscriptions = () => {
    router.push('/super/subscription/companies')
  }
</script>

<template>
  <div>
    <VCard v-if="isLoading">
      <v-skeleton-loader
        class="mx-auto"
        elevation="12"
        max-height="800px"
        type="table-heading, list-item-two-line, image"
      ></v-skeleton-loader>
    </VCard>

    <template v-else-if="permissions.isSuperAdmin">
      <div class="mb-4">
        <VCardTitle class="text-h4 d-flex align-center justify-space-between">
          <span>Subscription Management</span>
        </VCardTitle>
        <VCardSubtitle>
          Overview of all subscriptions and revenue
        </VCardSubtitle>
      </div>

      <VRow class="mb-6">
        <VCol cols="12" md="12">
          <VCard>
            <VCardTitle>Subscription Statistics</VCardTitle>
            <VCardText>
              <VRow v-if="subscriptionStats">
                <VCol cols="6" md="3">
                  <div class="text-center">
                    <div class="text-h2 text-primary">
                      {{ subscriptionStats.total }}
                    </div>
                    <div class="text-caption">Total Subscriptions</div>
                  </div>
                </VCol>
                <VCol cols="6" md="3">
                  <div class="text-center">
                    <div class="text-h2 text-success">
                      {{ subscriptionStats.active }}
                    </div>
                    <div class="text-caption">Active</div>
                  </div>
                </VCol>
                <VCol cols="6" md="3">
                  <div class="text-center">
                    <div class="text-h2 text-info">
                      {{ subscriptionStats.trial }}
                    </div>
                    <div class="text-caption">Expired</div>
                  </div>
                </VCol>
                <VCol cols="6" md="3">
                  <div class="text-center">
                    <div class="text-h2 text-error">
                      {{ subscriptionStats.canceled }}
                    </div>
                    <div class="text-caption">Canceled</div>
                  </div>
                </VCol>
              </VRow>
              <div v-else class="text-center py-4">
                <VProgressCircular indeterminate />
              </div>
            </VCardText>
          </VCard>
        </VCol>

        <!-- Revenue Stats -->
        <!--        <VCol cols="12" md="4">-->
        <!--          <VCard>-->
        <!--            <VCardTitle>Revenue</VCardTitle>-->
        <!--            <VCardText>-->
        <!--              <div v-if="revenueStats">-->
        <!--                <div class="mb-4">-->
        <!--                  <div class="text-overline">Monthly Recurring Revenue</div>-->
        <!--                  <div class="text-h3 text-primary">-->
        <!--                    €{{ revenueStats.monthly.toFixed(2) }}-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--                <div class="mb-4">-->
        <!--                  <div class="text-overline">Average Revenue per Company</div>-->
        <!--                  <div class="text-h4">-->
        <!--                    €{{ revenueStats.average.toFixed(2) }}-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--                <div>-->
        <!--                  <div class="text-overline">Annual Revenue Projection</div>-->
        <!--                  <div class="text-h4 text-success">-->
        <!--                    €{{ revenueStats.annual.toFixed(2) }}-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--              </div>-->
        <!--              <div v-else class="text-center py-4">-->
        <!--                <VProgressCircular indeterminate />-->
        <!--              </div>-->
        <!--            </VCardText>-->
        <!--          </VCard>-->
        <!--        </VCol>-->
      </VRow>

      <!-- Revenue Trends -->
      <!--      <VCard class="mb-6">-->
      <!--        <VCardTitle class="d-flex justify-space-between align-center pa-4">-->
      <!--          <span>Revenue Trends</span>-->
      <!--          <VChip size="small" color="info" variant="flat">-->
      <!--            Last 6 months-->
      <!--          </VChip>-->
      <!--        </VCardTitle>-->
      <!--        <VCardText class="pa-4">-->
      <!--          <div v-if="revenueChartData" style="height: 300px">-->
      <!--            &lt;!&ndash; Chart.js implementation &ndash;&gt;-->
      <!--            <VChart-->
      <!--              v-if="false"-->
      <!--              type="line"-->
      <!--              :data="revenueChartData"-->
      <!--              :options="revenueChartOptions"-->
      <!--            />-->
      <!--            &lt;!&ndash; Vuetify Sparkline Fallback &ndash;&gt;-->
      <!--            <VRow v-if="metrics?.revenueByPeriod">-->
      <!--              <VCol cols="12">-->
      <!--                <div class="mb-2">-->
      <!--                  <div class="text-h6">Revenue Trend</div>-->
      <!--                  <VSparkline-->
      <!--                    :model-value="-->
      <!--                      metrics.revenueByPeriod.slice(-6).map(p => p.revenue)-->
      <!--                    "-->
      <!--                    :gradient="['#4CAF50', '#8BC34A', '#CDDC39']"-->
      <!--                    :smooth="10"-->
      <!--                    :padding="8"-->
      <!--                    :line-width="3"-->
      <!--                    :stroke-linecap="'round'"-->
      <!--                    :gradient-direction="'top'"-->
      <!--                    :fill="true"-->
      <!--                    :auto-draw="true"-->
      <!--                    :auto-draw-duration="2000"-->
      <!--                    height="100"-->
      <!--                  >-->
      <!--                    <template v-slot:label="item">-->
      <!--                      €{{ item.value.toFixed(0) }}-->
      <!--                    </template>-->
      <!--                  </VSparkline>-->
      <!--                  <div class="d-flex justify-space-between mt-2 text-caption">-->
      <!--                    <span-->
      <!--                      v-for="(p, i) in metrics.revenueByPeriod.slice(-6)"-->
      <!--                      :key="i"-->
      <!--                    >-->
      <!--                      {{-->
      <!--                        new Date(p.period).toLocaleString('default', {-->
      <!--                          month: 'short',-->
      <!--                        })-->
      <!--                      }}-->
      <!--                    </span>-->
      <!--                  </div>-->
      <!--                </div>-->
      <!--              </VCol>-->
      <!--              <VCol cols="12">-->
      <!--                <div>-->
      <!--                  <div class="text-h6">Subscription Growth</div>-->
      <!--                  <VSparkline-->
      <!--                    :model-value="-->
      <!--                      metrics.revenueByPeriod-->
      <!--                        .slice(-6)-->
      <!--                        .map(p => p.subscriberCount)-->
      <!--                    "-->
      <!--                    :gradient="['#1976D2', '#42A5F5', '#64B5F6']"-->
      <!--                    :smooth="10"-->
      <!--                    :padding="8"-->
      <!--                    :line-width="3"-->
      <!--                    :stroke-linecap="'round'"-->
      <!--                    :gradient-direction="'top'"-->
      <!--                    :fill="true"-->
      <!--                    :auto-draw="true"-->
      <!--                    :auto-draw-duration="2000"-->
      <!--                    height="100"-->
      <!--                  >-->
      <!--                    <template v-slot:label="item">-->
      <!--                      {{ item.value }}-->
      <!--                    </template>-->
      <!--                  </VSparkline>-->
      <!--                  <div class="d-flex justify-space-between mt-2 text-caption">-->
      <!--                    <span-->
      <!--                      v-for="(p, i) in metrics.revenueByPeriod.slice(-6)"-->
      <!--                      :key="i"-->
      <!--                    >-->
      <!--                      {{-->
      <!--                        new Date(p.period).toLocaleString('default', {-->
      <!--                          month: 'short',-->
      <!--                        })-->
      <!--                      }}-->
      <!--                    </span>-->
      <!--                  </div>-->
      <!--                </div>-->
      <!--              </VCol>-->
      <!--            </VRow>-->
      <!--          </div>-->
      <!--          <div-->
      <!--            v-else-->
      <!--            class="d-flex justify-center align-center"-->
      <!--            style="height: 300px"-->
      <!--          >-->
      <!--            <VProgressCircular indeterminate color="primary" />-->
      <!--          </div>-->
      <!--        </VCardText>-->
      <!--      </VCard>-->

      <!-- Plan Distribution -->
      <VCard class="mb-6">
        <VCardTitle class="pa-4">Plan Distribution</VCardTitle>
        <VCardText class="pa-4">
          <VRow v-if="allPlans && allPlans.length > 0">
            <VCol v-for="plan in allPlans" :key="plan.id" cols="12" md="4">
              <VCard variant="outlined">
                <VCardText>
                  <div class="d-flex justify-space-between align-center mb-2">
                    <span class="text-h6">{{ plan.name }}</span>
                    <VChip
                      :color="plan.isActive ? 'success' : 'error'"
                      size="small"
                    >
                      {{ plan.isActive ? 'Active' : 'Inactive' }}
                    </VChip>
                  </div>
                  <div class="text-h4">
                    {{
                      planRevenue?.find(p => p.planId === plan.id)
                        ?.subscriberCount || 0
                    }}
                  </div>
                  <div class="text-caption">Active Subscriptions</div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
          <div v-else class="text-center py-4">
            <p class="text-body-1 mb-4">No pricing plans configured</p>
            <VBtn color="primary" @click="navigateToPricingPlans">
              Create Pricing Plans
            </VBtn>
          </div>
        </VCardText>
      </VCard>

      <!-- Recent Subscriptions -->
      <VCard>
        <VCardTitle>Recent Subscriptions</VCardTitle>
        <VCardText>
          <VTable v-if="allSubscriptions && allSubscriptions.length > 0">
            <thead>
              <tr>
                <th>Company</th>
                <th>Plan</th>
                <th>Status</th>
                <th>Billing Period</th>
                <th>Started</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="subscription in allSubscriptions.slice(0, 10)"
                :key="subscription.id"
              >
                <td>{{ subscription.companyName }}</td>
                <td>{{ subscription.planName }}</td>
                <td>
                  <VChip
                    :color="
                      subscription.status === 'ACTIVE'
                        ? 'success'
                        : subscription.status === 'EXPIRED'
                          ? 'info'
                          : subscription.status === 'PAST_DUE'
                            ? 'warning'
                            : 'error'
                    "
                    size="small"
                  >
                    {{ subscription.status }}
                  </VChip>
                </td>
                <td>{{ subscription.billingPeriod || '-' }}</td>
                <td>
                  {{
                    subscription.createdAt
                      ? new Date(subscription.createdAt).toLocaleDateString()
                      : '-'
                  }}
                </td>
                <td>
                  <VBtn
                    icon
                    size="small"
                    variant="text"
                    @click="
                      router.push(
                        `/super/subscription/companies/${subscription.companyId}`,
                      )
                    "
                  >
                    <VIcon icon="mdi-eye" />
                  </VBtn>
                </td>
              </tr>
            </tbody>
          </VTable>
          <div v-else class="text-center py-4">
            <p class="text-body-1">No subscriptions yet</p>
          </div>
        </VCardText>
      </VCard>
    </template>

    <VCard v-else class="pa-4">
      <NotAuthorized />
    </VCard>
  </div>
</template>

<route lang="yaml">
meta:
  super: true
  subject: auth
</route>
