<script setup lang="ts">
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import NotAuthorized from '@/components/NotAuthorized.vue'
  import { useRouter } from 'vue-router'
  import { useCompanySubscriptionGraph } from '@/api/graphHooks/useCompanySubscriptionGraph'

  const router = useRouter()

  const {
    state: { permissions, isLoading },
  } = usePermissions()

  const {
    state: { allSubscriptions, loadingAllSubscriptions },
    actions: {
      loadAllSubscriptions,
      cancelSubscription,
      reactivateSubscription,
    },
  } = useCompanySubscriptionGraph()

  // Search and filter states
  const search = ref('')
  const statusFilter = ref('ALL')
  const billingPeriodFilter = ref('ALL')

  // Pagination
  const page = ref(1)
  const itemsPerPage = ref(10)

  // Load data on mount
  onMounted(async () => {
    if (permissions.value.isSuperAdmin) {
      await loadAllSubscriptions()
    }
  })

  // Filter subscriptions based on search and filters
  const filteredSubscriptions = computed(() => {
    if (!allSubscriptions.value) return []

    let filtered = [...allSubscriptions.value]

    // Search filter
    if (search.value) {
      const searchLower = search.value.toLowerCase()
      filtered = filtered.filter(
        (s: any) =>
          s.companyName?.toLowerCase().includes(searchLower) ||
          s.planName?.toLowerCase().includes(searchLower),
      )
    }

    // Status filter
    if (statusFilter.value !== 'ALL') {
      filtered = filtered.filter((s: any) => s.status === statusFilter.value)
    }

    // Billing period filter
    if (billingPeriodFilter.value !== 'ALL') {
      filtered = filtered.filter(
        (s: any) => s.billingPeriod === billingPeriodFilter.value,
      )
    }

    return filtered
  })

  // Paginated subscriptions
  const paginatedSubscriptions = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredSubscriptions.value.slice(start, end)
  })

  // Total pages
  const totalPages = computed(() => {
    return Math.ceil(filteredSubscriptions.value.length / itemsPerPage.value)
  })

  // Handle subscription cancellation
  const handleCancelSubscription = async (subscriptionId: string) => {
    if (confirm('Are you sure you want to cancel this subscription?')) {
      try {
        await cancelSubscription(subscriptionId)
        await loadAllSubscriptions()
      } catch (error) {
        console.error('Failed to cancel subscription:', error)
      }
    }
  }

  // Handle subscription reactivation
  const handleReactivateSubscription = async (subscriptionId: string) => {
    try {
      await reactivateSubscription(subscriptionId)
      await loadAllSubscriptions()
    } catch (error) {
      console.error('Failed to reactivate subscription:', error)
    }
  }

  // Navigate to company details
  const viewCompanyDetails = (companyId: string) => {
    router.push(`/super/company/${companyId}`)
  }
</script>

<template>
  <div>
    <VCard v-if="isLoading">
      <v-skeleton-loader
        class="mx-auto"
        elevation="12"
        max-height="800px"
        type="table-heading, list-item-two-line, image"
      ></v-skeleton-loader>
    </VCard>

    <template v-else-if="permissions.isSuperAdmin">
      <VRow>
        <VCol cols="12">
          <div class="d-flex justify-space-between align-center">
            <div>
              <h2 class="text-h4 mb-2">Company Subscriptions</h2>
              <p class="text-body-2 text-medium-emphasis">
                Manage all company subscriptions
              </p>
            </div>
          </div>
        </VCol>
      </VRow>

      <!-- Filters -->
      <VRow class="mb-2">
        <VCol cols="12" md="4">
          <VTextField
            v-model="search"
            label="Search"
            variant="outlined"
            density="compact"
            prepend-inner-icon="mdi-magnify"
            clearable
            hide-details
          />
        </VCol>
        <VCol cols="12" md="4">
          <VSelect
            v-model="statusFilter"
            label="Status"
            variant="outlined"
            density="compact"
            :items="[
              { title: 'All', value: 'ALL' },
              { title: 'Active', value: 'ACTIVE' },
              { title: 'Expired', value: 'EXPIRED' },
              { title: 'Past Due', value: 'PAST_DUE' },
              { title: 'Canceled', value: 'CANCELED' },
            ]"
            hide-details
          />
        </VCol>
        <VCol cols="12" md="4">
          <VSelect
            v-model="billingPeriodFilter"
            label="Billing Period"
            variant="outlined"
            density="compact"
            :items="[
              { title: 'All', value: 'ALL' },
              { title: 'Monthly', value: 'MONTHLY' },
              { title: 'Quarterly', value: 'QUARTERLY' },
              { title: 'Yearly', value: 'YEARLY' },
              { title: 'One Time', value: 'ONE_TIME' },
            ]"
            hide-details
          />
        </VCol>
      </VRow>

      <!-- Subscriptions Table -->
      <VCard>
        <VCardText>
          <VTable v-if="paginatedSubscriptions.length > 0">
            <thead>
              <tr>
                <th>Company</th>
                <th>Plan</th>
                <th>Status</th>
                <th>Billing</th>
                <th>Amount</th>
                <th>Started</th>
                <th>Expires</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="subscription in paginatedSubscriptions"
                :key="subscription.id"
              >
                <td>
                  <div>
                    <div class="font-weight-medium">
                      {{ subscription.companyName }}
                    </div>
                  </div>
                </td>
                <td>{{ subscription.planName }}</td>
                <td>
                  <VChip
                    :color="
                      subscription.status === 'ACTIVE'
                        ? 'success'
                        : subscription.status === 'EXPIRED'
                          ? 'info'
                          : subscription.status === 'PAST_DUE'
                            ? 'warning'
                            : 'error'
                    "
                    size="small"
                  >
                    {{ subscription.status }}
                  </VChip>
                </td>
                <td>{{ subscription.billingPeriod || '-' }}</td>
                <td>
                  <span v-if="subscription.monthlyAmount != null">
                    €{{ Number(subscription.monthlyAmount).toFixed(2) }}
                  </span>
                  <span v-else>—</span>
                </td>
                <td>
                  {{
                    subscription.createdAt
                      ? new Date(subscription.createdAt).toLocaleDateString()
                      : '-'
                  }}
                </td>
                <td>
                  {{
                    subscription.expiresAt
                      ? new Date(subscription.expiresAt).toLocaleDateString()
                      : '-'
                  }}
                </td>
                <td>
                  <VMenu>
                    <template v-slot:activator="{ props }">
                      <VBtn icon variant="text" size="small" v-bind="props">
                        <VIcon icon="mdi-dots-vertical" />
                      </VBtn>
                    </template>
                    <VList>
                      <VListItem
                        @click="viewCompanyDetails(subscription.companyId)"
                      >
                        <VListItemTitle>
                          <VIcon icon="mdi-eye" class="mr-2" />
                          View Company
                        </VListItemTitle>
                      </VListItem>
                      <VListItem
                        v-if="subscription.status === 'ACTIVE'"
                        @click="handleCancelSubscription(subscription.id)"
                      >
                        <VListItemTitle class="text-error">
                          <VIcon icon="mdi-cancel" class="mr-2" />
                          Cancel Subscription
                        </VListItemTitle>
                      </VListItem>
                      <VListItem
                        v-if="subscription.status === 'CANCELED'"
                        @click="handleReactivateSubscription(subscription.id)"
                      >
                        <VListItemTitle class="text-success">
                          <VIcon icon="mdi-restore" class="mr-2" />
                          Reactivate
                        </VListItemTitle>
                      </VListItem>
                    </VList>
                  </VMenu>
                </td>
              </tr>
            </tbody>
          </VTable>

          <div v-else class="text-center py-8">
            <VIcon
              icon="mdi-magnify"
              size="48"
              class="mb-4 text-medium-emphasis"
            />
            <p class="text-body-1">No subscriptions found</p>
          </div>

          <!-- Pagination -->
          <div v-if="totalPages > 1" class="d-flex justify-center mt-4">
            <VPagination
              v-model="page"
              :length="totalPages"
              :total-visible="7"
            />
          </div>
        </VCardText>
      </VCard>
    </template>

    <VCard v-else class="pa-4">
      <NotAuthorized />
    </VCard>
  </div>
</template>

<route lang="yaml">
meta:
  super: true
  subject: auth
</route>
