<script setup lang="ts">
  import PricingPlanManagement from '@/components/Admin/PricingPlanManagement.vue'
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import NotAuthorized from '@/components/NotAuthorized.vue'

  const {
    state: { permissions, isLoading },
  } = usePermissions()
</script>

<template>
  <div>
    <VCard v-if="isLoading">
      <v-skeleton-loader
        class="mx-auto"
        elevation="12"
        max-height="800px"
        type="table-heading, list-item-two-line, image"
      ></v-skeleton-loader>
    </VCard>

    <template v-else-if="permissions.isSuperAdmin">
      <PricingPlanManagement />
    </template>

    <VCard v-else class="pa-4">
      <NotAuthorized />
    </VCard>
  </div>
</template>

<route lang="yaml">
meta:
  super: true
  subject: auth
</route>
