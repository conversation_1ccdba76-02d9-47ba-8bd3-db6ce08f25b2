import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
import { functions } from '@/libs/firebase/config'
import { uploadFile } from '@/libs/firebase/upload-file'
import type AddEditUser from '@/types/company-user/add-edit-user'
import type CompanyUser from '@/types/company-user/company-user'
import ImageCropperResult from '@/types/image-cropper-result'
import { uuidv4 } from '@firebase/util'
import type { Unsubscribe } from 'firebase/firestore'
import { httpsCallable } from 'firebase/functions'
import { defineStore } from 'pinia'
import { useAuthStore } from './authStore'

let unsubUsers: Unsubscribe | null = null

export const useUsersStore = defineStore('usersStore', {
  state: () => {
    return {
      activeUser: {} as CompanyUser,
      users: [] as CompanyUser[],
      userForm: {
        id: null as string | null,
        name: '',
        email: '',
        avatarImageUrl: '',
        imageResult: null as ImageCropperResult | null,
      },
      createJobAd: false,
      superAdmin: false,
      editCompany: false,
      createUser: false,
      loadingCompanyUsers: false,
    }
  },

  actions: {
    async init() {
      const authStore = useAuthStore()
      const activeUserId = authStore.claims.companyUserId
      const {
        state: { companyUsers, loadingCompanyUsers },
        actions: { loadCompanyUsers },
      } = useCompanyUser()

      if (companyUsers.value.length < 1) {
        await loadCompanyUsers()
      }
      const [activeUser] = companyUsers.value.filter((user: CompanyUser) => {
        return user.id === activeUserId
      })

      this.loadingCompanyUsers = computed(() => loadingCompanyUsers.value).value

      this.users = companyUsers.value
      this.activeUser = activeUser
    },
    resetStore() {
      this.activeUser = {} as CompanyUser
      this.users = [] as CompanyUser[]
      this.userForm = {
        id: null as string | null,
        name: '',
        email: '',
        avatarImageUrl: '',
        imageResult: null as ImageCropperResult | null,
      }
      this.createJobAd = false
      this.superAdmin = false
      this.editCompany = false
      this.createUser = false
      this.loadingCompanyUsers = false
    },
    updateSuperAdminRight(value: boolean) {
      this.superAdmin = value
    },
    updateCreateJobAdRight(value: boolean) {
      this.createJobAd = value
    },
    updateEditCompanyRight(value: boolean) {
      this.editCompany = value
    },
    updateCreateUserRight(value: boolean) {
      this.createUser = value
    },
    updateUserForm(user: CompanyUser) {
      this.userForm = {
        id: user.id,
        name: user.name,
        avatarImageUrl: user?.avatarImageUrl,
        email: user.email,
        imageResult: null,
      }
      // @ts-ignore
      this.superAdmin = user.rights[0]?.superAdmin || false
      this.createJobAd = user.rights[0]?.createJobAd || false
      this.editCompany = user.rights[0]?.editCompany || false
      this.createUser = user.rights[0]?.createUser || false
    },
    setActiveUser(user: CompanyUser) {
      this.activeUser = user
    },

    async updateUser(payload: AddEditUser): Promise<boolean | string> {
      const authStore = useAuthStore()
      const companyId = authStore.companyId
      const userToUpdate = this.users.find(user => user.id === payload.id)
      let profileImageDownloadUrl = null

      if (payload.imageResult) {
        const uploadPath = `companies/${companyId}/users/${payload.id}/avatarImage.jpeg`
        try {
          profileImageDownloadUrl = await uploadFile(
            payload.imageResult.imageBlob,
            uploadPath,
          )
          URL.revokeObjectURL(payload.imageResult.imageUrl)
        } catch (error) {
          console.log(error)

          return 'Upload Error'
        }
      }

      const updateFunction = httpsCallable(functions, 'updateUser')

      try {
        const result = await updateFunction({
          uid: payload.id,
          name: payload.name,
          rights: payload.rights,
          ...(userToUpdate?.email !== payload.email && {
            email: payload.email,
          }),
          ...(profileImageDownloadUrl != null && {
            profileImageUrl: profileImageDownloadUrl,
          }),
        })
      } catch (error) {
        console.log(error)

        return 'Update Error'
      }

      return true
    },

    async uploadUserAvatar(
      imageResult: ImageCropperResult,
    ): Promise<string | undefined> {
      const imageId = uuidv4()

      if (imageResult != null) {
        const authStore = useAuthStore()
        const companyId = authStore.companyId
        const uploadPath = `companies/${companyId}/users/${imageId}.jpeg`

        return await uploadFile(imageResult.imageBlob, uploadPath)
      }
    },

    async deleteUser(uid: string): Promise<boolean> {
      const deleteFunction = httpsCallable<
        Record<string, unknown>,
        Record<string, boolean>
      >(functions, 'deleteUserFromCompany')
      let success = false
      try {
        const res = await deleteFunction({
          uid,
        })

        success = res.data.success
      } catch (error) {
        console.log(error)
      }

      return success
    },
  },
  getters: {
    getActiveUser(): CompanyUser {
      return this.activeUser
    },
    getUsers(): CompanyUser[] {
      return this.users
    },
    getCreateJobAdRight(): boolean {
      return this.createJobAd
    },
    getEditCompanyRight(): boolean {
      return this.editCompany
    },
    getCreateUserRight(): boolean {
      return this.createUser
    },
  },
})
