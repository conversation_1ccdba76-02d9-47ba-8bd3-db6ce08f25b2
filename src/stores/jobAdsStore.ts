import { uploadFile } from '@/libs/firebase/upload-file'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
import type JobAd from '@/types/job-ad/job-ad'
import { uuidv4 } from '@firebase/util'
import type { Unsubscribe } from 'firebase/firestore'
import { defineStore } from 'pinia'
import { useAuthStore } from './authStore'

let unsubJobAds: Unsubscribe | undefined

export const useJobAdsStore = defineStore('jobAdsStore', {
  state: () => {
    return {
      jobAds: [] as JobAd[],
      jobAdLikes: {} as Record<string, any[]>,
      statistics: {} as Record<string, unknown>,
      filter: '',
    }
  },
  actions: {
    async init() {
      if (!unsubJobAds) {
        const authSore = useAuthStore()
        if (!authSore.companyId || !authSore.user) return
      }
    },

    async setJobAdImage(): Promise<string> {
      const authStore = useAuthStore()
      const jobAdForm = useJobAdFormStore()

      if (!jobAdForm.headerResult)
        throw new Error('Please provide a header image')

      const companyId = authStore.companyId
      const imageId = uuidv4()

      const uploadPath = `companies/${companyId}/jobAds/${imageId}.jpeg`

      return await uploadFile(jobAdForm.headerResult.imageBlob, uploadPath)
    },

    async updateJobLikes(jobAdLikes: any[], jobAdId: string) {
      const existingLikes = this.jobAdLikes[jobAdId] || []
      const combinedLikes = [
        ...new Map(
          [...existingLikes, ...jobAdLikes].map(like => [like.id, like]),
        ).values(),
      ]

      if (combinedLikes.length > 0) {
        this.jobAdLikes[jobAdId] = combinedLikes
      } else {
        delete this.jobAdLikes[jobAdId]
      }
    },

    async pushToJobLikes(jobAdLike: any, jobAdvertId: string) {
      if (
        jobAdLike?.state === 'BOOKMARKED' ||
        jobAdLike?.state === 'DELETED' ||
        jobAdLike?.deletedFromCompany
      )
        return
      if (!this.jobAdLikes[jobAdvertId]) {
        this.jobAdLikes[jobAdvertId] = []
      }

      const jobAdLikeIndex = this.jobAdLikes[jobAdvertId].findIndex(
        like => like.id === jobAdLike.id,
      )

      if (jobAdLikeIndex === -1) {
        this.jobAdLikes[jobAdvertId].push(jobAdLike)
      } else {
        this.jobAdLikes[jobAdvertId][jobAdLikeIndex] = jobAdLike
      }
    },
    async updateJobLikeStatus(
      jobAdvertId: string,
      jobActionId: string,
      applicantId: string,
      newStatus: string,
      newState: string,
    ) {
      if (!this.jobAdLikes[jobAdvertId]) {
        console.log(`JobAdvert with ID ${jobAdvertId} not found in store`)
        return
      }

      const jobLikeIndex = this.jobAdLikes[jobAdvertId].findIndex(
        like => like.id === jobActionId && like.applicantId === applicantId,
      )
      console.log({ jobLikeIndex })

      if (jobLikeIndex === -1) {
        console.log(
          `JobAction with ID ${jobActionId} and applicantId ${applicantId} not found`,
        )
        return
      }

      this.jobAdLikes[jobAdvertId][jobLikeIndex].status = newStatus
      this.jobAdLikes[jobAdvertId][jobLikeIndex].state = newState

      console.log(
        'Updated job like:',
        this.jobAdLikes[jobAdvertId][jobLikeIndex],
      )

      // Force reactivity update by creating a new array reference
      this.jobAdLikes = { ...this.jobAdLikes }
    },
    async removeJobLike(jobAdLikeId: string, jobAdvertId: string) {
      if (!this.jobAdLikes[jobAdvertId]) {
        this.jobAdLikes[jobAdvertId] = []
      }

      const jobAdLikeIndex = this.jobAdLikes[jobAdvertId].findIndex(
        like => like.id === jobAdLikeId,
      )

      this.jobAdLikes[jobAdvertId].splice(jobAdLikeIndex, 1)
    },
    /**
     * Call this before sign out.
     */
    resetStore() {
      if (unsubJobAds) {
        unsubJobAds()
        unsubJobAds = undefined
      }
      this.jobAds = []
      this.statistics = {}
      this.filter = ''
    },
  },
})
