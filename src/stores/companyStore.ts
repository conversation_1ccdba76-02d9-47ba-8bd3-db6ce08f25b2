import { useAuthStore } from '@/stores/authStore'
import type { Unsubscribe } from 'firebase/firestore'
import {
  collection,
  deleteDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore'
import { defineStore } from 'pinia'
import { db } from '@/libs/firebase/config'
import { uploadFile } from '@/libs/firebase/upload-file'
import { deleteTextImage } from '@/libs/firebase/upload-text-image'
import type Company from '@/types/company/company'
import CompanyImages from '@/types/company/CompanyImages'
import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
import { useCompanyDetailsGraphData } from '@/api/graphHooks/company/useCompanyDetailsGraphData'

let unsubCompany: Unsubscribe | null = null

export const useCompanyStore = defineStore('companyStore', {
  state: () => {
    return {
      company: null as Company | null,
      showCompanyForm: false,
      showContactPersonsList: false,
      showFairJobsList: false,
      showContactPersonTimeslots: false,
      showCompanyFairJobEditDialog: false,
      allCompaniesForFair: [] as Company[],
      companyFairJobEditDialogData: null as {
        id: string
        description: string
      } | null,
    }
  },
  getters: {
    /**
     * Returns the company object
     * @returns {Company | null} Company object
     */
    getCompany(): Company | null {
      return this.company
    },
  },
  actions: {
    /**
     * Initializes the company store. Should be called straight after auth is available.
     */
    async init() {
      const {
        state: { companyDetails },
        actions: { refetchCompany },
      } = useCompanyDetailsGraphData()

      try {
        await refetchCompany()
        if (!this.company) {
          this.company = companyDetails.value
        }
      } catch (error) {
        console.error('Failed to fetch company:', error)
      }
    },
    setCompanyDetails(company: any) {
      if (!company.id || this.company?.id === company.id) {
        return
      }
      this.company = company
    },
    toggleCompanyForm() {
      this.showCompanyForm = !this.showCompanyForm
    },
    setContactPersonsListView(val: boolean) {
      this.showContactPersonsList = val
    },
    setFairJobsListView(val: boolean) {
      this.showFairJobsList = val
    },
    setContactPersonTimeSlotsView(val: boolean) {
      this.showContactPersonTimeslots = val
    },
    setFairJobEditDialog(val: boolean) {
      this.showCompanyFairJobEditDialog = val
    },
    updateFairJobEditDialogData(data: any) {
      this.companyFairJobEditDialogData = data
    },

    updateCompany(company: Company | any) {
      this.company = company
    },
    updateCompaniesInFair(allCompaniesForFair: any[]) {
      this.allCompaniesForFair = allCompaniesForFair
    },
    updateCompanyInFair(companyId: string) {
      this.allCompaniesForFair = this.allCompaniesForFair.map(company =>
        company.id === companyId ? { ...company, isInFair: true } : company,
      )
    },

    updateCompanyManagedStatus(isManaged: boolean) {
      this.company = {
        ...this.company,
        isFairManaged: isManaged,
      }
    },

    resetCompanyFairJob() {
      this.companyFairJobEditDialogData = {
        id: '',
        description: '',
      }
      this.showCompanyFairJobEditDialog = false
    },

    resetCompany() {
      this.company = null
    },

    updateStripeCustomerId(stripeCustomerId: string) {
      if (this.company) {
        this.company = {
          ...this.company,
          stripeCustomerId: stripeCustomerId,
        }
      }
    },
    async uploadCompanyImages(data: CompanyImages, createNew = false) {
      const companyFormData = useCompanyFormStore()
      const authStore = useAuthStore()
      console.log('companyId')
      const randomId = Math.random().toString(36).substring(7)

      const route = useRoute()

      const companyId = computed(() => {
        return (
          this.getCompany?.id ||
          authStore.companyId ||
          (route && route.params ? route.params.cid : null) ||
          randomId
        )
      }).value

      const imagePromises: Promise<string>[] = []
      let headerUrl: string = ''
      let logoUrl: string = ''

      if (data.headerResult != null) {
        const uploadPath = `companies/${companyId}/headerImage.jpeg`

        imagePromises.push(uploadFile(data.headerResult.imageBlob, uploadPath))
        URL.revokeObjectURL(data.headerResult.imageUrl)
        console.log(`companies/${companyId}/headerImage.jpeg`)
      }
      if (data.logoResult != null) {
        const uploadPath = `companies/${companyId}/logoImage.jpeg`

        imagePromises.push(uploadFile(data.logoResult.imageBlob, uploadPath))
        URL.revokeObjectURL(data.logoResult.imageUrl)
        console.log(`companies/${companyId}/logoImage.jpeg`)
      }

      try {
        const imagePromises = {
          header:
            data.headerResult != null
              ? uploadFile(
                  data.headerResult.imageBlob,
                  `companies/${companyId}/headerImage.jpeg`,
                )
              : Promise.resolve(null),
          logo:
            data.logoResult != null
              ? uploadFile(
                  data.logoResult.imageBlob,
                  `companies/${companyId}/logoImage.jpeg`,
                )
              : Promise.resolve(null),
        }

        await Promise.all(Object.values(imagePromises)).then(results => {
          const resultObj = Object.fromEntries(
            Object.keys(imagePromises).map((key, i) => [key, results[i]]),
          )
          console.log('results', resultObj)
          if (resultObj.header) headerUrl = resultObj.header
          if (resultObj.logo) logoUrl = resultObj.logo
        })
      } catch (e) {
        console.log('error', e)
      }

      if (headerUrl && headerUrl.trim() !== '')
        companyFormData.headerSavedUrl = headerUrl
      if (logoUrl && logoUrl.trim() !== '')
        companyFormData.logoSavedUrl = logoUrl

      const textImagesPromise = data.uploadedImages.map(
        async (imageUrl: string) => {
          const snapshots = await getDocs(
            query(
              collection(db, `companies/${companyId}/textImages`),
              where('url', '==', imageUrl),
            ),
          )
          if (snapshots.size === 1) await deleteDoc(snapshots.docs[0].ref)
        },
      )

      textImagesPromise.push(
        ...data.imagesToDelete.map(async (imageUrl: string) => {
          await deleteTextImage(imageUrl)
        }),
      )

      const resolvedImages = await Promise.all(textImagesPromise)
      return resolvedImages
    },

    updateDynamicLink(dynamicLink: string) {
      if (this.company) {
        this.company = {
          ...this.company,
          dynamicLink: dynamicLink,
        }
      }
    },

    resetStore() {
      if (unsubCompany != null) unsubCompany()
      this.company = null
    },
  },
  persist: true,
})
