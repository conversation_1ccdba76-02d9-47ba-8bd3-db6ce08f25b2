import type { ActiveChat } from '@/components/Chat/helpers/useChat'
import { useChatRoom } from '@/composables/ChatRoom/useChatRoom'
import { ChatRoom } from '@/gql/graphql'
import { useAuthStore } from '@/stores/authStore'
import type {
  ChatContactWithChat,
  ChatMessage,
} from '@/components/Chat/helpers/types'
import { useUsersStore } from '@/stores/usersStore'
import { useFilterStore } from '@/stores/fairs/filterStore'

interface State {
  chatsContacts: ChatContactWithChat[]
  activeChat: ActiveChat
  chatRoomDetails: ChatRoom
  showChatWindow: boolean
}

export const useChatStore = defineStore('chat', {
  state: (): State => ({
    chatsContacts: [],
    activeChat: null,
    showChatWindow: true,
    chatRoomDetails: {} as ChatRoom,
  }),

  actions: {
    async init() {
      const {
        state: { chatRoomDetails },
        actions: { fetchChatRoom },
      } = useChatRoom()

      //first clear active chat
      this.activeChat = null
      this.chatRoomDetails = {} as ChatRoom

      const authStore = useAuthStore()

      await fetchChatRoom()

      const companyUserId = authStore.claims.companyUserId as string

      const applicantId = this.activeChat?.contact.id

      const chatMessages = chatRoomDetails.value?.messages

      const matchMessages = chatMessages?.map(
        (message: {
          id: string
          content: string
          isCompany: boolean
          isApplicant: boolean
          isSent: boolean
          authorName: string
          isDelivered: boolean
          isSeen: boolean
          createdAt: string
          tempId: string
        }) => {
          return {
            id: message.id,
            senderId: message.isCompany ? companyUserId : applicantId,
            message: message.content,
            isCompany: message.isCompany,
            authorName: message.authorName,
            time: message.createdAt,
            isDelivered: message.isDelivered,
            isApplicant: message.isApplicant,
            feedback: {
              isSent: message.isSent,
              isDelivered: message.isDelivered,
              isSeen: message.isSeen,
            },
          }
        },
      )

      this.chatRoomDetails = chatRoomDetails.value
      // @ts-ignore
      if (this.activeChat && this.activeChat.chat) {
        this.activeChat.chat.messages = matchMessages
      }
    },
    setShowChatWindow(value: boolean) {
      this.showChatWindow = value
    },
    async fetchChatsAndContacts(q: string) {
      const { user } = useAuthStore()
    },
    clearActiveChat() {
      this.activeChat = null
    },

    async getChat(chatRoomDetails: ChatRoom) {
      const authStore = useAuthStore()

      const chatMessages = chatRoomDetails?.messages || []

      const applicant = chatRoomDetails?.jobAction?.applicant

      const matchMessages = chatMessages?.map(message => {
        return {
          id: message.id,
          senderId: message.isCompany
            ? authStore.claims.companyUserId
            : applicant?.id,
          message: message.content,
          authorName: message.authorName,
          isCompany: message.isCompany,
          time: message.createdAt,
          isApplicant: message.isApplicant,
          isSent: message.isSent,
          isDelivered: message.isDelivered,
          isSeen: message.isSeen,
        }
      })

      const res = {
        chat: {
          id: 2,
          userId: 1,
          unseenMsgs: 0,
          messages: [...matchMessages],
        },
        contact: {
          id: applicant?.id,
          fullName: applicant?.firstName + ' ' + applicant?.lastName,
          avatar: applicant?.profileImageUrl,
        },
      }
      // @ts-expect-error
      this.activeChat = res
    },

    async sendMsg(message: string) {
      const authStore = useAuthStore()
      const userStore = useUsersStore()
      const filterStore = useFilterStore()

      const companyUserId = authStore.claims.companyUserId as string

      const isSuper = authStore?.claims?.isSuperUser

      const companyFairContactPersonId = computed(() => {
        return filterStore.activeAppointment?.companyFairContactPersonId
      })

      const authorName = computed(() => {
        if (isSuper) return 'Admin'

        return companyFairContactPersonId.value
          ? filterStore?.activeAppointment?.contactPersonName
          : userStore?.activeUser?.name || authStore?.user?.displayName
      })

      const msg: ChatMessage = {
        message,
        time: new Date().toISOString(),
        senderId: companyUserId,
        tempId: Math.random().toString(36).substring(7),
        authorName: authorName.value,
        isCompany: true,
        isApplicant: false,
        isSent: true,
        isDelivered: false,
        isSeen: false,
      }

      this.activeChat?.chat?.messages.push(msg)
    },

    updateChatDetailsId(id: string) {
      this.chatRoomDetails.id = id
    },

    async updateChatFromLive(liveMessage: any) {
      const response = {
        msg: {
          message: liveMessage.content,
          time: liveMessage?.createdAt || new Date().toISOString(),
          id: liveMessage?.id,
          senderId: liveMessage?.applicantId || liveMessage?.companyUserId,
          authorName: liveMessage?.authorName,
          isCompany: liveMessage?.isCompany || false,
          isApplicant: liveMessage?.isApplicant || false,
          isSent: false,
          isDelivered: liveMessage?.isDelivered || false,
          isSeen: false,
          feedback: {
            isSent: false,
            isDelivered: false,
            isSeen: false,
          },
        },
      }

      const { msg }: { msg: ChatMessage } = response

      const existingMessageIndex = this.activeChat?.chat?.messages.findIndex(
        message =>
          message?.message === msg?.message &&
          message?.isCompany === true &&
          message.tempId,
      ) as number

      if (existingMessageIndex !== -1 && existingMessageIndex !== undefined) {
        if (this.activeChat && this.activeChat.chat) {
          this.activeChat.chat.messages[existingMessageIndex] = {
            ...this.activeChat.chat.messages[existingMessageIndex],
            tempId: '',
            ...msg,
          }
        }
      } else {
        this.activeChat?.chat?.messages.push(msg)
      }
    },
    resetStore() {
      this.chatsContacts = []
      this.activeChat = null
      this.showChatWindow = true
      this.chatRoomDetails = {} as ChatRoom
    },
  },
  persist: true,
})
