import { useJobAdvertGraph } from '@/api/graphHooks/useJobAdvertGraph'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { uuidv4 } from '@firebase/util'
import { uploadFile } from '@/libs/firebase/upload-file'
import AddJobAdApi from '@/types/job-ad/add-job-ad-api'
import { GeoPoint } from '@firebase/firestore'

export const useJobAdvertsStore = defineStore('jobAdvertsStore', () => {
  const jobAdState = useJobAdFormStore()

  const jobAdverts: never[] = []

  const saveJobAd = async (jobAd: AddJobAdApi) => {
    const authStore = useAuthStore()
    const companyStore = useCompanyStore()

    const route = useRoute()
    const cid = route.params.cid

    if (!authStore.companyId || !companyStore.company)
      throw new Error('Company not available')

    if (!jobAd.headerResult) throw new Error('Please provide a header image')

    const companyId = authStore.companyId || cid
    const imageId = uuidv4()
    const uploadPath = `companies/${companyId}/jobAds/${imageId}.jpeg`

    const downloadUrl = await uploadFile(
      jobAd.headerResult.imageBlob,
      uploadPath,
    )

    // @ts-ignore
    const jobAdPayload: AddJobAdApi = {
      jobAdType: 'ausbildung',
      title: jobAd.title, //good
      educationDuration: jobAd?.educationDuration, //good
      workHours: jobAd?.workHours.toString() || '',
      holidayDays: jobAd?.holidayDays.toString() || '', //good
      gehalt: jobAd?.gehalt,
      gehalt2: jobAd?.gehalt2,
      gehalt3: jobAd?.gehalt3,
      city: jobAd.city, //good
      address: jobAd.address, //good
      district: jobAd.district, //good
      companyId: '656749cdec170355b2611918', //TODO: change to companyId value from authStore when available
      //TODO: change to responsibleUsers value from authStore when available
      responsibleUsers: ['6567ca6aa3c64cfa770ebe0c'] || jobAd.selectedUsers,
      description: jobAd.description, //good
      detailDescription: jobAd.detailDescription, //good
      // imageUrl: companyStore.company?.logoImage, //good
      headerImage: downloadUrl, //good
      categories: jobAd.selectedCategories, //good
      coordinates: {
        //good
        latitude: jobAd.coordinates.latitude as GeoPoint['latitude'],
        longitude: jobAd.coordinates.longitude as GeoPoint['longitude'],
      },
      startDate: jobAd.startDate, //good
      activeFromDate: jobAd.activeFromDate,
    }
  }

  return {
    jobAdState,
    jobAdverts,
    saveJobAd,
  }
})
