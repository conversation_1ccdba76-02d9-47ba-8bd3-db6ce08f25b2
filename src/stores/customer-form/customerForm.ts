import { defineStore } from 'pinia'

export const useCustomerFormStore = defineStore('customerFormStore', {
  state: () => {
    return {
      id: null as string | null,
      name: '',
      email: '',
      addressLine1: '',
      addressLine2: '',
      country: 'Germany',
      city: '',
      postalCode: '',
    }
  },
  actions: {
    init(id: string) {},
    updateName(name: string) {
      this.name = name
    },
    updateEmail(email: string) {
      this.email = email
    },
    updateCustomerForm(data: any) {
      this.email = data?.email || ''
      this.name = data?.name || ''
      this.addressLine1 = data?.address?.line1 || ''
      this.addressLine2 = data?.address?.line2 || ''
      this.city = data?.address?.city || ''
      this.country = data?.address?.country || ''
      this.postalCode = data?.address?.postalCode || ''
    },
    resetStore() {
      this.id = null
      this.name = ''
      this.email = ''
      this.addressLine1 = ''
      this.addressLine2 = ''
      this.city = ''
      this.country = 'Germany'
      this.postalCode = ''
    },
  },
  getters: {
    getCustomerName(): string {
      return this.name
    },
    getCustomerEmail(): string {
      return this.email
    },
    isCustomerFormValid(): boolean {
      return (
        this.name?.length > 0 &&
        this.email?.length > 0 &&
        this.city?.length > 0 &&
        this.addressLine1?.length > 0 &&
        this.postalCode?.length > 0 &&
        this.country?.length > 0
      )
    },
  },
})
