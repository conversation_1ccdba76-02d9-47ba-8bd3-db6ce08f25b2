import { defineStore } from 'pinia'
import { GeoPoint } from '@firebase/firestore'
import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
import type ImageCropperResult from '@/types/image-cropper-result'
import type PlaceChanged from '@/types/place-changed'

export const useJobAdFormStore = defineStore('jobAdFormStore', {
  state: () => {
    return {
      id: null as string | null,
      title: '',
      jobAdType: 'ausbildung' as 'ausbildung' | 'praktikum',
      address: '',
      city: '',
      approved: false,
      isDeclined: false,
      isDraft: false,
      declineReason: '',
      coordinates: null as GeoPoint | null,
      district: '',
      workHours: null as number | null,
      holidayDays: null as number | null,
      educationDuration: null as number | null,
      gehalt: null as number | null,
      gehalt2: null as number | null,
      gehalt3: null as number | null,
      startDate: null as Date | null,
      activeFromDate: new Date(),
      selectedCategories: [] as string[],
      selectedUsers: [] as string[],
      originalSelectedCategories: [] as string[],
      originalSelectedUsers: [] as string[],
      staleImgIds: [] as string[],
      headerResult: null as ImageCropperResult | null,
      headerSavedUrl: null as string | null,
      description: '',
      detailDescription:
        '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihre Anforderungen auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ul><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ul>',
      uploadedImages: [] as string[],
      imagesToDelete: [] as string[],
    }
  },
  actions: {
    async init(jobAdvert: any) {
      // Check if jobAdvert and jobAdvert.value exist
      if (!jobAdvert || !jobAdvert.value) {
        return
      }

      try {
        this.id = jobAdvert.value?.id || null
        this.title = jobAdvert.value?.title || ''

        // Safely handle type conversion
        if (jobAdvert.value?.type && typeof jobAdvert.value.type === 'string') {
          this.jobAdType = jobAdvert.value.type.toLowerCase() as
            | 'ausbildung'
            | 'praktikum'
        } else {
          this.jobAdType = 'ausbildung'
        }

        this.address = jobAdvert.value?.address || ''
        this.city = jobAdvert.value?.city || ''

        // Handle coordinates safely
        if (
          jobAdvert.value?.latitude !== undefined &&
          jobAdvert.value?.longitude !== undefined
        ) {
          this.coordinates = new GeoPoint(
            jobAdvert.value.latitude,
            jobAdvert.value.longitude,
          )
        } else {
          this.coordinates = new GeoPoint(0, 0)
        }

        this.district = jobAdvert.value?.district || ''
        this.description = jobAdvert.value?.description || ''
        this.detailDescription = jobAdvert.value?.detailDescription || ''
        this.workHours = jobAdvert.value?.workHours || null
        this.approved = jobAdvert.value?.approved || false
        this.isDeclined = jobAdvert.value?.isDeclined || false
        this.isDraft = jobAdvert.value?.isDraft || false
        this.declineReason = jobAdvert.value?.declineReason || ''
        this.holidayDays = jobAdvert.value?.holidayDays || null
        this.educationDuration = jobAdvert.value?.educationDuration || null

        // Handle gehalt array safely
        if (Array.isArray(jobAdvert.value?.gehalt)) {
          this.gehalt = jobAdvert.value.gehalt[0] || null
          this.gehalt2 = jobAdvert.value.gehalt[1] || null
          this.gehalt3 = jobAdvert.value.gehalt[2] || null
        } else {
          this.gehalt = null
          this.gehalt2 = null
          this.gehalt3 = null
        }

        this.startDate = jobAdvert.value?.startDate || null
        this.activeFromDate = jobAdvert.value?.activeFromDate || new Date()
        this.headerSavedUrl = jobAdvert.value?.headerImageUrl || null
      } catch (error) {
        // Silent error handling
      }

      try {
        // Handle arrays safely
        if (Array.isArray(jobAdvert.value?.responsibleUsers)) {
          this.selectedUsers = jobAdvert.value.responsibleUsers
            .filter((user: any) => user && typeof user === 'object' && user.id)
            .map((user: { id: string }) => user.id)

          this.originalSelectedUsers = [...this.selectedUsers]
        } else {
          this.selectedUsers = []
          this.originalSelectedUsers = []
        }

        if (Array.isArray(jobAdvert.value?.categories)) {
          this.selectedCategories = jobAdvert.value.categories
            .filter(
              (category: any) =>
                category && typeof category === 'object' && category.id,
            )
            .map((category: { id: any }) => category.id)

          this.originalSelectedCategories = [...this.selectedCategories]
        } else {
          this.selectedCategories = []
          this.originalSelectedCategories = []
        }
      } catch (error) {
        // Silent error handling
        this.selectedUsers = []
        this.originalSelectedUsers = []
        this.selectedCategories = []
        this.originalSelectedCategories = []
      }
    },

    /**
     * Sets the city, address and geometry data from places autocomplete search
     * @param {PlaceChanged} result Result from autocomplete containing address, city and coordinates
     */
    placeChanged(result: PlaceChanged) {
      this.address = result.address
      this.city = result.city
      this.coordinates = new GeoPoint(
        result.coordinates.lat,
        result.coordinates.long,
      )
    },

    /**
     * Sets the given image
     * @param {Object} result image result
     * @param {Blob} result.resultBlob Blob of cropped image
     * @param {String} result.resultUrl Result URL of cropped image (gets invoked after submitting the form)
     */
    imageChanged(result: ImageCropperResult) {
      this.headerResult = result
    },
    setApprovedJobAd() {
      this.approved = true
      this.declineReason = ''
    },
    setDetailedDescription(desc: string) {
      this.detailDescription = desc
    },
    setDefaultUser(userId: string) {
      if (!userId) return
      this.selectedUsers.push(userId)
    },
    setBlockedJobAd() {
      this.isDeclined = true
      this.approved = false
    },
    resetStore() {
      this.id = null
      this.title = ''
      this.jobAdType = 'ausbildung'
      this.address = ''
      this.city = ''
      this.approved = false
      this.isDeclined = false
      this.isDraft = false
      this.declineReason = ''
      this.coordinates = null
      this.district = ''
      this.workHours = null
      this.holidayDays = null
      this.educationDuration = null
      this.gehalt = null
      this.gehalt2 = null
      this.gehalt3 = null
      this.startDate = null
      this.activeFromDate = new Date()
      this.selectedCategories = []
      this.selectedUsers = []
      this.originalSelectedCategories = []
      this.originalSelectedUsers = []
      this.staleImgIds = []
      this.headerResult = null
      this.headerSavedUrl = null
      this.description = ''
      this.detailDescription = '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihre Anforderungen auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ul><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ul>'
      this.uploadedImages = []
      this.imagesToDelete = []
    },
  },

  getters: {
    /**
     * Returns the job ad type
     * @returns {String} Job ad type
     */
    getAddress(): string {
      return this.address
    },
  },
})
