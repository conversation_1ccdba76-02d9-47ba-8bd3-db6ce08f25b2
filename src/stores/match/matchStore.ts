import type Applicant from '@/types/match/applicant'
import type { ChatMessage } from '@/types/match/chat-message'

export const useAppointmentStore = defineStore('appointmentStore', () => {
  let applicant = ref<Applicant>()

  const setMatchedApplicant = (matchedApplicant: Applicant) => {
    applicant.value = matchedApplicant
  }

  function reset() {
    useAppointmentStore().$reset()
  }

  return {
    applicant,
    reset,
  }
})
