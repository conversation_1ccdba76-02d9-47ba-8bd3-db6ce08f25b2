import { defineStore } from 'pinia'

interface CPTimeslot {
  id: string
  startTime: Date
  endTime: Date
  available: boolean
}

export const useContactPersonStore = defineStore('contactPerson', {
  state: () => ({
    showContactPersonForm: false,
    activeContactPersonTimeslots: [] as CPTimeslot[],
    activeContactPersonFairDays: [] as any[],
    companyFairContactPersonId: '',
    id: '',
    name: '',
    position: '',
    email: '',
    phone: '',
    companyId: '',
  }),

  actions: {
    toggleContactPersonForm() {
      this.showContactPersonForm = !this.showContactPersonForm
    },
    updateContactPersonForm(contactPerson: any) {
      this.id = contactPerson.id || ''
      this.name = contactPerson.name || ''
      this.position = contactPerson.position || ''
      this.email = contactPerson.email || ''
      this.phone = contactPerson.phone || ''
      this.companyId = contactPerson?.companyId || ''
    },
    setContactPersonName(name: string, id: string) {
      this.name = name
      this.companyFairContactPersonId = id
    },
    updateContactPersonTimeslots(timeslots: any, fairDays: any[]) {
      this.activeContactPersonTimeslots = timeslots
      this.activeContactPersonFairDays = fairDays
    },
    setContactPersonTimeslot(timeslot: any) {
      const index = this.activeContactPersonTimeslots.findIndex(
        (slot: any) => slot.id === timeslot.id,
      )
      if (index !== -1) {
        this.activeContactPersonTimeslots = [
          ...this.activeContactPersonTimeslots.slice(0, index),
          timeslot,
          ...this.activeContactPersonTimeslots.slice(index + 1),
        ]
      }
    },
    addContactPersonTimeslot(timeslot: any) {
      this.activeContactPersonTimeslots = [
        ...this.activeContactPersonTimeslots,
        timeslot,
      ]
    },

    removeContactPersonTimeslot(id: string) {
      this.activeContactPersonTimeslots =
        this.activeContactPersonTimeslots.filter((slot: any) => slot.id !== id)
    },
    resetForm() {
      this.id = ''
      this.name = ''
      this.position = ''
      this.email = ''
      this.phone = ''
      this.companyId = ''
    },
    resetStore() {
      this.showContactPersonForm = false
      this.activeContactPersonTimeslots = []
      this.activeContactPersonFairDays = []
      this.companyFairContactPersonId = ''
      this.resetForm()
    },
  },
})
