import { Company } from '@/gql/graphql'
import { defineStore } from 'pinia'
import { useCompanyStore } from '../companyStore'
import type PlaceChanged from '@/types/place-changed'
import type ImageCropperResult from '@/types/image-cropper-result'

export const useCompanyFormStore = defineStore('companyFormStore', {
  state: () => ({
    id: undefined as string | undefined,
    companyName: undefined as string | undefined,
    detailContent:
      '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihr Unternehmen beschreiben und wichtige Aspekte auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ol><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ol>',
    address: undefined as string | undefined,
    city: undefined as string | undefined,
    mitarbeiter: undefined as number | undefined,
    date: undefined as number | undefined,
    position: undefined as
      | {
          lat: number
          long: number
        }
      | undefined,
    headerResult: undefined as ImageCropperResult | undefined,
    logoResult: undefined as ImageCropperResult | undefined,
    avatarResult: undefined as ImageCropperResult | undefined,
    uploadedImages: [] as string[],
    imagesToDelete: [] as string[],
    staleImgIds: [] as string[],
    headerSavedUrl: undefined as string | undefined,
    logoSavedUrl: undefined as string | undefined,
    addressChanged: undefined as boolean | undefined,
    initialSetup: undefined as boolean | undefined,
  }),

  actions: {
    init(createNew: boolean, companyDetails: Company | null) {
      this.initialSetup = createNew

      if (!createNew && companyDetails) {
        try {
          const shouldInitialize =
            !this.id || !this.companyName || this.id !== companyDetails.id

          if (shouldInitialize) {
            this.id = companyDetails.id || ''
            this.companyName = companyDetails.name || ''
            this.detailContent = companyDetails?.detailContent || ''
            this.address = companyDetails.address || ''
            this.city = companyDetails.city || ''
            this.mitarbeiter = companyDetails.totalEmployees || 0
            this.date = companyDetails.foundingYear || 0
            this.position = {
              lat: companyDetails.latitude || 0,
              long: companyDetails.longitude || 0,
            }
            this.headerSavedUrl = companyDetails.headerImageUrl || ''
            this.logoSavedUrl = companyDetails.logoImageUrl || ''

            if (companyDetails.address) {
              this.placeChanged({
                address: companyDetails.address,
                city: companyDetails.city,
                coordinates: {
                  lat: companyDetails.latitude || 0,
                  long: companyDetails.longitude || 0,
                },
              })
            }
          }
        } catch (error) {
          // Silent error handling
        }
      }
    },

    /**
     * Sets the city, address and geometry data from places autocomplete search
     * @param {Object} result Result from autocomplete
     * @param {String} result.city City name
     * @param {String} result.address Company address
     * @param {Object} result.coordinates LatLng Coordinates of place
     * @param {Number} result.coordinates.lat Latitude
     * @param {Number} result.coordinates.lng Longitude
     */
    placeChanged(result: PlaceChanged) {
      this.address = result.address
      this.city = result.city
      this.position = {
        lat: result.coordinates.lat,
        long: result.coordinates.long,
      }
    },

    /**
     * Set cropped image result in state
     * @param {Object} result image result
     * @param {Blob} result.resultBlob Blob of cropped image
     * @param {String} result.resultUrl Result URL of cropped image (gets revoked after submitting the form)
     * @param {'profile'|'header'|'logo'} imageType
     */
    imageChanged(
      result: ImageCropperResult,
      imageType: 'profile' | 'header' | 'logo',
    ) {
      console.log('imageChanged::', result, imageType)
      switch (imageType) {
        case 'profile':
          this.avatarResult = result
          break
        case 'header':
          this.headerResult = result
          break
        case 'logo':
          this.logoResult = result
          break
      }
    },

    async saveImages(): Promise<boolean> {
      const companyStore = useCompanyStore()

      const res = await companyStore.uploadCompanyImages(
        {
          uploadedImages: this.uploadedImages,
          imagesToDelete: this.imagesToDelete,
          ...(this.headerResult != null && { headerResult: this.headerResult }),
          ...(this.logoResult != null && { logoResult: this.logoResult }),
          ...(this.initialSetup &&
            this.avatarResult != null && { avatarResult: this.avatarResult }),
        },
        this.initialSetup,
      )

      return true
    },

    resetStore() {
      this.id = undefined
      this.companyName = undefined
      this.detailContent =
        '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihr Unternehmen beschreiben und wichtige Aspekte auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ol><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ol>'
      this.address = undefined
      this.city = undefined
      this.mitarbeiter = undefined
      this.date = undefined
      this.position = undefined
      this.headerResult = undefined
      this.logoResult = undefined
      this.avatarResult = undefined
      this.uploadedImages = []
      this.imagesToDelete = []
      this.staleImgIds = []
      this.headerSavedUrl = undefined
      this.logoSavedUrl = undefined
      this.addressChanged = undefined
      this.initialSetup = undefined
    },
  },

  persist: {
    paths: [
      'id',
      'companyName',
      'detailContent',
      'address',
      'city',
      'mitarbeiter',
      'date',
      'position',
      'headerSavedUrl',
      'logoSavedUrl',
      'addressChanged',
      'initialSetup',
    ],
  },
})
