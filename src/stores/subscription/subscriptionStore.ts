import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
import {
  PaymentMethodResponseDto,
  StripeCouponResponseDto,
  StripeCustomerInput,
  StripePromoCodeResponseDto,
} from '@/gql/graphql'
import { defineStore, storeToRefs } from 'pinia'
import { useCompanyStore } from '@/stores/companyStore'
import { useCompanySubscriptionConfig } from '@/composables/CompanySubscriptionConfig/useCompanySubscriptionConfig'
import { useAuthStore } from '@/stores/authStore'
import { watch } from 'vue'

export const useSubscriptionStore = defineStore('subscriptionStore', {
  state: () => {
    return {
      paymentMethod: null as PaymentMethodResponseDto | null,
      customer: null as StripeCustomerInput | null,
      billingDetails: null as any,
      selectedMethod: null as string | null,
      jobAdverts: null as string[] | null,
      promoCode: null as string | null,
      couponDetails: null as StripeCouponResponseDto | null,
      promoCodeDetails: null as StripePromoCodeResponseDto | null,
      // Subscription config state
      subscriptionConfig: null as any,
      subscriptionConfigLoading: false,
      subscriptionConfigError: null as string | null,
      lastFetchedCompanyId: null as string | null,
    }
  },
  getters: {
    getPaymentMethod(): PaymentMethodResponseDto | null {
      return this.paymentMethod
    },
    getCustomer(): StripeCustomerInput | null {
      return this.customer
    },
    getJobAdverts(): string[] | null {
      return this.jobAdverts
    },
    getSelectedPaymentMethod(): any {
      return this.paymentMethod?.data?.find(
        method => method.id === this.selectedMethod,
      )
    },
    getCouponDetails(): StripeCouponResponseDto | null {
      return this.couponDetails
    },
    getPromoCodeDetails(): StripePromoCodeResponseDto | null {
      return this.promoCodeDetails
    },
    // Subscription config getters
    subscriptionType(): string | null {
      return this.subscriptionConfig?.subscriptionType || null
    },
    isCompanyUnlimited(): boolean {
      return this.subscriptionType === 'COMPANY_UNLIMITED'
    },
    isPerJobAdvert(): boolean {
      return this.subscriptionType === 'PER_JOB_ADVERT'
    },
    availablePricingPlans(): any[] {
      return this.subscriptionConfig?.availablePricingPlans || []
    },
  },
  actions: {
    async init() {
      const {
        state: { paymentMethods, stripeCustomer },
        actions: { refetchPaymentMethods },
      } = useStripeSubscription()

      const companyStore = useCompanyStore()
      const authStore = useAuthStore()

      // Set up watchers for company ID changes
      this.setupWatchers()

      // Fetch subscription config first if we have a company ID
      if (authStore.companyId) {
        await this.fetchSubscriptionConfig(true) // Force refresh on init
      }

      const customerId =
        companyStore?.getCompany?.stripeCustomerId ||
        companyStore?.company?.stripeCustomerId ||
        stripeCustomer.value?.id

      if (customerId) {
        await refetchPaymentMethods()

        if (paymentMethods.value) {
          // @ts-ignore
          this.paymentMethod = paymentMethods.value
        }
      }
    },
    setupWatchers() {
      const authStore = useAuthStore()
      const { companyId } = storeToRefs(authStore)

      // Watch for company ID changes and refetch subscription config
      watch(
        companyId,
        async (newCompanyId, oldCompanyId) => {
          if (newCompanyId && newCompanyId !== oldCompanyId) {
            // Company ID has changed, fetch new subscription config
            await this.fetchSubscriptionConfig(true)
          } else if (!newCompanyId && oldCompanyId) {
            // Company ID was cleared, reset subscription config
            this.subscriptionConfig = null
            this.lastFetchedCompanyId = null
          }
        },
        { immediate: false }
      )
    },
    updatePaymentMethod(paymentMethod: PaymentMethodResponseDto) {
      this.paymentMethod = paymentMethod
    },

    updateSelectedMethod(selectedMethod: string) {
      console.log({ selectedMethod })
      this.selectedMethod = selectedMethod
    },

    async updateJobAdverts(jobAdverts: string[]) {
      this.jobAdverts = jobAdverts
    },
    updateCouponDetails(couponDetails: StripeCouponResponseDto) {
      this.couponDetails = couponDetails
    },
    updatePromoCodeDetails(promoCodeDetails: StripePromoCodeResponseDto) {
      this.promoCodeDetails = promoCodeDetails
    },
    removePromoCode() {
      this.couponDetails = null
    },
    async fetchSubscriptionConfig(forceRefresh = false) {
      const authStore = useAuthStore()
      const companyId = authStore.companyId

      if (!companyId) {
        this.subscriptionConfigError = 'No company ID available'
        return null
      }

      // Skip if already fetched for this company and not forcing refresh
      if (
        !forceRefresh &&
        this.lastFetchedCompanyId === companyId &&
        this.subscriptionConfig
      ) {
        return this.subscriptionConfig
      }

      const {
        actions: { fetchConfigByCompanyId },
      } = useCompanySubscriptionConfig()

      this.subscriptionConfigLoading = true
      this.subscriptionConfigError = null

      try {
        const config = await fetchConfigByCompanyId(companyId)
        this.subscriptionConfig = config
        this.lastFetchedCompanyId = companyId
        return config
      } catch (err) {
        this.subscriptionConfigError =
          err instanceof Error
            ? err.message
            : 'Failed to fetch subscription config'
        console.error('Error fetching subscription config:', err)
        return null
      } finally {
        this.subscriptionConfigLoading = false
      }
    },
    resetStore() {
      this.paymentMethod = null
      this.customer = null
      this.billingDetails = null
      this.selectedMethod = null
      this.jobAdverts = null
      this.promoCode = null
      this.couponDetails = null
      this.promoCodeDetails = null
      this.subscriptionConfig = null
      this.subscriptionConfigLoading = false
      this.subscriptionConfigError = null
      this.lastFetchedCompanyId = null
    },
  },
  persist: {
    key: 'subscription-store',
    storage: localStorage,
    paths: [
      'paymentMethod',
      'customer',
      'billingDetails',
      'selectedMethod',
      'jobAdverts',
      'promoCode',
      'couponDetails',
      'promoCodeDetails',
      'subscriptionConfig',
      'lastFetchedCompanyId',
    ],
  },
})
