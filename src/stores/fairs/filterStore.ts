import { defineStore } from 'pinia'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

// Extend dayjs with UTC plugin
dayjs.extend(utc)

export const useFilterStore = defineStore('filterStore', {
  state: () => ({
    filters: {
      timeRange: {
        startTime: null as string | null,
        endTime: null as string | null,
      },
      applicantId: null as string | null,
      contactPersonId: null as string | null,
      companyId: null as string | null,
      fairId: null as string | null,
      status: null as string | null,
      date: null as Date | null,
    },
    filteredAppointments: [] as any[],
    totalAppointmentsCount: 0,
    activeAppointment: null as any | null,
    filtersHaveBeenApplied: false,
    isUpdatingAppointmentStatus: false,
  }),
  getters: {
    formattedTimeRange: state => {
      const { startTime, endTime } = state.filters.timeRange
      // if (!startTime && !endTime) return ''
      // const formattedStart = startTime ? format(startTime, 'HH:mm') : '00:00'
      // const formattedEnd = endTime ? format(endTime, 'HH:mm') : '00:00'
      return `${startTime} - ${endTime}`
      // return `${formattedStart} - ${formattedEnd}`
    },
    formattedDate: state => {
      return state.filters.date
        ? dayjs.utc(state.filters.date).format('DD.MM.YYYY')
        : ''
    },
    appliedFiltersCount: state => {
      let count = 0
      if (state.filters.timeRange.startTime || state.filters.timeRange.endTime)
        count++
      if (state.filters.date) count++
      if (state.filters.applicantId) count++
      if (state.filters.contactPersonId) count++
      if (state.filters.companyId) count++
      if (state.filters.fairId) count++
      if (state.filters.status) count++
      return count
    },
  },
  actions: {
    setFilteredAppointments(appointments: any, count: number) {
      this.filteredAppointments = appointments
      this.totalAppointmentsCount = count
    },
    setActiveAppointment(appointment: any) {
      console.log(appointment)
      this.activeAppointment = appointment
    },
    clearDateFilter() {
      this.filters.date = null
    },
    setAppointmentStatusLoading(loading: boolean) {
      this.isUpdatingAppointmentStatus = loading
    },
    updateAppointmentStatus(newStatus: string, rejectReason?: string) {
      if (this.activeAppointment) {
        this.activeAppointment = {
          ...this.activeAppointment,
          status: newStatus,
          rejectReason: rejectReason || null,
        }
      }
    },
    updateAppointmentRejectReason(rejectReason: string) {
      if (this.activeAppointment) {
        this.activeAppointment = {
          ...this.activeAppointment,
          rejectReason,
        }
      }
    },
    clearAppointmentRejectReason() {
      if (this.activeAppointment) {
        this.activeAppointment = {
          ...this.activeAppointment,
          rejectReason: null,
        }
      }
    },
    clearTimeRangeFilter() {
      this.filters.timeRange.startTime = null
      this.filters.timeRange.endTime = null
    },
    clearAllFilters() {
      this.filters = {
        timeRange: { startTime: null, endTime: null },
        applicantId: null,
        contactPersonId: null,
        companyId: null,
        fairId: null,
        status: null,
        date: null,
      }
      this.filtersHaveBeenApplied = false
    },
    setFiltersHaveBeenApplied() {
      this.filtersHaveBeenApplied = true
    },
    resetStore() {
      this.filters = {
        timeRange: { startTime: null, endTime: null },
        applicantId: null,
        contactPersonId: null,
        companyId: null,
        fairId: null,
        status: null,
        date: null,
      }
      this.filteredAppointments = []
      this.totalAppointmentsCount = 0
      this.activeAppointment = null
      this.filtersHaveBeenApplied = false
    },
    // Hydrate store from persisted data on app initialization
    hydrateStore() {
      // The persist plugin will automatically restore persisted data
      // This action can be called on app initialization to ensure store is ready
      console.log('Store hydrated with persisted data')
    },
  },
  persist: true,
})
