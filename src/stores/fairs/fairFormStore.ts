import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { Fair } from '@/gql/graphql'
import type ImageCropperResult from '@/types/image-cropper-result'
import { FairStatus } from '@/types/enums'

export const useFairFormStore = defineStore(
  'fairFormStore',
  () => {
    const fair = ref<Fair | null>(null)
    const name = ref<string>('')
    const id = ref<string>('')
    const description = ref<string>('')
    const city = ref<string>('')
    const location = ref<string>('')
    const locationName = ref<string>('')
    const status = ref<FairStatus>(FairStatus.Inactive)
    const startDate = ref<Date>()
    const endDate = ref<Date>()
    const registrationStartDate = ref<Date>()
    const registrationEndDate = ref<Date>()
    const contactPersonName = ref<string>('')
    const contactPersonEmail = ref<string>('')
    const logoResult = ref<ImageCropperResult | null>(null)
    const publisherLogoResult = ref<ImageCropperResult | null>(null)
    const logoSavedUrl = ref<string>('')
    const publisherLogoSavedUrl = ref<string>('')
    const publisherName = ref<string>('')
    const showContactPersonForm = ref(false)
    const showFairCloneList = ref(false)
    const fairDays = ref<
      { date: string; startTime: string; endTime: string }[]
    >([])

    const isValid = computed(() => {
      return !!(
        name.value &&
        city.value &&
        location.value &&
        startDate.value &&
        endDate.value
      )
    })

    const dateRange = computed(() => {
      if (!startDate.value || !endDate.value) return ''
      return `${startDate.value.toLocaleDateString()} - ${endDate.value.toLocaleDateString()}`
    })

    const toggleContactPersonForm = () => {
      showContactPersonForm.value = !showContactPersonForm.value
    }

    const toggleFairCloneList = () => {
      showFairCloneList.value = !showFairCloneList.value
    }
    const showCloneList = () => {
      showFairCloneList.value = true
    }

    const setFair = (fairData: Fair) => {
      fair.value = fairData
      id.value = fairData.id
      name.value = fairData.name
      description.value = fairData.description || ''
      city.value = fairData.city
      location.value = fairData.location
      locationName.value = fairData.locationName || ''
      status.value = fairData.status as unknown as FairStatus
      startDate.value = new Date(fairData.startDate)
      endDate.value = new Date(fairData.endDate)
      registrationStartDate.value = new Date(fairData.registrationStartDate)
      registrationEndDate.value = new Date(fairData.registrationEndDate)
      contactPersonName.value = fairData.contactPersonName || ''
      contactPersonEmail.value = fairData.contactPersonEmail || ''
      logoSavedUrl.value = fairData.logoImageUrl || ''
      publisherLogoSavedUrl.value = fairData.publisherLogoImageUrl || ''
      logoResult.value = null
      publisherLogoResult.value = null
      publisherName.value = fairData.publisherName || ''
      fairDays.value =
        fairData?.fairDays?.map((fairDay: any) => ({
          date: fairDay.day,
          startTime: fairDay.startTime,
          endTime: fairDay.endTime,
        })) || []
    }

    const updateFairDays = (newFairDays: any) => {
      fairDays.value = newFairDays
    }

    const handleImageChange = (
      result: ImageCropperResult,
      type: 'logo' | 'publisherLogo',
    ) => {
      if (type === 'logo') {
        logoResult.value = result
      } else {
        publisherLogoResult.value = result
      }
    }

    const resetForm = () => {
      fair.value = null
      name.value = ''
      description.value = ''
      id.value = ''
      city.value = ''
      location.value = ''
      locationName.value = ''
      status.value = FairStatus.Inactive
      startDate.value = undefined
      endDate.value = undefined
      registrationStartDate.value = undefined
      registrationEndDate.value = undefined
      contactPersonName.value = ''
      contactPersonEmail.value = ''
      logoResult.value = null
      publisherLogoResult.value = null
      logoSavedUrl.value = ''
      publisherLogoSavedUrl.value = ''
      publisherName.value = ''
      fairDays.value = []
    }

    return {
      fair,
      name,
      description,
      city,
      id,
      location,
      locationName,
      status,
      startDate,
      endDate,
      registrationStartDate,
      registrationEndDate,
      contactPersonName,
      contactPersonEmail,
      logoResult,
      publisherLogoResult,
      logoSavedUrl,
      publisherLogoSavedUrl,
      publisherName,
      isValid,
      dateRange,
      showContactPersonForm,
      showFairCloneList,
      fairDays,
      showCloneList,
      updateFairDays,
      setFair,
      handleImageChange,
      toggleContactPersonForm,
      toggleFairCloneList,
      resetForm,
    }
  },
  {
    persist: true,
  },
)
