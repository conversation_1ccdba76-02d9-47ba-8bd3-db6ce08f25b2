import { defineStore } from 'pinia'
import { uploadFile } from '@/libs/firebase/upload-file'
import { Fair } from '@/gql/graphql'
import { useFairFormStore } from '@/stores/fairs/fairFormStore'
import { uuidv4 } from '@firebase/util'

export const useFairStore = defineStore('fairStore', {
  state: () => {
    return {
      fair: null as Fair | null,
      showFairForm: false,
      showFairJobForm: false,
      showCompanyList: false,
    }
  },

  getters: {
    getFair(): Fair | null {
      return this.fair
    },
  },

  actions: {
    async init() {},

    toggleFairForm() {
      this.showFairForm = !this.showFairForm
    },

    openFairForm() {
      this.showFairForm = true
    },

    toggleFairJobForm() {
      this.showFairJobForm = !this.showFairJobForm
    },

    toggleCompanyList() {
      this.showCompanyList = !this.showCompanyList
    },

    updateFair(fair: Fair) {
      this.fair = fair
    },

    resetFair() {
      this.fair = null
    },

    async uploadFairImages(data: any, createNew = false) {
      const fairFormData = useFairFormStore()
      const randomId = uuidv4()

      const route = useRoute()

      const fairId = computed(() => {
        return (
          this.getFair?.id ||
          (route && route.params ? route.params.fid : null) ||
          randomId
        )
      }).value

      let logoUrl: string = ''
      let publisherLogoUrl: string = ''

      try {
        const imagePromises = {
          logo:
            data.logoResult != null
              ? uploadFile(
                  data.logoResult.imageBlob,
                  `fairs/${fairId}/logoImage.jpeg`,
                )
              : Promise.resolve(null),
          publisherLogo:
            data.publisherLogoResult != null
              ? uploadFile(
                  data.publisherLogoResult.imageBlob,
                  `fairs/${fairId}/publisherLogoImage.jpeg`,
                )
              : Promise.resolve(null),
        }

        await Promise.all(Object.values(imagePromises)).then(results => {
          const resultObj = Object.fromEntries(
            Object.keys(imagePromises).map((key, i) => [key, results[i]]),
          )
          if (resultObj.logo) logoUrl = resultObj.logo
          if (resultObj.publisherLogo)
            publisherLogoUrl = resultObj.publisherLogo
        })
      } catch (e) {
        console.error('Error uploading images:', e)
      }

      if (logoUrl && logoUrl.trim() !== '') fairFormData.logoSavedUrl = logoUrl
      if (publisherLogoUrl && publisherLogoUrl.trim() !== '')
        fairFormData.publisherLogoSavedUrl = publisherLogoUrl

      return {
        logoUrl,
        publisherLogoUrl,
      }
    },

    resetStore() {
      this.fair = null
      this.showFairForm = false
    },
  },
  persist: true,
})
