import { useChatStore } from '@/stores/apiStores/useChatStore'
import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
import { useAppStore } from './appStore'
import { useAuthStore } from './authStore'
import { useCompanyStore } from './companyStore'
import { useJobAdsStore } from './jobAdsStore'
import { useUsersStore } from './usersStore'
import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
import { useFilterStore } from '@/stores/fairs/filterStore'

function useStores() {
  const stores = [
    useJobAdsStore,
    useUsersStore,
    useSubscriptionStore,
    useCompanyStore,
    useCompanyFormStore,
    useChatStore,
    useCustomerFormStore,
    useContactPersonStore,
    useFilterStore,
    useJobAdFormStore,
  ]

  return stores.map(useStore => useStore())
}

/**
 * Initializes all stores, that need to be loaded before application renders first time
 */
export const initStores = async () => {
  const appStore = useAppStore()
  if (appStore.initialized || appStore.initializing) return

  const authStore = useAuthStore()

  appStore.initializing = true

  if (authStore.user != null && authStore.companyId) {
    const stores = useStores()

    await Promise.all(
      stores.map(async store => {
        if (typeof store.init === 'function') {
          await store.init()
        }
        return true
      }),
    )
    appStore.initialized = true
  }

  appStore.initializing = false
}

/**
 * Resets all stores that have been initialized with initStores.
 * All stores that need to be resetted, shpuld have a method 'resetStore' that handles unsub listeners and resetting the store.
 */
export const resetStores = async () => {
  const stores = useStores()
  const appStore = useAppStore()

  if (appStore.initialized !== true) return

  await Promise.all(stores.map(store => store.resetStore?.()))
}
