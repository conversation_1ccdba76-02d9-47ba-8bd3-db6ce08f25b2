import { defineStore } from 'pinia'
import { useApplicantGraph } from '@/api/graphHooks/useApplicantGraph'
import { Applicant } from '@/gql/graphql'

export const useApplicantStore = defineStore('applicant', {
  state: () => ({
    applicant: {} as Applicant,
    loadingApplicant: false,
    applicantError: null as Error | null,
    isApplicantLoaded: false
  }),

  actions: {
    async loadApplicant() {
      // If already loaded, don't fetch again
      if (this.isApplicantLoaded && Object.keys(this.applicant).length > 0) {
        console.log('Applicant already loaded, using stored data:', this.applicant)
        return
      }

      console.log('Loading applicant data from store action')
      this.loadingApplicant = true
      this.applicantError = null

      try {
        const {
          actions: { loadApplicantById },
          state: { applicant }
        } = useApplicantGraph()

        await loadApplicantById()

        // Wait a moment to ensure the data is available
        await new Promise(resolve => setTimeout(resolve, 100))

        if (applicant.value) {
          this.applicant = applicant.value
          this.isApplicantLoaded = true
          console.log('Applicant data loaded into store:', this.applicant)
        } else {
          console.error('Failed to load applicant data, value is empty')
          this.applicantError = new Error('Failed to load applicant data')
        }
      } catch (error) {
        console.error('Error loading applicant:', error)
        this.applicantError = error instanceof Error ? error : new Error('Unknown error loading applicant')
      } finally {
        this.loadingApplicant = false
      }
    },

    resetApplicantData() {
      this.applicant = {} as Applicant
      this.isApplicantLoaded = false
      this.loadingApplicant = false
      this.applicantError = null
    }
  }
})
