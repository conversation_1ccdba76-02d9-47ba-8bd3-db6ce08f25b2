import { useFairStatsGraph } from '@/api/graphHooks/useFairStatsGraph'
import fairs from '@images/svg/rocket.svg'
import companies from '@images/svg/matches.svg'
import appointments from '@images/svg/applicants.svg'
import chatRooms from '@images/svg/impressions.svg'
import { inlineTranslate } from '@/utils/utils'

export const useFairStats = () => {
  const {
    state: { fairStats, loadingFairStats },
    actions: { refetchFairStats },
  } = useFairStatsGraph()

  // Create statistics array for the component
  const statistics = computed(() => [
    {
      title: inlineTranslate('Fairs'),
      icon: fairs,
      stats: fairStats.value?.fairs || 0,
    },
    {
      title: inlineTranslate('Companies'),
      icon: companies,
      stats: fairStats.value?.companies || 0,
    },
    {
      title: inlineTranslate('Appointments'),
      icon: appointments,
      stats: fairStats.value?.appointments || 0,
    },
    {
      title: inlineTranslate('Chatrooms'),
      icon: chatRooms,
      stats: fairStats.value?.chatRooms || 0,
    },
  ])

  // Load fair stats on mount
  onMounted(async () => {
    try {
      await refetchFairStats()
    } catch (error) {
      console.error('Error loading fair statistics:', error)
    }
  })

  return {
    state: {
      statistics,
      loadingFairStats,
    },
    actions: {
      refetchFairStats,
    },
  }
}
