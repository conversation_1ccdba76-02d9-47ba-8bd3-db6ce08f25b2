import {
  confirmJobAdApproval,
  confirmJobAdBlock,
  jobAdBlockedAlert,
} from '@/composables/useSweetAlert'
import { useAppStore } from '@/stores/appStore'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
import { inlineTranslate } from '@/utils/utils'
import { useJobAdvertMutationsGraph } from '@/api/graphHooks/jobAdverts/useJobAdvertMutationsGraph'
import { useJobAdvertById } from '@/api/graphHooks/jobAdverts/useJobAdvertById'

export const useJobAdvertForm = () => {
  const {
    actions: { approveJobAd, blockJobAd },
  } = useJobAdvertMutationsGraph()

  const {
    state: { jobAdvert, loadingSingleJobAdvert },
    actions: { refetchSingleJobAd },
  } = useJobAdvertById()

  const jobAdStore = useJobAdFormStore()
  const appStore = useAppStore()

  const loadingApprove = ref(false)
  const loadingBlock = ref(false)

  const jobAdTypeOptions = [
    { label: 'Ausbildung', value: 0 },
    { label: 'Praktikum', value: 1 },
  ]

  const jobAdType = computed({
    get() {
      return jobAdTypeOptions.find(
        item => item.label.toLowerCase() === jobAdStore.jobAdType,
      )?.value
    },
    set(type) {
      jobAdStore.jobAdType = jobAdTypeOptions
        .find(item => item.value === type)
        ?.label.toLowerCase() as 'ausbildung' | 'praktikum'
    },
  })

  const handleApproveJobAd = async (jobAdId: string, dark: boolean) => {
    const confirmApprove = await confirmJobAdApproval(dark)
    if (confirmApprove.isConfirmed) {
      appStore.showAppLoader()
      loadingApprove.value = true

      try {
        await approveJobAd({
          jobAdId,
        })
        await refetchSingleJobAd({
          jobAdvertId: jobAdId,
        })
        jobAdStore.setApprovedJobAd()
      } catch (error) {
        console.error('Error approving job ad:', error)
      } finally {
        appStore.hideAppLoader()
        loadingApprove.value = false
      }
    }
  }

  const handleBlockJobAd = async (jobAdId: string, dark: boolean) => {
    const confirmBlock = await confirmJobAdBlock(dark)

    if (confirmBlock.isConfirmed) {
      appStore.showAppLoader()
      loadingBlock.value = true
      try {
        await blockJobAd({
          jobAdId,
          declineReason: confirmBlock.value,
        })
        await refetchSingleJobAd({
          jobAdvertId: jobAdId,
        })
        jobAdStore.setBlockedJobAd()
      } catch (error) {
        console.error('Error blocking job ad:', error)
      } finally {
        appStore.hideAppLoader()
        jobAdBlockedAlert(dark)
        loadingBlock.value = false
      }
    }
  }

  const jobAdText = computed(() => ({
    jobType: inlineTranslate('Job Type'),
    jobTitle: inlineTranslate('Job Title'),
    internshipTitle: inlineTranslate('Internship Title'),
    companyAddress: inlineTranslate('Company Address'),
    regionTraining: inlineTranslate('Region Training'),
    regionInternship: inlineTranslate('Region Internship'),
    categoryTraining: inlineTranslate('Category Training'),
    categoryInternship: inlineTranslate('Category Internship'),
    hoursWeek: inlineTranslate('Hours / Week'),
    hours: inlineTranslate('Hours'),
    days: inlineTranslate('Days'),
    years: inlineTranslate('Years'),
    holidaysYear: inlineTranslate('Holidays / Year'),
    trainingDuration: inlineTranslate('Training Duration'),
    salary1Year: inlineTranslate('Salary 1st Year'),
    salary2Year: inlineTranslate('Salary 2nd Year'),
    salary3Year: inlineTranslate('Salary 3rd Year'),
    year2Optional: inlineTranslate('2nd Year (optional)'),
    year3Optional: inlineTranslate('3rd Year (optional)'),
    startOfTraining: inlineTranslate('Start of training'),
    adActiveFrom: inlineTranslate('Ad Active From'),
    whoShouldHaveAccess: inlineTranslate('Who should have access?'),
    titlePicture: inlineTranslate('Title Picture'),
    selectBanner: inlineTranslate('Select Banner'),
    availableFrom: inlineTranslate('Available From'),
    banner: inlineTranslate('Banner'),
    shortDescriptionTraining: inlineTranslate('Short Description Training'),
    briefDescriptionInternship: inlineTranslate('Brief Description Internship'),
    saveChanges: inlineTranslate('Save Changes'),
    approveJobAd: inlineTranslate('Approve Job Ad'),
    blockJobAd: inlineTranslate('Block Job Ad'),
    savePremium: inlineTranslate('Save Premium'),
    saveBasic: inlineTranslate('Save Basic'),
    saveDraft: inlineTranslate('Save Draft'),
    createJobAdvertisement: inlineTranslate('Create Job Advertisement'),
    createNewTraining: inlineTranslate('Create New Training'),
    editNewTraining: inlineTranslate('Edit New Training'),
    createNewInternship: inlineTranslate('Create New Internship'),
    editNewInternship: inlineTranslate('Edit New Internship'),
    createJobInfo: inlineTranslate('Create Job Info'),
    editJobInfo: inlineTranslate('Edit Job Info'),
    descriptionPlaceholder: inlineTranslate('Description Placeholder'),
    previewJobAd: inlineTranslate('Preview Job Ad Header'),
    previewJobAdInfo: inlineTranslate('Preview Job Ad Info'),
    trainingLocation: inlineTranslate('Training Location'),
    applicantsOverview: inlineTranslate('Overview Of Applicants'),
    statisticsOn: inlineTranslate('Statistics On'),
    realTimeData: inlineTranslate('Real Time Data'),
  }))

  return {
    state: {
      jobAdvert,
      jobAdText,
      jobAdTypeOptions,
      jobAdType,
      loadingSingleJobAdvert,
      loadingApprove,
      loadingBlock,
    },
    actions: {
      handleApproveJobAd,
      handleBlockJobAd,
      refetchSingleJobAd,
    },
  }
}
