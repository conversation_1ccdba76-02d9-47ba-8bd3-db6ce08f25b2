import { useJobActions } from '@/composables/JobActions/useJobActions'
import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
import { usePusher } from '@/composables/usePusher'
import { useJobAdsStore } from '@/stores/jobAdsStore'
import LikeMatch from '@/types/like-match'
import { useRoute, useRouter } from 'vue-router'
import {
  applicationRedrawnNotice,
  basicAdvertNotice,
} from '@/composables/useSweetAlert'
import { computed } from 'vue'
import impressions from '@images/svg/impressions.svg'
import likes from '@images/svg/likes.svg'
import applicants from '@images/svg/applicants.svg'
import matches from '@images/svg/matches.svg'
import { inlineTranslate } from '@/utils/utils'
import { useAuthStore } from '@/stores/authStore'
import { useJobAdvertById } from '@/api/graphHooks/jobAdverts/useJobAdvertById'
import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'

export const useJobLikes = () => {
  const route = useRoute()
  const router = useRouter()
  const jobAdvertId = route.params?.id as string

  const {
    actions: { refetchSingleJobAd },
    state: { jobAdvert, loadingSingleJobAdvert: loadingJobAdvert },
  } = useJobAdvertById({ jobAdvertId: jobAdvertId })

  const {
    actions: { handleDeclineMatchedApplicant },
  } = useJobActions()

  const {
    actions: { updateJobAdvertInCache },
  } = useJobAdverts()

  const jobAdsStore = useJobAdsStore()
  const authStore = useAuthStore()
  const subscriptionStore = useSubscriptionStore()

  const refetchLikes = () => {
    refetchSingleJobAd()
  }

  const jobAdSubscriptions = computed(
    () => jobAdvert.value?.subscriptions ?? [],
  )

  const isPremium = computed(() => {
    if (subscriptionStore.isCompanyUnlimited) return true
    return jobAdSubscriptions.value.some(
      (subscription: { plan: string }) => subscription.plan === 'PREMIUM',
    )
  })

  const jobAdId = computed(() => jobAdvert.value?.id ?? '')
  const jobAdvertTitle = computed(() =>
    authStore.isSuperUser ? '' : jobAdvert.value?.title ?? '',
  )
  const jobAdvertApproved = computed(() => jobAdvert.value?.approved ?? false)
  const jobAdvertDeclined = computed(() => jobAdvert.value?.isDeclined ?? false)
  const jobAdvertDeclineReason = computed(
    () => jobAdvert.value?.declineReason ?? '',
  )
  const subscriptionPlan = computed(() => jobAdvert.value?.subscription ?? '')

  const loadingDeclineAction = ref(false)

  const { listen, unsubscribe } = usePusher()

  let cleanup: (() => void) | null = null

  const initiateJobLikesListen = async (jobAdId: string) => {
    if (!jobAdId || authStore.isSuperUser) return

    try {
      const privateChannelJobLikes = `private-liveJobAdvertActions.${jobAdId}`

      cleanup = await listen(privateChannelJobLikes, {
        event: 'jobActionUpdated',
        callback: async (data: any) => {
          try {
            await updateJobLikeInCache(data, jobAdId)
          } catch (error) {
            console.error('Error updating cache:', error)
          }
        },
      })
    } catch (error) {
      console.error('Error setting up listener:', error)
    }
  }

  const stopJobLikesListen = (jobAdId: string) => {
    if (cleanup) {
      cleanup()
      cleanup = null
      const privateChannelJobLikes = `private-liveJobAdvertActions.${jobAdId}`
      unsubscribe(privateChannelJobLikes)
    }
  }

  const updateJobLikeInCache = async (jobAction: any, jobAdvertId: string) => {
    await jobAdsStore.pushToJobLikes(jobAction, jobAdvertId)
    const data = { id: jobAdvertId, ...jobAction }
    await updateJobAdvertInCache(data, jobAdvertId)
  }

  interface JobStats {
    likes: number
    bookmarks: number
    impressions: number
    matches: number
  }

  const calculateJobAdvertStats = (jobActions: any[]): JobStats => {
    const likes = jobActions.filter(action => action.state === 'LIKED').length

    const bookmarks = jobActions.filter(
      action => action.state === 'BOOKMARKED',
    ).length

    const impressions = jobActions.length

    const matches = jobActions.filter(
      action => action.state === 'MATCHED' && !action.deletedFromApplicant,
    ).length

    return {
      likes,
      bookmarks,
      impressions,
      matches,
    }
  }

  const comAdvertStats = computed(() => {
    return calculateJobAdvertStats(jobAdsStore.jobAdLikes[jobAdvertId] ?? [])
  })

  const advertStatistics = computed(() => [
    {
      title: inlineTranslate('Impressions'),
      icon: impressions,
      stats: comAdvertStats.value?.impressions,
    },
    {
      title: 'Bookmarks',
      icon: likes,
      stats: comAdvertStats.value?.bookmarks,
    },
    {
      title: inlineTranslate('Applicant'),
      icon: applicants,
      stats: comAdvertStats.value?.likes,
    },
    {
      title: 'Matches',
      icon: matches,
      stats: comAdvertStats.value?.matches,
    },
  ])

  const handleRowClicked = async (like: LikeMatch, dark = true) => {
    if (!isPremium.value) {
      await basicAdvertNotice(dark)
      return false
    }

    if (like.deletedFromApplicant) {
      const result = await applicationRedrawnNotice(dark)
      if (result.isConfirmed) {
        loadingDeclineAction.value = true
        await handleDeclineMatchedApplicant(like.id, '')
        loadingDeclineAction.value = false
      }
      return false
    } else {
      await router.push({
        name: 'company-job-ads-id-applicants-uid',
        params: {
          id: route.params.id,
          uid: like.applicantId,
        },
      })
    }
  }

  return {
    state: {
      jobAdId,
      advertStatistics,
      jobAdvertTitle,
      isPremium,
      subscriptionPlan,
      loadingJobAdvert,
      jobAdvertApproved,
      jobAdvertDeclined,
      jobAdvertDeclineReason,
      loadingDeclineAction,
    },
    actions: {
      handleRowClicked,
      refetchLikes,
      initiateJobLikesListen,
      stopJobLikesListen,
    },
  }
}
