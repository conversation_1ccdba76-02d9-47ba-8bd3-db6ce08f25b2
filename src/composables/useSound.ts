import { ref } from 'vue'
import { Howl, HowlOptions } from 'howler'
import sendMsgSound from '../assets/sounds/SPNZ_crypto_generic_button.mp3'
import receiveMsgSound from '../assets/sounds/SPNZ_crypto_keno_tile_select.mp3'
import notificationSound from '../assets/sounds/notificationSound.wav'

type SoundName = 'sendMsg' | 'recMsg' | 'notification'

interface Sounds {
  [key: string]: Howl
}

const createSound = (src: string, options?: HowlOptions): Howl =>
  new Howl({ src, ...options })

export const useSound = () => {
  const sounds: Sounds = ref({
    sendMsg: createSound(sendMsgSound),
    recMsg: createSound(receiveMsgSound),
    notification: createSound(notificationSound),
  }).value

  const play = (soundName: SoundName): void => {
    if (sounds[soundName]) {
      sounds[soundName].play()
    }
  }

  return { play }
}
