import { useContactPersonGraph } from '@/api/graphHooks/useContactPersonGraph'
import { CreateContactPersonInput } from '@/gql/graphql'
import { useRouter } from 'vue-router'
import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

export const useContactPersons = () => {
  const {
    state: { companyContactPersonsInFair, loadingContactPersonsAndJobsInFair },
  } = useCompanyContactPersonGraphData()

  const router = useRouter()

  const cid = router.currentRoute.value.params.cid as string

  const contactPersonStore = useContactPersonStore()

  const {
    state: {
      contactPersonsByCompanyId,
      loadingContactPersonByCompanyId,
      loadingCreate<PERSON>ontactPerson,
      loadingRemove<PERSON><PERSON><PERSON><PERSON>erson,
      loadingUpdate<PERSON>ontact<PERSON>erson,
    },
    actions: {
      create<PERSON><PERSON><PERSON><PERSON><PERSON>,
      remove<PERSON>ontactPerson,
      update<PERSON>ontactPerson,
      refetchContactPersonByCompanyId,
      loadContactPersonByCompanyId,
    },
  } = useContactPersonGraph()

  const companyContactPersonsInFairList = computed(() => {
    return companyContactPersonsInFair.value?.map((contactPerson: any) => ({
      contactPersonName: contactPerson?.contactPerson.name,
      contactPersonEmail: contactPerson?.contactPerson.email,
      contactPersonPhone: contactPerson?.contactPerson.phone,
      id: contactPerson?.contactPerson.id,
      companyFairContactPersonId: contactPerson?.id,
      contactPersonTimeslots: contactPerson?.ContactPersonTimeslot,
      fairDays: contactPerson?.fairDays,
    }))
  })

  const contactPersonsByCompanyIdList = computed(() => {
    return contactPersonsByCompanyId.value
      .map((contactPerson: any) => ({
        name: contactPerson?.name,
        email: contactPerson?.email,
        position: contactPerson?.position,
        phone: contactPerson?.phone,
        id: contactPerson?.id,
        isInCompany: companyContactPersonsInFairList.value?.some(
          (companyContactPerson: any) =>
            companyContactPerson.id === contactPerson?.id,
        ),
      }))
      .sort((a: any, b: any) => Number(a.isInCompany) - Number(b.isInCompany))
  })

  const handleCreateContactPerson = async () => {
    try {
      const input = {
        name: contactPersonStore.name,
        position: contactPersonStore.position,
        email: contactPersonStore.email,
        phone: contactPersonStore.phone,
      } as CreateContactPersonInput
      const response = await createContactPerson({
        input: { ...input, companyId: cid },
      })
      await Promise.all([refetchContactPersonByCompanyId({ companyId: cid })])
      return response?.data.createContactPerson
    } catch (error) {
      console.error('Error creating contact person:', error)
      throw error
    }
  }

  const handleUpdateContactPerson = async () => {
    try {
      const input = {
        id: contactPersonStore.id,
        name: contactPersonStore.name,
        position: contactPersonStore.position,
        email: contactPersonStore.email,
        phone: contactPersonStore.phone,
      }
      const response = await updateContactPerson({
        input: {
          ...input,
        },
      })
      await Promise.all([refetchContactPersonByCompanyId({ companyId: cid })])
      return response?.data.updateContactPerson
    } catch (error) {
      console.error('Error updating contact person:', error)
      throw error
    }
  }

  const handleRemoveContactPerson = async () => {
    try {
      console.log('contactPersonStore.id', contactPersonStore.id)
      const id = contactPersonStore.id
      const response = await removeContactPerson({
        id: id,
      })
      await Promise.all([refetchContactPersonByCompanyId({ companyId: cid })])
      return response?.data.removeContactPerson
    } catch (error) {
      console.error('Error removing contact person:', error)
      throw error
    }
  }

  return {
    state: {
      companyContactPersonsInFairList,
      contactPersonsByCompanyIdList,
      loadingContactPersonByCompanyId,
      loadingCreateContactPerson,
      loadingUpdateContactPerson,
      loadingRemoveContactPerson,
      loadingContactPersons: loadingContactPersonsAndJobsInFair,
    },
    actions: {
      handleCreateContactPerson,
      handleUpdateContactPerson,
      handleRemoveContactPerson,
      loadContactPersonByCompanyId,
    },
  }
}
