import { useRouter } from 'vue-router'
import { useCompanyStore } from '@/stores/companyStore'
import { useAppStore } from '@/stores/appStore'
import { computed } from 'vue'

export const useSubscriptionGuard = () => {
  const router = useRouter()
  const companyStore = useCompanyStore()
  const appStore = useAppStore()

  const isCompanySubscriptionActive = computed(() => {
    const company = companyStore.getCompany
    if (!company) return false

    // Check if company has an active subscription
    return (
      company.subscriptionStatus === 'ACTIVE' ||
      company.subscriptionStatus === 'TRIAL'
    )
  })

  const hasRemainingJobAdverts = computed(() => {
    const company = companyStore.getCompany
    if (!company) return false

    // For company-wide subscriptions, check if they have remaining job adverts
    if (company.subscriptionType === 'COMPANY') {
      return (
        company.remainingJobAdverts > 0 || company.totalJobAdvertsLimit === -1
      ) // -1 means unlimited
    }

    // For per-job subscriptions, they can always create (will go through payment)
    return true
  })

  const checkSubscriptionForJobAdvert = async (): Promise<boolean> => {
    const company = companyStore.getCompany

    if (!company) {
      appStore.showSnack('Bitte wählen Sie zuerst ein Unternehmen aus')
      return false
    }

    // If company uses company-wide subscription
    if (company.subscriptionType === 'COMPANY') {
      // Check if subscription is active
      if (!isCompanySubscriptionActive.value) {
        appStore.showSnack(
          'Sie benötigen ein aktives Abonnement, um Stellenanzeigen zu erstellen',
        )

        // Redirect to subscription page
        await router.push({
          name: 'company-subscription-plans',
          query: { returnTo: 'new-job-ad' },
        })
        return false
      }

      // Check if they have remaining job adverts
      if (!hasRemainingJobAdverts.value) {
        appStore.showSnack(
          'Sie haben Ihr Limit für Stellenanzeigen erreicht. Bitte upgraden Sie Ihr Abonnement.',
        )

        await router.push({
          name: 'company-subscription',
        })
        return false
      }
    }

    // For PER_JOB subscriptions, allow creation (payment will be per job)
    return true
  }

  const checkSubscriptionAfterCompanyCreation = async (companyId: string) => {
    const company = companyStore.getCompany

    // If the company uses company-wide subscription and doesn't have an active one
    if (
      company?.subscriptionType === 'COMPANY' &&
      !isCompanySubscriptionActive.value
    ) {
      appStore.showSnack(
        'Bitte wählen Sie einen Abonnementplan für Ihr Unternehmen',
      )

      // Redirect to subscription page with return parameter
      await router.push({
        name: 'company-subscription-plans',
        query: {
          companyId,
          returnTo: 'company',
          isNewCompany: 'true',
        },
      })
      return false
    }

    return true
  }

  const handleSubscriptionReturn = () => {
    const route = router.currentRoute.value
    const returnTo = route.query.returnTo as string
    const isNewCompany = route.query.isNewCompany === 'true'

    if (returnTo) {
      if (returnTo === 'new-job-ad') {
        router.push({ name: 'company-new-job-ad' })
      } else if (returnTo === 'company') {
        if (isNewCompany) {
          appStore.showSnack(
            'Willkommen! Ihr Unternehmen wurde erfolgreich erstellt und Ihr Abonnement ist aktiv.',
          )
        }
        router.push({ name: 'company' })
      } else {
        router.push({ name: returnTo })
      }
    } else {
      router.push({ name: 'company' })
    }
  }

  return {
    state: {
      isCompanySubscriptionActive,
      hasRemainingJobAdverts,
    },
    actions: {
      checkSubscriptionForJobAdvert,
      checkSubscriptionAfterCompanyCreation,
      handleSubscriptionReturn,
    },
  }
}
