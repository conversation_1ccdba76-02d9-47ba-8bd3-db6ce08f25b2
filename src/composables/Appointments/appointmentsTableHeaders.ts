import { inlineTranslate } from '@/utils/utils'
import { computed } from 'vue'
import type { VDataTable } from 'vuetify/components'

// Define the header type explicitly, handling potential undefined
type Headers = VDataTable['$props']['headers']
type AppointmentTableHeader = NonNullable<Headers>[number]

export const appointmentsTableHeaders = computed<AppointmentTableHeader[]>(() => {
  return [
    {
      title: inlineTranslate('Applicant'),
      sortable: false,
      key: 'profileImageUrl',
      align: 'center',
      width: '80px',
    },
    {
      title: inlineTranslate('Fair'),
      sortable: true,
      key: 'fair',
    },
    {
      title: inlineTranslate('Company'),
      sortable: true,
      key: 'company',
    },
    {
      title: inlineTranslate('Contact Person'),
      sortable: true,
      key: 'contactPersonName',
    },
    {
      title: inlineTranslate('Date'),
      sortable: true,
      key: 'date',
    },
    {
      title: inlineTranslate('Start'),
      sortable: true,
      key: 'startTime',
    },
    {
      title: inlineTranslate('End'),
      sortable: true,
      key: 'endTime',
    },
    {
      title: inlineTranslate('Status'),
      sortable: true,
      key: 'status',
    },
    {
      title: '',
      sortable: false,
      key: 'actions',
      width: '100px',
    },
  ]
})
