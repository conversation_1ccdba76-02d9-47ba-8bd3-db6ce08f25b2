import { useMutation } from '@vue/apollo-composable'
import { UPDATE_APPOINTMENT_STATUS } from '@/api/graphql/mutations/appointmentMutation'

export const useAppointmentView = () => {
  const { mutate: updateAppointment, loading: updatingAppointment } =
    useMutation(UPDATE_APPOINTMENT_STATUS)

  const markAppointmentAsViewed = async (appointmentId: string) => {
    try {
      const response = await updateAppointment({
        input: {
          id: appointmentId,
          companyIsNew: false,
        },
      })

      return response?.data?.updateAppointment
    } catch (error) {
      console.error('Failed to mark appointment as viewed:', error)
      throw error
    }
  }

  return {
    state: {
      updatingAppointment,
    },
    actions: {
      markAppointmentAsViewed,
    },
  }
}
