import { useAppStore } from '@/stores/appStore'
import { useAppointmentGraph } from '@/api/graphHooks/useAppointmentGraph'
import { Appointment, AppointmentStatus } from '@/gql/graphql'
import { useFilterStore } from '@/stores/fairs/filterStore'
import { useRoute } from 'vue-router'
import {
  AppointmentFilterApiInput,
  AppointmentFilterOption,
} from '@/api/apiTypes'
import { usePusher } from '@/composables/usePusher'
import { useAuthStore } from '@/stores/authStore'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

export const useAppointments = () => {
  dayjs.extend(utc)
  const appStore = useAppStore()
  const filterStore = useFilterStore()
  const authStore = useAuthStore()

  const route = useRoute()

  const isFairAppointmentModule = route.path.includes('fair/appointments')

  const {
    state: {
      appointmentsList: rawAppointmentsList,
      loadingAllAppointments,
      loadingFilteredAppointments,
      filteredAppointments,
      filteredAppointmentsError,
      filterOptions,
      loadingFilterOptions,
      loadingStatusUpdate,
    },
    actions: {
      loadFilterOptions,
      updateAppointmentStatus: updateAppointmentStatusMutation,
      deleteAppointment: deleteAppointmentMutation,
      refetchAppointments,
      refetchFilteredAppointments,
    },
  } = useAppointmentGraph()

  const appointmentFilters = computed(() => filterStore.filters)

  const getFilterId = (
    option: string | AppointmentFilterOption | null,
  ): string | null => {
    if (!option) return null
    if (typeof option === 'string') return option
    return option.id
  }

  const prepareFiltersForApi = computed((): AppointmentFilterApiInput => {
    const filters = appointmentFilters.value

    const apiFilters: AppointmentFilterApiInput = {
      applicantId: getFilterId(filters.applicantId),
      contactPersonId: getFilterId(filters.contactPersonId),
      companyId: getFilterId(filters.companyId),
      fairId: getFilterId(filters.fairId),
      status: getFilterId(filters.status),
      timeRange:
        filters.timeRange.startTime || filters.timeRange.endTime
          ? {
              startTime:
                filters.date && filters.timeRange.startTime
                  ? dayjs
                      .utc(
                        `${dayjs(filters.date).format('YYYY-MM-DD')}T${
                          filters.timeRange.startTime
                        }:00.000Z`,
                      )
                      .toDate()
                  : null,
              endTime:
                filters.date && filters.timeRange.endTime
                  ? dayjs
                      .utc(
                        `${dayjs(filters.date).format('YYYY-MM-DD')}T${
                          filters.timeRange.endTime
                        }:00.000Z`,
                      )
                      .toDate()
                  : null,
            }
          : undefined,
      date: filters.date || undefined,
    }

    return Object.fromEntries(
      Object.entries(apiFilters).filter(([_, value]) => value !== undefined),
    ) as AppointmentFilterApiInput
  })

  const formattedAppointments = computed(() => {
    const appointmentsData = filterStore.filteredAppointments || []

    if (!appointmentsData || !Array.isArray(appointmentsData)) {
      return []
    }

    return appointmentsData.map((appointment: Appointment) => {
      const applicant = appointment.applicant || {}
      const applicantImage = appointment.applicant.profileImageUrl || ''
      const applicantCity = appointment.applicant.city || ''
      const applicantSchool = appointment.applicant.graduation || ''
      const applicantName = `${applicant.firstName || ''} ${
        applicant.lastName || ''
      }`.trim()

      const hasUnseenMessages =
        appointment.chatRoom?.messages?.some(
          (message: any) => !message.isSeen,
        ) || false

      // Extract timeslot data
      const timeslot = appointment.contactPersonTimeslot || {}

      // Use dayjs.utc for time formatting
      const startTime = timeslot.startTime
        ? dayjs(timeslot.startTime).format('HH:mm')
        : ''
      const endTime = timeslot.endTime
        ? dayjs(timeslot.endTime).format('HH:mm')
        : ''

      // Format date with dayjs.utc
      const date = timeslot.startTime
        ? dayjs(timeslot.startTime).format('DD.MM.YYYY')
        : ''
      const companyFairContactPerson = timeslot.companyFairContactPerson || {}
      const contactPerson = companyFairContactPerson.contactPerson || {}
      const companyFairParticipation =
        companyFairContactPerson.companyFairParticipation || {}
      const fair = companyFairParticipation.fair || {}
      const companyIsNew = appointment.companyIsNew || false

      return {
        id: appointment.id,
        profileImageUrl:
          applicantImage || 'https://cdn.vuetifyjs.com/images/cards/foster.jpg',
        applicantName,
        applicantCity,
        applicantSchool,
        fair: fair.name || '',
        company: companyFairParticipation?.company?.name || '',
        contactPersonName: contactPerson.name || '',
        date,
        startTime,
        endTime,
        appointmentStatus: appointment.status || '',
        status: appointment.status.toLowerCase() || '',
        applicantId: applicant.id || '',
        rejectReason: appointment.rejectReason || '',
        companyId: companyFairParticipation?.company?.id || '',
        fairId: fair.id || '',
        companyFairContactPersonId: companyFairContactPerson.id || '',
        contactPersonTimeslotId: timeslot.id || '',
        contactPersonId: contactPerson.id || '',
        companyIsNew,
        hasUnseenMessages,
      }
    })
  })

  const handleUpdateAppointmentStatus = async (
    id: string,
    status: string,
    rejectReason?: string,
  ) => {
    try {
      filterStore.setAppointmentStatusLoading(true)
      appStore.showAppLoader()
      console.log('Updating appointment status:', id, status, rejectReason)

      const response = await updateAppointmentStatusMutation({
        input: {
          id,
          status,
          rejectReason,
        },
      })

      // Update the store with both status and rejectReason
      const normalizedStatus =
        response?.data?.updateAppointment?.status.toLowerCase()

      if (status.toLowerCase() === 'rejected' && rejectReason) {
        // Set rejectReason in store when rejecting
        filterStore.updateAppointmentStatus(normalizedStatus, rejectReason)
      } else if (status.toLowerCase() === 'confirmed') {
        // Clear rejectReason in store when confirming
        filterStore.updateAppointmentStatus(normalizedStatus)
        filterStore.clearAppointmentRejectReason()
      } else {
        // For other status updates
        filterStore.updateAppointmentStatus(normalizedStatus)
      }

      await refetchAppointments()
      console.log(response)
    } catch (err) {
      console.error('Error updating appointment status:', err)
      throw err
    } finally {
      filterStore.setAppointmentStatusLoading(false)
      appStore.hideAppLoader()
    }
  }

  const handleDeleteAppointment = async (id: string, rejectReason?: string) => {
    try {
      filterStore.setAppointmentStatusLoading(true)
      appStore.showAppLoader()

      const status = AppointmentStatus.Rejected

      const response = await updateAppointmentStatusMutation({
        input: {
          id,
          status,
          rejectReason,
        },
      })

      // Update the store with rejected status and rejectReason
      if (rejectReason) {
        filterStore.updateAppointmentStatus('rejected', rejectReason)
      }

      await refetchAppointments()
    } catch (err) {
      console.error('Error deleting appointment:', err)
      throw err
    } finally {
      filterStore.setAppointmentStatusLoading(false)
      appStore.hideAppLoader()
    }
  }

  const { listen, unsubscribe } = usePusher()

  let cleanupCreated: (() => void) | null = null
  let cleanupUpdated: (() => void) | null = null

  const initiateAppointmentListen = async (contactPersonId: string) => {
    try {
      const privateChannelAppointments = `private-liveContactPersonAppointments.super`

      // Listen for appointmentCreated events
      cleanupCreated = await listen(privateChannelAppointments, {
        event: 'appointmentCreated',
        callback: async (data: any) => {
          console.log('Appointment created:', data)
          try {
            await updateAppointmentInCache(data)
          } catch (error) {
            console.error('Error updating appointment cache:', error)
          }
        },
      })

      // Listen for appointmentUpdated events
      cleanupUpdated = await listen(privateChannelAppointments, {
        event: 'appointmentUpdated',
        callback: async (data: any) => {
          console.log('Appointment updated:', data)
          try {
            await updateAppointmentInCache(data)
          } catch (error) {
            console.error('Error updating appointment cache:', error)
          }
        },
      })
    } catch (error) {
      console.error('Error setting up appointment listener:', error)
    }
  }

  const stopAppointmentListen = () => {
    if (cleanupCreated) {
      cleanupCreated()
      cleanupCreated = null
    }
    if (cleanupUpdated) {
      cleanupUpdated()
      cleanupUpdated = null
    }
    const privateChannelAppointments = `private-liveContactPersonAppointments`
    unsubscribe(privateChannelAppointments)
  }

  const updateAppointmentInCache = async (appointmentData: any) => {
    await refetchFilteredAppointments()
    await refetchAppointments()
    await loadFilterOptions()
  }

  const fetchCompanyAppointments = async (
    companyId: string,
    skip: number,
    take: number,
  ) => {
    console.log(
      `Fetching appointments for company: ${companyId}, skip: ${skip}, take: ${take}`,
    )
    try {
      await refetchFilteredAppointments({
        filter: { companyId: companyId },
        skip: skip,
        take: take,
      })
    } catch (error) {
      console.error('Error fetching company appointments:', error)
      // Handle error appropriately
      appStore.showSnack('Error fetching appointments')
    }
  }

  return {
    state: {
      appointmentsList: formattedAppointments,
      rawAppointmentsList,
      loadingAllAppointments,
      loadingFilteredAppointments,
      filteredAppointmentsError,
      filterOptions,
      loadingStatusUpdate,
      loadingFilterOptions,
      isFairAppointmentModule,
      prepareFiltersForApi,
    },
    actions: {
      stopAppointmentListen,
      initiateAppointmentListen,
      handleUpdateAppointmentStatus,
      handleDeleteAppointment,
      refetchAppointments,
      refetchFilteredAppointments,
      fetchCompanyAppointments,
      loadFilterOptions,
    },
  }
}
