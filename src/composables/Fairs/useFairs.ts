import { useFairGraph } from '@/api/graphHooks/useFairGraph'
import { useFairFormStore } from '@/stores/fairs/fairFormStore'
import { useFairStore } from '@/stores/fairs/fairStore'
import { useRouter } from 'vue-router'
import { Company, Fair } from '@/gql/graphql'
import { useAppStore } from '@/stores/appStore'
import { useCompanyStore } from '@/stores/companyStore'
import { useAuthStore } from '@/stores/authStore'
import {
  convertFairDaysToDateTime,
  convertFairDaysToString,
} from '@core/utils/formatters'

export const useFairs = () => {
  const {
    state: {
      allFairsList,
      companiesInFair,
      loadingAllFairs,
      currentFair,
      loadingCurrentFair,
      loadingCreateFair,
      loadingCompaniesInFair,
      loadingCloneFair,
    },
    actions: {
      createFair,
      updateFair,
      loadFairs,
      cloneFair,
      loadCurrentFair,
      loadCompaniesInFair,
    },
  } = useFairGraph()

  const fairFormStore = useFairFormStore()
  const fairStore = useFairStore()
  const appStore = useAppStore()
  const authStore = useAuthStore()
  const companyStore = useCompanyStore()
  const router = useRouter()
  const fairForm = useFairFormStore()

  const fid = computed(() => {
    return fairStore.fair?.id || router.currentRoute.value.params.fid
  })
  const rawDays = fairFormStore.fairDays || []

  const companiesInFairList = computed(() => {
    return companiesInFair.value?.map((participation: any) => ({
      address: participation.company.address,
      city: participation.company.city,
      companyUserId: participation.company.companyUserId,
      detailContent: participation.company.detailContent,
      dynamicLink: participation.company.dynamicLink,
      foundingYear: participation.company.foundingYear,
      headerImageUrl: participation.company.headerImageUrl,
      logoImageUrl: participation.company.logoImageUrl,
      id: participation.company.id,
      name: participation.company.name,
      totalEmployees: participation.company.totalEmployees,
      updatedAt: participation.company.updatedAt,
      participationId: participation.id,
      contactPersonName:
        participation.companyFairContactPersons[0]?.contactPerson.name,
      contactPersonEmail:
        participation.companyFairContactPersons[0]?.contactPerson.email,
      contactPersonPhone:
        participation.companyFairContactPersons[0]?.contactPerson.phone,
      contactPersonId:
        participation.companyFairContactPersons[0]?.contactPerson.id,
    }))
  })

  //Add fetch fair
  const fetchFairs = async () => {
    await loadFairs()
  }

  const handleCreateFair = async () => {
    const newFairDays = convertFairDaysToDateTime(fairForm.fairDays)

    const newFairData = {
      input: {
        name: fairFormStore.name,
        description: fairFormStore.description,
        city: fairFormStore.city,
        location: fairFormStore.location,
        locationName: fairFormStore.locationName,
        status: fairFormStore.status,
        startDate: fairFormStore.startDate,
        endDate: fairFormStore.endDate,
        registrationStartDate: fairFormStore.registrationStartDate,
        registrationEndDate: fairFormStore.registrationEndDate,
        contactPersonName: fairFormStore.contactPersonName,
        contactPersonEmail: fairFormStore.contactPersonEmail,
        publisherName: fairFormStore.publisherName,
        publisherLogoImageUrl: fairFormStore.publisherLogoSavedUrl,
        logoImageUrl: fairFormStore.logoSavedUrl,
      },
      fairDays: newFairDays,
    }

    const uploadedFairImgs = await fairStore.uploadFairImages(
      fairFormStore,
      true,
    )

    if (uploadedFairImgs.logoUrl) {
      newFairData.input.logoImageUrl = uploadedFairImgs.logoUrl
    }
    if (uploadedFairImgs.publisherLogoUrl) {
      newFairData.input.publisherLogoImageUrl =
        uploadedFairImgs.publisherLogoUrl
    }

    return await createFair(newFairData)
  }

  const handleUpdateFair = async () => {
    const newFairDays = convertFairDaysToDateTime(fairForm.fairDays)
    console.log('updateing Fair as we speak::')
    const updateFairData = {
      id: fairForm?.id,
      input: {
        id: fairFormStore?.id,
        name: fairFormStore.name,
        description: fairFormStore.description,
        city: fairFormStore.city,
        location: fairFormStore.location,
        locationName: fairFormStore.locationName,
        status: fairFormStore.status,
        startDate: fairFormStore.startDate,
        endDate: fairFormStore.endDate,
        registrationStartDate: fairFormStore.registrationStartDate,
        registrationEndDate: fairFormStore.registrationEndDate,
        contactPersonName: fairFormStore.contactPersonName,
        contactPersonEmail: fairFormStore.contactPersonEmail,
        publisherName: fairFormStore.publisherName,
        publisherLogoImageUrl: fairFormStore.publisherLogoSavedUrl,
        logoImageUrl: fairFormStore.logoSavedUrl,
      },
      fairDays: newFairDays,
    }

    // Only upload images if they've changed
    if (fairFormStore.logoResult || fairFormStore.publisherLogoResult) {
      const uploadedFairImgs = await fairStore.uploadFairImages(
        fairFormStore,
        false,
      )

      if (uploadedFairImgs.logoUrl) {
        updateFairData.input.logoImageUrl = uploadedFairImgs.logoUrl
      }
      if (uploadedFairImgs.publisherLogoUrl) {
        updateFairData.input.publisherLogoImageUrl =
          uploadedFairImgs.publisherLogoUrl
      }
    }

    return await updateFair(updateFairData)
  }
  const handleCloneFair = async () => {
    const newFairData = {
      input: {
        name: fairFormStore.name,
        description: fairFormStore.description,
        city: fairFormStore.city,
        location: fairFormStore.location,
        locationName: fairFormStore.locationName,
        status: fairFormStore.status,
        startDate: fairFormStore.startDate,
        endDate: fairFormStore.endDate,
        registrationStartDate: fairFormStore.registrationStartDate,
        registrationEndDate: fairFormStore.registrationEndDate,
        contactPersonName: fairFormStore.contactPersonName,
        contactPersonEmail: fairFormStore.contactPersonEmail,
        publisherName: fairFormStore.publisherName,
        publisherLogoImageUrl: fairFormStore.publisherLogoSavedUrl,
        logoImageUrl: fairFormStore.logoSavedUrl,
      },
    }

    let uploadedFairImgs = { logoUrl: '', publisherLogoUrl: '' }

    if (
      fairFormStore.publisherLogoResult?.imageUrl ||
      fairFormStore.logoResult?.imageUrl
    ) {
      uploadedFairImgs = await fairStore.uploadFairImages(fairFormStore, true)
    }

    if (uploadedFairImgs.logoUrl) {
      newFairData.input.logoImageUrl = uploadedFairImgs.logoUrl
    }
    if (uploadedFairImgs.publisherLogoUrl) {
      newFairData.input.publisherLogoImageUrl =
        uploadedFairImgs.publisherLogoUrl
    }

    return await cloneFair({ input: newFairData.input, id: fairFormStore.id })
  }

  const handleFairClicked = async (e: any, fair: Fair) => {
    const routePath = 'fair-fairs-fid'
    await router.push({
      name: routePath,
      params: { fid: fair.id },
    })
    fairStore.updateFair(fair)
    fairFormStore.setFair(fair)
    await appStore.setInFairView(true)
  }

  const handleFairCompanyClicked = async (e: any, company: Company) => {
    const routePath = 'fair-fairs-fid-company-cid'
    await router.push({
      name: routePath,
      params: { fid: fid.value, cid: company.id },
    })
    await authStore.setCompanyId(company.id)
    companyStore.updateCompany(company)
    await appStore.setInCompanyView(true)
  }

  const handleCompanyFairClicked = async (e: any, fair: Fair) => {
    const routePath = 'company-fair-management-fid-company-cid'
    await router.push({
      name: routePath,
      params: { fid: fair.id, cid: authStore.companyId },
    })
    fairStore.updateFair(fair)
  }

  const exitFairCompany = async () => {
    // Reset company-specific state first
    companyStore.resetCompany()
    companyStore.setContactPersonsListView(false)
    companyStore.setFairJobsListView(false)
    companyStore.setContactPersonTimeSlotsView(false)

    // Update app state
    await appStore.setInCompanyView(false)

    // Navigate back to fair view
    const currentFairId =
      fairStore.fair?.id || router.currentRoute.value.params.fid
    if (currentFairId) {
      await router.push({
        name: 'fair-fairs-fid',
        params: { fid: currentFairId },
      })
    }
  }

  const exitFair = async () => {
    // Reset all state first
    companyStore.resetCompany()
    companyStore.setContactPersonsListView(false)
    companyStore.setFairJobsListView(false)
    companyStore.setContactPersonTimeSlotsView(false)
    fairStore.resetFair()
    await appStore.setInFairView(false)
    await appStore.setInCompanyView(false)

    // Navigate to fairs list
    await router.push({ name: 'fair-fairs' })
  }

  const exitCompany = () => {
    companyStore.resetCompany()
    appStore.setInCompanyView(false)
    router.push({ name: 'super-company' })
  }

  const resetBreadCrumb = async () => {
    companyStore.resetCompany()
    await appStore.setInCompanyView(false)
    await appStore.setInFairView(false)
    fairStore.resetFair()
  }

  const handlePauseFair = async (fair: Fair) => {
    try {
      appStore.showAppLoader()
      const updateFairData = {
        id: fair.id,
        input: {
          id: fair.id,
          status: 'INACTIVE',
        },
      }

      await updateFair(updateFairData)
      await loadFairs()
      appStore.showSnack('Fair paused successfully')
      return true
    } catch (error) {
      console.error('Error pausing fair:', error)
      appStore.showSnack('Failed to pause fair')
      return false
    } finally {
      appStore.hideAppLoader()
    }
  }

  const handleActivateFair = async (fair: Fair) => {
    try {
      appStore.showAppLoader()
      const updateFairData = {
        id: fair.id,
        input: {
          id: fair.id,
          status: 'ACTIVE',
        },
      }

      await updateFair(updateFairData)
      await loadFairs()
      appStore.showSnack('Messe erfolgreich aktiviert')
      return true
    } catch (error) {
      appStore.showSnack('Aktivierung der Messe fehlgeschlagen')
      return false
    } finally {
      appStore.hideAppLoader()
    }
  }

  return {
    state: {
      allFairsList,
      companiesInFair,
      companiesInFairList,
      loadingAllFairs,
      currentFair,
      loadingCurrentFair,
      loadingCreateFair,
      loadingCompaniesInFair,
    },
    actions: {
      handleCreateFair,
      handleCloneFair,
      handleFairClicked,
      handleFairCompanyClicked,
      handleCompanyFairClicked,
      refetchFairs: loadFairs,
      loadFairs,
      resetBreadCrumb,
      exitFair,
      exitCompany,
      exitFairCompany,
      handleUpdateFair,
      fetchFairs,
      loadCurrentFair,
      loadCompaniesInFair,
      handlePauseFair,
      handleActivateFair,
    },
  }
}
