import { inlineTranslate } from '@/utils/utils'
import { computed } from 'vue'

export const fairHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Logo'),
      sortable: true,
      key: 'logoImageUrl',
    },
    {
      title: inlineTranslate('Name'),
      sortable: true,
      key: 'name',
    },
    {
      title: inlineTranslate('City'),
      sortable: false,
      key: 'city',
    },
    {
      title: 'Location',
      sortable: false,
      key: 'location',
    },
    {
      title: inlineTranslate('Contact Person'),
      sortable: false,
      key: 'contactPersonName',
    },
    {
      title: inlineTranslate('Period'), //Zeitraum
      sortable: false,
      key: 'startDate',
    },
    {
      title: inlineTranslate('Time'), //Uhrzeit
      sortable: false,
      key: 'endDate',
    },
    {
      title: 'Status',
      sortable: false,
      key: 'status',
    },
    {
      title: inlineTranslate('Actions'),
      sortable: false,
      key: 'actions',
    },
  ]
})

export const fairCompanyHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Logo'),
      sortable: true,
      key: 'logoImageUrl',
    },
    {
      title: inlineTranslate('Name'),
      sortable: false,
      key: 'name',
    },
    {
      title: inlineTranslate('Address'),
      sortable: false,
      key: 'address',
    },
    // {
    //   title: inlineTranslate('Contact Person'),
    //   sortable: false,
    //   key: 'contactPersonName',
    // },
    // {
    //   title: inlineTranslate('Telephone'), //Zeitraum
    //   sortable: false,
    //   key: 'contactPersonPhone',
    // },
    // {
    //   title: inlineTranslate('Email'), //Uhrzeit
    //   sortable: false,
    //   key: 'contactPersonEmail',
    // },
    // {
    //   title: 'Status',
    //   sortable: false,
    //   key: 'status',
    // },
    {
      title: inlineTranslate('Actions'),
      sortable: false,
      key: 'actions',
    },
  ]
})

export const contactPersonHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Name'),
      sortable: true,
      key: 'contactPersonName',
    },
    {
      title: inlineTranslate('Email'),
      sortable: false,
      key: 'contactPersonEmail',
    },
    {
      title: inlineTranslate('Phone'),
      sortable: false,
      key: 'contactPersonPhone',
    },
    {
      title: '',
      sortable: false,
      key: 'actions',
    },
  ]
})

export const companyFairJobHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Job Postings'),
      sortable: true,
      key: 'title',
    },
    {
      title: inlineTranslate('Description'),
      sortable: true,
      key: 'description',
    },
    {
      title: '',
      sortable: false,
      key: 'actions',
    },
  ]
})

export const fairJobHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Job Postings'),
      sortable: true,
      key: 'title',
    },
    {
      title: '',
      sortable: false,
      key: 'actions',
    },
  ]
})
