import { useChatRoomGraph } from '@/api/graphHooks/useChatRoomGraph'

export const useChatRoom = () => {
  const {
    state: {
      chatRoomDetails,
      loadingMatchChatRoom,
      loadingAppointmentMatchChatRoom,
    },
    actions: { fetchChatRoom },
  } = useChatRoomGraph()

  return {
    state: {
      chatRoomDetails,
      loadingMatchChatRoom,
      loadingAppointmentMatchChatRoom,
    },
    actions: {
      fetchChatRoom,
    },
  }
}
