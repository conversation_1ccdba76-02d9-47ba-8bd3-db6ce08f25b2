import { useCompanyFairParticipationGraph } from '@/api/graphHooks/useCompanyFairParticipationGraph'
import { useRoute } from 'vue-router'
import { useFairStore } from '@/stores/fairs/fairStore'
import { useAppStore } from '@/stores/appStore'

export const useCompanyFairParticipation = () => {
  const {
    state: {
      loadingRemoveCompanyFromFair,
      loadingCreate,
      loadingRemove,
      loadingUpdate,
    },
    actions: {
      removeParticipation,
      removeCompanyFromFair,
      createParticipation,
      updateParticipation,
    },
  } = useCompanyFairParticipationGraph()

  const route = useRoute()
  const fairStore = useFairStore()
  const appStore = useAppStore()

  const fairId = (route.params.fid as string) || fairStore.getFair?.id

  const handleCreateParticipation = async (companyId: string) => {
    const inputVals = {
      companyId: companyId,
      fairId: fairId,
      categoryIds: [],
    }
    try {
      const response = await createParticipation({
        ...inputVals,
      })
      console.log('Participation created:', response)
      return response
    } catch (error) {
      console.error('Error creating participation:', error)
      throw error
    }
  }

  const handleRemoveParticipation = async (companyId: string) => {
    const inputVals = {
      companyId: companyId,
      fairId: fairId,
    }

    try {
      const response = await removeCompanyFromFair({
        ...inputVals,
      })
      console.log('Participation removed:', response)
      return response
      appStore.showSnack('Company successfully removed from fair')
    } catch (error: any) {
      console.error('Error removing participation:', error)

      let errorMessage = 'Error removing company from fair'
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        errorMessage = error.graphQLErrors[0].message
      } else if (error.message) {
        errorMessage = error.message
      }

      // Show error message in snackbar
      appStore.showSnack(errorMessage)

      throw error
    }
  }

  const handleUpdateCategories = async (id: string, categoryIds: string[]) => {
    try {
      const response = await updateParticipation({
        input: {
          id,
          categoryIds,
        },
      })
    } catch (err) {
      console.error('Error updating participation categories:', err)
      throw err
    }
  }

  const handleUpdatePartnerLinks = async (id: string, partnerLinkIds: string[]) => {
    try {
      const response = await updateParticipation({
        input: {
          id,
          partnerLinkIds,
        },
      })
    } catch (err) {
      console.error('Error updating participation partner links:', err)
      throw err
    }
  }

  const handleDeleteParticipation = async (id: string) => {
    try {
      const response = await removeParticipation({
        id,
      })
      console.log('Participation removed:', response)
      return response
    } catch (error) {
      console.error('Error removing participation:', error)
      throw error
    }
  }

  return {
    state: {
      loadingCreate,
      loadingRemove,
      loadingUpdate,
      loadingRemoveCompanyFromFair,
    },
    actions: {
      handleCreateParticipation,
      handleDeleteParticipation,
      handleUpdateCategories,
      handleUpdatePartnerLinks,
      handleRemoveParticipation,
    },
  }
}
