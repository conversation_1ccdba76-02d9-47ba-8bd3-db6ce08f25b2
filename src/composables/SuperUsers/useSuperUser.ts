import { useSuperUserGraph } from '@/api/graphHooks/useSuperUserGraph'
import { useAppStore } from '@/stores/appStore'

export const useSuperUser = () => {
  const {
    state: { verifySuperUserLoading },
    actions: { verifySuperUser },
  } = useSuperUserGraph()

  const appStore = useAppStore()

  const isSuperUser = async (email: string) => {
    try {
      const superUser = await verifySuperUser({
        email,
      })

      if (superUser) {
        return true
      }
    } catch (error) {
      console.error(error)
      appStore.showSnack(
        `Entschuldigung, Ben<PERSON>er mit der E-Mail-Adresse “${email}” wurde nicht gefunden. 😊`,
      )
      return false
    }
  }

  return {
    state: {
      verifySuperUserLoading,
    },
    actions: {
      isSuperUser,
    },
  }
}
