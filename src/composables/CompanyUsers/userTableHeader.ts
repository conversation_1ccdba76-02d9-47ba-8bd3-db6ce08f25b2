import { inlineTranslate } from '@/utils/utils'

export const UserTableHeader = computed(() => {
  return [
    {
      title: inlineTranslate('Image'), // Bild
      sortable: true,
      key: 'avatarImageUrl',
    },
    {
      title: inlineTranslate('Name'),
      sortable: false,
      key: 'name',
    },
    {
      title: inlineTranslate('Email'),
      sortable: true,
      key: 'email',
    },
    {
      title: inlineTranslate('Rights'), //Berechtigungen
      sortable: true,
      key: 'rights',
    },
  ]
})
