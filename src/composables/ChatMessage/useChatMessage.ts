import { useChatMessageGraph } from '@/api/graphHooks/useChatMessageGraph'
import { useChatRoomGraph } from '@/api/graphHooks/useChatRoomGraph'
import { usePusher } from '@/composables/usePusher'
import { useSound } from '@/composables/useSound'
import { useChatStore } from '@/stores/apiStores/useChatStore'
import { useAuthStore } from '@/stores/authStore'
import { useUsersStore } from '@/stores/usersStore'

import { useFilterStore } from '@/stores/fairs/filterStore'

export const useChatMessage = () => {
  const {
    actions: { createChatMessage },
    state: { isCreatingChatMessage },
  } = useChatMessageGraph()

  const {
    actions: { fetchChatRoom },
    state: { chatRoomDetails },
  } = useChatRoomGraph()

  const authStore = useAuthStore()
  const userStore = useUsersStore()
  const store = useChatStore()
  const filterStore = useFilterStore()

  const { play } = useSound()

  const companyUserId = authStore.claims?.companyUserId

  const isSuper = authStore?.claims?.isSuperUser

  const companyFairContactPersonId = computed(() => {
    return filterStore.activeAppointment?.companyFairContactPersonId
  })

  const authorName = computed(() => {
    if (isSuper) {
      return 'Admin'
    }
    return companyFairContactPersonId.value
      ? filterStore?.activeAppointment?.contactPersonName
      : userStore?.activeUser?.name || authStore?.user?.displayName
  })

  const { listen, unsubscribe } = usePusher()

  const initiateChatListen = async (id: string, onNewMessage: () => void) => {
    const privateChannel = `private-liveChatMessages.${id}`

    await listen(privateChannel, {
      event: 'created',
      callback: async (data: any) => {
        if (data?.isApplicant) {
          play('recMsg')
          await store.updateChatFromLive(data)
          onNewMessage()
        }
      },
    })
  }

  const stopChatListen = async (id: string) => {
    const privateChannel = `private-liveChatMessages.${id}`
    unsubscribe(privateChannel)
  }

  const saveChatMessage = async (content: string) => {
    let chatRoomId = chatRoomDetails.value?.id || store.chatRoomDetails?.id
    if (!chatRoomId) {
      await fetchChatRoom()
      chatRoomId = chatRoomDetails.value?.id
    }
    const chatMessageVariables = {
      chatRoomId: chatRoomId,
      companyUserId: companyUserId,
      companyFairContactPersonId: companyFairContactPersonId.value,
      createMessageInput: {
        content: content,
        authorName: authorName.value,
        isCompany: true,
        isApplicant: false,
        isSeen: true,
      },
    }

    try {
      const msgSaved = await createChatMessage(chatMessageVariables)
      await store.updateChatFromLive({
        ...msgSaved?.data?.createMessage,
        companyUserId: companyUserId,
      })
    } catch (error) {
      console.error('Error saving chat message:', error)
    }
  }

  return {
    state: {
      isCreatingChatMessage,
    },
    actions: {
      saveChatMessage,
      initiateChatListen,
      stopChatListen,
    },
  }
}
