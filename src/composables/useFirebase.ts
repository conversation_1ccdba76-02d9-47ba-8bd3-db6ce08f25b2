import {
  browserLocalPersistence,
  getAuth,
  onAuthStateChanged,
  setPersistence,
  User,
} from 'firebase/auth'
import { auth } from '@/libs/firebase/config'
import { ref } from 'vue'

setPersistence(auth, browserLocalPersistence)
  .then(r => console.log('setPersistence', r))
  .catch(e => console.log('setPersistence', e))

const firebaseUser = ref<User | null>(null)

/**
 * Restores the login of the user
 * Should be called on app startup
 * @returns {Promise<User>} the user if the user is logged in, null if the user is not logged in
 */
const restoreLogin = async (): Promise<User | null> => {
  return new Promise((resolve, reject) => {
    const auth = getAuth()
    onAuthStateChanged(auth, user => {
      firebaseUser.value = user
      resolve(user)
    })
  })
}

const getProfilePictureUrl = async (): Promise<string | null> => {
  const user = firebaseUser.value
  if (!user) {
    return null
  }
  return user.photoURL
}

export const useFirebase = () => {
  return {
    auth,
    currentUser: auth.currentUser,
    firebaseUser,
    getProfilePictureUrl,
    restoreLogin,
  }
}
