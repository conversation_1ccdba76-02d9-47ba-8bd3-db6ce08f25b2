import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
import router from '@/router'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useUsersStore } from '@/stores/usersStore'
import { useCompanyStore } from '@/stores/companyStore'

export const useAuth = () => {
  const authStore = useAuthStore()
  const usersStore = useUsersStore()
  const appStore = useAppStore()
  const companyStore = useCompanyStore()

  const credentials = authStore.credentials

  const isUnlimitedPlan = computed(() => {
    return (
      companyStore.getCompany?.subscriptionConfig.subscriptionType ===
      'COMPANY_UNLIMITED'
    )
  })

  const {
    state: { resetPasswordLoading },
    actions: { registerUser, saveCompanyUser, resetPassword },
  } = useAuthGraph()

  const handleSaveCompanyUser = async () => {
    return await saveCompanyUser()
  }

  const handleResetPassword = async (email: string) => {
    try {
      const resetPass = await resetPassword({ email })
      return resetPass
    } catch (error) {
      appStore.showSnack(
        `Entschuldigung, Benutzer mit der E-Mail-Adresse “${email}” wurde nicht gefunden. 😊`,
      )
      console.error(error)
      throw new Error('error resetting password')
    }
  }

  const handleRegisterUser = async (): Promise<String> => {
    const { uid } = await authStore.registerUser(credentials)

    const newUser = await registerUser({
      registerInput: {
        firebaseUid: uid,
      },
    })

    const newUserId = newUser?.data?.register?.user?.id
    await authStore.setUserId(newUserId)

    const newCompanyUser = await saveCompanyUser({
      userId: newUserId,
      createCompanyUserInput: {
        name: credentials?.adminName,
        email: credentials?.email,
      },
    })

    if (newCompanyUser?.data?.createCompanyUser?.id) {
      authStore.credentials = {
        adminName: '',
        email: '',
        password: '',
        userId: newUserId,
        confirmPassword: '',
        confirmedPolicies: false,
      }

      usersStore.setActiveUser({
        rights: [{ superUser: true }],
        ...newCompanyUser?.data?.createFirebaseBridgeUser,
      })

      localStorage.setItem(
        'userDetails',
        JSON.stringify({
          userId: newUserId,
          companyUserId: newCompanyUser?.data?.createCompanyUser?.id,
        }),
      )
      await router.push({ name: 'company-register-company' })
    }
    return newCompanyUser?.data?.createCompanyUser?.id
  }

  return {
    state: {
      resetPasswordLoading,
      isUnlimitedPlan,
    },
    actions: {
      handleRegisterUser,
      handleSaveCompanyUser,
      handleResetPassword,
    },
  }
}
