import { computed, ref } from 'vue'
import { useCompanySubscriptionConfigGraph } from '@/api/graphHooks/useCompanySubscriptionConfigGraph'
import { useAppStore } from '@/stores/appStore'

export interface CompanySubscriptionConfig {
  id: string
  companyId: string
  company: {
    id: string
    name: string
    city: string
    country: string
    address?: string
    totalEmployees?: number
  }
  subscriptionType: 'PER_JOB_ADVERT' | 'COMPANY_UNLIMITED'
  availablePricingPlans: Array<{
    id: string
    name: string
    displayName: string
    price: number
    currency: string
    billingPeriod: string
  }>
  notes?: string
  createdAt: string
  updatedAt: string
}

export const useCompanySubscriptionConfig = () => {
  const appStore = useAppStore()

  const {
    // Data
    allConfigs,
    selectedConfig,
    configByCompany,
    allPricingPlans,

    // Loading states
    loadingAllConfigs,
    loadingConfigById,
    loadingConfigByCompanyId,
    loadingAllPricingPlans,
    creatingConfig,
    updatingConfig,
    bulkUpdatingConfigs,
    deletingConfig,

    // Actions
    setFilter,
    setConfigId,
    setCompanyId,
    refetchAllConfigs,
    refetchPricingPlans,
    createConfig: createConfigMutation,
    updateConfig: updateConfigMutation,
    bulkUpdateConfigs: bulkUpdateConfigsMutation,
    deleteConfig: deleteConfigMutation,
  } = useCompanySubscriptionConfigGraph()

  const fetchAllConfigs = async (filter?: {
    searchQuery?: string
    subscriptionType?: 'PER_JOB_ADVERT' | 'COMPANY_UNLIMITED'
    pricingPlanId?: string
  }) => {
    try {
      const filters = filter ? { filter } : undefined
      setFilter(filter)
      await refetchAllConfigs()
      return allConfigs.value
    } catch (error) {
      console.error('Error fetching subscription configs:', error)
      appStore.showSnack('Failed to load subscription configurations', 'error')
      throw error
    }
  }

  // Business logic for fetching a single config by ID
  const fetchConfigById = async (id: string) => {
    try {
      setConfigId(id)
      await refetchAllConfigs()
      return selectedConfig.value
    } catch (error) {
      console.error('Error fetching subscription config:', error)
      appStore.showSnack('Failed to load subscription configuration', 'error')
      throw error
    }
  }

  // Business logic for fetching config by company ID
  const fetchConfigByCompanyId = async (companyId: string) => {
    try {
      setCompanyId(companyId)
      await refetchAllConfigs()
      return configByCompany.value
    } catch (error) {
      console.error('Error fetching company subscription config:', error)
      appStore.showSnack(
        'Failed to load company subscription configuration',
        'error',
      )
      throw error
    }
  }

  // Business logic for fetching all pricing plans
  const fetchAllPricingPlans = async () => {
    try {
      await refetchPricingPlans()
      return allPricingPlans.value
    } catch (error) {
      console.error('Error fetching pricing plans:', error)
      appStore.showSnack('Failed to load pricing plans', 'error')
      throw error
    }
  }

  // Business logic for creating a subscription config
  const createSubscriptionConfig = async (input: {
    companyId: string
    subscriptionType?: 'PER_JOB_ADVERT' | 'COMPANY_UNLIMITED'
    availablePricingPlanIds?: string[]
    notes?: string
  }) => {
    try {
      const result = await createConfigMutation({
        input: {
          companyId: input.companyId,
          subscriptionType: input.subscriptionType || 'COMPANY_UNLIMITED',
          availablePricingPlanIds: input.availablePricingPlanIds || [],
          notes: input.notes,
        },
      })

      if (result?.data?.createCompanySubscriptionConfig) {
        appStore.showSnack('Subscription configuration created successfully')
        await refetchAllConfigs()
        return result.data.createCompanySubscriptionConfig
      }

      throw new Error('Failed to create subscription configuration')
    } catch (error) {
      console.error('Error creating subscription config:', error)
      appStore.showSnack('Failed to create subscription configuration', 'error')
      throw error
    }
  }

  // Business logic for updating a subscription config
  const updateSubscriptionConfig = async (input: {
    id: string
    subscriptionType?: 'PER_JOB_ADVERT' | 'COMPANY_UNLIMITED'
    availablePricingPlanIds?: string[]
    notes?: string
  }) => {
    try {
      const updateData: any = {
        id: input.id,
      }

      if (input.subscriptionType !== undefined) {
        updateData.subscriptionType = input.subscriptionType
      }
      if (input.availablePricingPlanIds !== undefined) {
        updateData.availablePricingPlanIds = input.availablePricingPlanIds
      }
      if (input.notes !== undefined) {
        updateData.notes = input.notes
      }

      const result = await updateConfigMutation({ input: updateData })

      if (result?.data?.updateCompanySubscriptionConfig) {
        appStore.showSnack('Subscription configuration updated successfully')
        await refetchAllConfigs()
        return result.data.updateCompanySubscriptionConfig
      }

      throw new Error('Failed to update subscription configuration')
    } catch (error) {
      console.error('Error updating subscription config:', error)
      appStore.showSnack('Failed to update subscription configuration', 'error')
      throw error
    }
  }

  // Business logic for bulk updating subscription configs
  const bulkUpdateSubscriptionConfigs = async (input: {
    configIds: string[]
    subscriptionType?: 'PER_JOB_ADVERT' | 'COMPANY_UNLIMITED'
    availablePricingPlanIds?: string[]
    notes?: string
  }) => {
    try {
      const updateData: any = {
        configIds: input.configIds,
      }

      if (input.subscriptionType !== undefined) {
        updateData.subscriptionType = input.subscriptionType
      }
      if (input.availablePricingPlanIds !== undefined) {
        updateData.availablePricingPlanIds = input.availablePricingPlanIds
      }
      if (input.notes !== undefined) {
        updateData.notes = input.notes
      }

      const result = await bulkUpdateConfigsMutation({ input: updateData })

      if (result?.data?.bulkUpdateCompanySubscriptionConfigs) {
        appStore.showSnack(
          `Updated ${input.configIds.length} subscription configurations successfully`,
        )
        await refetchAllConfigs()
        return result.data.bulkUpdateCompanySubscriptionConfigs
      }

      throw new Error('Failed to bulk update subscription configurations')
    } catch (error) {
      console.error('Error bulk updating subscription configs:', error)
      appStore.showSnack(
        'Failed to update subscription configurations',
        'error',
      )
      throw error
    }
  }

  // Business logic for deleting a subscription config
  const deleteSubscriptionConfig = async (id: string) => {
    try {
      const result = await deleteConfigMutation({ id })

      if (result?.data?.deleteCompanySubscriptionConfig) {
        appStore.showSnack('Subscription configuration deleted successfully')
        await refetchAllConfigs()
        return true
      }

      throw new Error('Failed to delete subscription configuration')
    } catch (error) {
      console.error('Error deleting subscription config:', error)
      appStore.showSnack('Failed to delete subscription configuration', 'error')
      throw error
    }
  }

  // Computed properties
  const hasConfigs = computed(() => allConfigs.value.length > 0)

  const configsBySubscriptionType = computed(() => {
    const configs = allConfigs.value || []
    return {
      perJobAdvert: configs.filter(
        c => c.subscriptionType === 'PER_JOB_ADVERT',
      ),
      companyUnlimited: configs.filter(
        c => c.subscriptionType === 'COMPANY_UNLIMITED',
      ),
    }
  })

  return {
    // State
    state: {
      allConfigs,
      selectedConfig,
      configByCompany,
      allPricingPlans,
      hasConfigs,
      configsBySubscriptionType,
      loadingAllConfigs,
      loadingConfigById,
      loadingConfigByCompanyId,
      loadingAllPricingPlans,
      creatingConfig,
      updatingConfig,
      bulkUpdatingConfigs,
      deletingConfig,
    },

    // Actions
    actions: {
      fetchAllConfigs,
      fetchConfigById,
      fetchConfigByCompanyId,
      fetchAllPricingPlans,
      createSubscriptionConfig,
      updateSubscriptionConfig,
      bulkUpdateSubscriptionConfigs,
      deleteSubscriptionConfig,
      refetchAllConfigs,
    },
  }
}
