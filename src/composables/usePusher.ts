import { getIdToken } from '@/api/middleware/getToken'
import { DEV_API_HOST, PUSHER_CLUSTER, PUSHER_KEY } from '@/constants/app'
import { onUnmounted, ref, readonly } from 'vue'
import Pusher, { Channel } from 'pusher-js'

interface PusherConfig {
  key: string
  cluster: string
  encrypted?: boolean
  forceTLS?: boolean
  enabledTransports?: string[]
  authEndpoint: string
  auth?: {
    headers?: Record<string, string>
  }
}

interface EventCallback {
  event: string
  callback: (data: any) => void
}

let pusherInstance: Pusher | null = null
let activeConnections = 0
const activeChannels = new Map<string, Channel>()

export function usePusher() {
  const channels = ref(activeChannels)
  const connectionError = ref<Error | null>(null)
  const isConnected = ref(false)

  const getAuthToken = async (): Promise<string> => {
    try {
      const token = await getIdToken()
      return token
    } catch (error) {
      console.error('Failed to get auth token:', error)
      return ''
    }
  }

  const getConfig = async (): Promise<PusherConfig> => {
    const token = await getAuthToken()
    return {
      key: PUSHER_KEY,
      cluster: PUSHER_CLUSTER,
      encrypted: true,
      forceTLS: true,
      authEndpoint: `${DEV_API_HOST}/pusher/auth`,
      auth: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  }

  const initializePusher = async () => {
    if (!pusherInstance) {
      const config = await getConfig()
      try {
        pusherInstance = new Pusher(config.key, {
          cluster: config.cluster,
          authEndpoint: config.authEndpoint,
          authTransport: 'ajax',
          auth: {
            headers: config.auth?.headers,
          },
        })

        pusherInstance.connection.bind('connected', () => {
          isConnected.value = true
        })

        pusherInstance.connection.bind('disconnected', () => {
          isConnected.value = false
        })

        pusherInstance.connection.bind('error', (err: Error) => {
          console.error('Pusher connection error:', err)
          connectionError.value = err
          isConnected.value = false
        })
      } catch (error) {
        console.error('Failed to initialize Pusher:', error)
        connectionError.value = error as Error
        throw error
      }
    }
    activeConnections++
    return pusherInstance
  }

  const subscribe = async (channelName: string) => {
    try {
      if (!activeChannels.has(channelName)) {
        const pusher = pusherInstance || (await initializePusher())
        const channel = pusher.subscribe(channelName)

        channel.bind('pusher:subscription_error', (error: any) => {
          console.error(`Subscription error for channel ${channelName}:`, error)
          connectionError.value = new Error(
            `Subscription error: ${error.message}`,
          )
        })

        activeChannels.set(channelName, channel)
        return channel
      }
      return activeChannels.get(channelName)!
    } catch (error) {
      console.error(`Failed to subscribe to channel ${channelName}:`, error)
      throw error
    }
  }

  const listen = async (
    channelName: string,
    { event, callback }: EventCallback,
  ) => {
    const channel = await subscribe(channelName)
    const boundCallback = (data: any) => {
      Promise.resolve().then(() => callback(data))
    }
    channel.bind(event, boundCallback)

    return () => {
      if (channel) {
        channel.unbind(event, boundCallback)
      }
    }
  }

  const unsubscribe = (channelName: string) => {
    if (activeChannels.has(channelName)) {
      if (pusherInstance) {
        pusherInstance.unsubscribe(channelName)
      }
      activeChannels.delete(channelName)
    }
  }

  const disconnect = () => {
    activeConnections--
    if (activeConnections === 0 && pusherInstance) {
      pusherInstance.disconnect()
      pusherInstance = null
      activeChannels.clear()
      isConnected.value = false
      connectionError.value = null
    }
  }

  return {
    subscribe,
    unsubscribe,
    listen,
    disconnect,
    channels: readonly(channels),
    connectionError: readonly(connectionError),
    isConnected: readonly(isConnected),
  }
}
