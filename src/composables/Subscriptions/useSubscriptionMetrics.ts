import { computed, onMounted } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import { GET_COMPANY_SUBSCRIPTION_METRICS } from '@/api/graphql/queries/companySubscriptionQueries'

export interface SubscriptionMetrics {
  totalActiveSubscriptions: number
  totalRevenue: number
  averageRevenuePerCompany: number
  topPlans: {
    planId: string
    planName: string
    subscriberCount: number
    revenue: number
  }[]
  revenueByPeriod: {
    period: string
    revenue: number
    subscriberCount: number
  }[]
}

export function useSubscriptionMetrics() {
  const {
    result: metricsResult,
    loading: loadingMetrics,
    refetch: refetchMetrics,
  } = useQuery<{
    companySubscriptionMetrics: SubscriptionMetrics
  }>(GET_COMPANY_SUBSCRIPTION_METRICS)

  const metrics = computed<SubscriptionMetrics | null>(
    () => metricsResult.value?.companySubscriptionMetrics || null
  )

  onMounted(() => {
    refetchMetrics()
  })

  return {
    metrics,
    loadingMetrics,
    refetchMetrics,
  }
}
