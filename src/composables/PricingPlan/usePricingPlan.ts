import { computed, ref } from 'vue'
import { usePricingPlanGraph } from '@/api/graphHooks/usePricingPlanGraph'

export const usePricingPlan = () => {
  const {
    // Data
    activePlans,
    allPlans,
    selectedPlan,
    
    // Loading states
    loadingActivePlans,
    loadingAllPlans,
    loadingPlanById,
    creatingPlan,
    updatingPlan,
    deactivatingPlan,
    
    // Actions
    loadAllPlans,
    loadPlanById,
    refetchActivePlans,
    refetchAllPlans,
    createPlan: createPlanMutation,
    updatePlan: updatePlanMutation,
    deactivatePlan: deactivatePlanMutation,
  } = usePricingPlanGraph()

  // Helper functions
  const toPlanType = (type: string | undefined): string => {
    if (!type) return 'STANDARD'
    const upperType = String(type).toUpperCase()
    if (['STANDARD', 'ENTERPRISE', 'CUSTOM', 'PROMOTIONAL'].includes(upperType)) {
      return upperType
    }
    // Map legacy types to new types
    if (['STARTER', 'PROFESSIONAL'].includes(upperType)) {
      return 'STANDARD'
    }
    return 'STANDARD'
  }

  const normalizeBillingPeriod = (period: string): string => {
    const upperPeriod = String(period).toUpperCase()
    // Valid billing periods from backend enum
    const validPeriods = ['MONTHLY', 'QUARTERLY', 'SEMI_ANNUAL', 'ANNUAL', 'ONE_TIME', 'CUSTOM']
    return validPeriods.includes(upperPeriod) ? upperPeriod : 'MONTHLY'
  }

  // Business logic for creating a pricing plan
  const createPricingPlan = async (planData: any) => {
    try {
      const input = {
        name: planData.name,
        displayName: planData.displayName || planData.name,
        description: planData.description || '',
        price: Number(planData.price || planData.basePrice || 0),
        currency: planData.currency || 'EUR',
        billingPeriod: normalizeBillingPeriod(planData.billingPeriod || 'MONTHLY'),
        durationDays: planData.durationDays || 30,
        isPopular: Boolean(planData.isPopular),
        isCustom: toPlanType(planData.planType) === 'CUSTOM',
        displayOrder: Number(planData.displayOrder || 0),
        isActive: planData.isActive !== false,
        unlimitedJobAdverts: Boolean(planData.unlimitedJobAdverts || planData.jobAdvertLimit === -1),
        hasCustomBranding: Boolean(planData.hasCustomBranding),
        planType: toPlanType(planData.planType),
      }

      const result = await createPlanMutation({ input })
      
      // Refetch plans to update the lists
      await refetchActivePlans()
      
      return result?.data?.createPricingPlan
    } catch (error) {
      console.error('Error creating pricing plan:', error)
      throw error
    }
  }

  // Business logic for updating a pricing plan
  const updatePricingPlan = async (planData: any) => {
    try {
      const input: any = {
        id: planData.id,
      }

      // Only include fields that are being updated
      if (planData.name !== undefined) input.name = planData.name
      if (planData.displayName !== undefined) input.displayName = planData.displayName
      if (planData.description !== undefined) input.description = planData.description
      if (planData.price !== undefined) input.price = Number(planData.price)
      if (planData.currency !== undefined) input.currency = planData.currency
      if (planData.billingPeriod !== undefined) {
        input.billingPeriod = normalizeBillingPeriod(planData.billingPeriod)
      }
      if (planData.durationDays !== undefined) {
        input.durationDays = Number(planData.durationDays)
      }
      if (planData.isPopular !== undefined) input.isPopular = Boolean(planData.isPopular)
      if (planData.isCustom !== undefined) input.isCustom = Boolean(planData.isCustom)
      if (planData.displayOrder !== undefined) {
        input.displayOrder = Number(planData.displayOrder)
      }
      if (planData.isActive !== undefined) input.isActive = Boolean(planData.isActive)
      if (planData.unlimitedJobAdverts !== undefined) {
        input.unlimitedJobAdverts = Boolean(planData.unlimitedJobAdverts)
      }
      if (planData.hasCustomBranding !== undefined) {
        input.hasCustomBranding = Boolean(planData.hasCustomBranding)
      }
      if (planData.planType !== undefined) {
        input.planType = toPlanType(planData.planType)
      }

      const result = await updatePlanMutation({ input })
      
      // Refetch plans to update the lists
      await refetchActivePlans()
      
      return result?.data?.updatePricingPlan
    } catch (error) {
      console.error('Error updating pricing plan:', error)
      throw error
    }
  }

  // Business logic for deactivating a pricing plan
  const deactivatePricingPlan = async (planId: string) => {
    try {
      const result = await deactivatePlanMutation({ id: planId })
      
      // Refetch plans to update the lists
      await refetchActivePlans()
      
      return result?.data?.deactivatePricingPlan
    } catch (error) {
      console.error('Error deactivating pricing plan:', error)
      throw error
    }
  }

  // Load a single plan by ID
  const fetchPlanById = async (id: string) => {
    try {
      await loadPlanById({ id })
      return selectedPlan.value
    } catch (error) {
      console.error('Error fetching pricing plan:', error)
      throw error
    }
  }

  // Load all plans (admin view)
  const fetchAllPlans = async () => {
    try {
      await loadAllPlans()
      return allPlans.value
    } catch (error) {
      console.error('Error fetching all pricing plans:', error)
      throw error
    }
  }

  // Group plans by name for UI display
  const groupedPlans = computed(() => {
    const plans = allPlans.value || []
    const grouped = plans.reduce((acc: any, plan: any) => {
      const key = plan.name
      if (!acc[key]) {
        acc[key] = {
          name: plan.name,
          displayName: plan.displayName,
          description: plan.description,
          planType: plan.planType,
          isActive: plan.isActive,
          plans: [],
        }
      }
      acc[key].plans.push(plan)
      return acc
    }, {})
    return Object.values(grouped)
  })

  return {
    // State
    state: {
      activePlans,
      allPlans,
      selectedPlan,
      groupedPlans,
      loadingActivePlans,
      loadingAllPlans,
      loadingPlanById,
      creatingPlan,
      updatingPlan,
      deactivatingPlan,
    },
    
    // Actions
    actions: {
      createPricingPlan,
      updatePricingPlan,
      deactivatePricingPlan,
      fetchPlanById,
      fetchAllPlans,
      refetchActivePlans,
      refetchAllPlans,
    },
  }
}
