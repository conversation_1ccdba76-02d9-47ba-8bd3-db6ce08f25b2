import { useCompanyUser } from '@/composables/CompanyUsers/useCompanyUser'
import { useAuthStore } from '@/stores/authStore'
import { computed, onMounted, type ComputedRef } from 'vue'

export const usePermissions = () => {
  const authStore = useAuthStore()

  const {
    actions: { loadCompanyUser },
    state: { companyUserInfo, loadingCompanyUser },
  } = useCompanyUser()

  const isSuper: ComputedRef<boolean> = computed(() => {
    return (
      Boolean(authStore?.claims?.isSuperUser) ||
      Boolean(authStore?.claims?.isFGAdmin)
    )
  })

  const permissionsReady = computed(() => {
    // For super users, permissions are always ready
    if (isSuper.value) {
      return true
    }

    return (
      !loadingCompanyUser.value &&
      Object.keys(companyUserInfo.value || {}).length > 0
    )
  })

  onMounted(async () => {
    if (!companyUserInfo.value?.id && !isSuper.value) {
      await loadCompanyUser()
    }
  })

  const permissions = computed(() => {
    const userRights = companyUserInfo.value?.userRights?.[0] || {}

    return {
      canCreateUser: Boolean(userRights.createUser),
      canEditCompany: Boolean(userRights.editCompany),
      isSuperAdmin: Boolean(userRights.superAdmin || isSuper.value),
      canCreateJobAd: Boolean(userRights.createJobAd),
    }
  })

  return {
    state: {
      permissions,
      permissionsReady,
      isLoading: loadingCompanyUser,
    },
  }
}
