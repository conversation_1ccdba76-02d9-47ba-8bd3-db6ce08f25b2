import { useFairJobsGraph } from '@/api/graphHooks/useFairJobsGraph'
import { useAppStore } from '@/stores/appStore'
import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

export const useFairJobs = () => {
  const {
    state: { companyJobsInFair, loadingContactPersonsAndJobsInFair },
    actions: { loadContactPersonsAndJobsInFair },
  } = useCompanyContactPersonGraphData()

  const appStore = useAppStore()

  const {
    state: {
      loadingCreateFairJob,
      loadingDeleteFairJob,
      allFairJobsList,
      loadingAllFairJobs,
    },
    actions: { createFairJob, deleteFairJob, refetchFairJobs, loadFairJobs },
  } = useFairJobsGraph()

  const companyFairJobsInFairList = computed(() => {
    return companyJobsInFair.value?.map((fairJob: any) => ({
      id: fairJob?.id,
      title: fairJob?.fairJob.title,
      description: fairJob?.description,
    }))
  })

  const allFairJobsWithIsInCompany = computed(() => {
    return allFairJobsList.value
      ?.map((fairJob: any) => ({
        title: fairJob?.title,
        id: fairJob?.id,
        description: fairJob?.description,
        isInCompanyFairJobs: companyFairJobsInFairList.value?.some(
          (companyFairJob: any) => companyFairJob.title === fairJob?.title,
        ),
      }))
      .sort(
        (a: any, b: any) =>
          Number(a.isInCompanyFairJobs) - Number(b.isInCompanyFairJobs),
      )
  })

  const handleCreateFairJob = async (title: string) => {
    const newFairJob = await createFairJob({
      input: {
        title,
      },
    })
    appStore.showSnack('Fair job created successfully')

    return newFairJob
  }

  const handleDeleteFairJob = async (id: string) => {
    return await deleteFairJob({
      id,
    })
  }

  return {
    state: {
      companyFairJobsInFairList,
      allFairJobs: allFairJobsWithIsInCompany,
      loadingFairJobs: loadingContactPersonsAndJobsInFair,
      loadingAllFairJobs,
      loadingDeleteFairJob,
      loadingCreateFairJob,
    },
    actions: {
      refetchFairJobs,
      handleCreateFairJob,
      handleDeleteFairJob,
      loadContactPersonsAndJobsInFair,
      loadFairJobs,
    },
  }
}
