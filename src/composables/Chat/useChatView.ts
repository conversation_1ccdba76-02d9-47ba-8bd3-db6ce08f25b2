import { useMutation } from '@vue/apollo-composable'
import { MARK_MESSAGES_AS_SEEN } from '@/api/graphql/mutations/chatMessageMutation'
import { useChatStore } from '@/stores/apiStores/useChatStore'

export const useChatView = () => {
  const chatStore = useChatStore()

  const { mutate: markMessagesAsSeen, loading: updatingMessages } = useMutation(
    MARK_MESSAGES_AS_SEEN,
  )

  const markChatMessagesAsSeen = async (
    applicantId: string,
    chatRoomId: string,
  ) => {
    if (!chatRoomId) {
      console.warn('No chat room ID available')
      return
    }

    try {
      const response = await markMessagesAsSeen({
        markMessagesAsSeenInput: {
          chatRoomId,
          applicantId,
        },
      })

      console.log(
        `Marked ${
          response?.data?.markMessagesAsSeen?.count || 0
        } messages as seen`,
      )

      // Update local state to reflect the changes
      if (chatStore.activeChat?.chat?.messages) {
        chatStore.activeChat.chat.messages.forEach(msg => {
          if (msg.isApplicant && !msg.isSeen) {
            msg.isSeen = true
          }
        })
      }

      return response?.data?.markMessagesAsSeen
    } catch (error) {
      console.error('Failed to mark messages as seen:', error)
      throw error
    }
  }

  return {
    state: {
      updatingMessages,
    },
    actions: {
      markChatMessagesAsSeen,
    },
  }
}
