import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
import { useStaleImage } from '@/composables/StaleImage/useStaleImage'
import { newCompanySaveSuccess } from '@/composables/useSweetAlert'
import { Company } from '@/gql/graphql'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
import { useCompanyStore } from '@/stores/companyStore'
import { deleteTextImage } from '@/libs/firebase/upload-text-image'
import { inlineTranslate } from '@/utils/utils'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/appStore'
import { useFairs } from '@/composables/Fairs/useFairs'

export const useCompany = () => {
  const {
    state: {
      allCompanies,
      paginatedCompanies,
      loadingAllCompanies,
      loadingPaginatedCompanies,
      loadingCompaniesForFairList,
      saveCompanyLoading,
      updateCompanyLoading,
      loadingManageFair,
    },
    actions: {
      saveCompany,
      saveCompanyByAdmin,
      updateCompany,
      removeCompany,
      loadAllCompanies,
      loadPaginatedCompanies,
      updatePaginationParams,
      loadCompaniesForFairList,
      manageFair,
    },
  } = useCompanyGraph()

  const {
    actions: { handleRemoveMultipleStaleImages },
  } = useStaleImage()

  const route = useRoute()

  const router = useRouter()
  const companyStore = useCompanyStore()
  const companyFormData = useCompanyFormStore()
  const authStore = useAuthStore()
  const appStore = useAppStore()

  const cid = computed(() => {
    return route && route.params
      ? route.params?.cid
      : companyStore.getCompany?.id
  })

  let allCompaniesForFair = ref([])

  const {
    actions: { setCustomClaims },
  } = useAuthGraph()

  const createNewCompany = async (dark: boolean) => {
    try {
      await handleRemoveMultipleStaleImages()
      await companyStore.uploadCompanyImages(companyFormData)
      const newCompany = await saveCompany()
      companyStore.updateCompany(newCompany?.data.createCompany)
      await setCustomClaims()

      const doneAlert = await newCompanySaveSuccess(dark)
      if (doneAlert?.isConfirmed) {
        await authStore.init()
      }
      return newCompany
    } catch (e) {
      console.log(e)
    }
  }
  const createNewCompanyByAdmin = async (dark: boolean) => {
    try {
      await handleRemoveMultipleStaleImages()
      await companyStore.uploadCompanyImages(companyFormData)
      const newCompany = await saveCompanyByAdmin()
      companyStore.updateCompany(newCompany?.data.createCompany)

      const doneAlert = await newCompanySaveSuccess(dark)
      if (doneAlert?.isConfirmed) {
        await authStore.init()
      }
      return newCompany
    } catch (e) {
      console.log(e)
    }
  }

  const handleUpdateCompany = async () => {
    const userDetailsString = localStorage.getItem('userDetails')

    const userDetails = userDetailsString ? JSON.parse(userDetailsString) : null

    const authStore = useAuthStore()
    const companyUserId =
      authStore?.claims?.companyUserId || userDetails?.companyUserId

    const imagesToDelete = companyFormData?.imagesToDelete

    //check if a new image was uploaded
    const logoChangedUrl = companyFormData?.logoResult?.imageUrl
    const headerChangedUrl = companyFormData?.headerResult?.imageUrl

    if (logoChangedUrl || headerChangedUrl) {
      await companyStore.uploadCompanyImages(companyFormData)
    }

    const variables = {
      id: companyStore.getCompany?.id || cid.value,
      companyInput: {
        companyUserId: companyUserId,
        name: companyFormData.companyName,
        city: companyFormData.city,
        country: companyFormData.address?.split(',').pop(),
        address: companyFormData.address,
        detailContent: companyFormData.detailContent,
        foundingYear: companyFormData?.date,
        headerImageUrl: companyFormData?.headerSavedUrl,
        logoImageUrl: companyFormData?.logoSavedUrl,
        latitude: companyFormData?.position?.lat,
        longitude: companyFormData?.position?.long,
        totalEmployees: companyFormData.mitarbeiter,
      },
    }

    const updatedCompany = await updateCompany(variables)
    await handleRemoveMultipleStaleImages()

    for (const url of imagesToDelete) {
      await deleteTextImage(url)
    }
    return updatedCompany
  }

  const updateCompanyDynamicLink = async (url: String) => {
    return await updateCompany({
      companyInput: {
        dynamicLink: url,
      },
    })
  }

  const removeCompanyById = async (id: string) => {
    let companyRemoved = null
    try {
      companyRemoved = await removeCompany({ id })
    } catch (e) {
      console.log(e)
    }
    return companyRemoved
  }

  const handleManageFair = async (isManaged: boolean) => {
    const companyId = authStore.companyId
    if (!companyId) {
      console.error('Company ID not found in auth store.')
      return
    }
    const manageFairRes = await manageFair({
      companyId,
      isFairManaged: isManaged,
    })
    companyStore.updateCompanyManagedStatus(isManaged)
    console.log({ manageFairRes })
  }

  const allCompaniesHeader = computed(() => {
    return [
      {
        title: inlineTranslate('Company'),
        sortable: false,
        key: 'name',
      },
      {
        title: inlineTranslate('City'),
        sortable: true,
        key: 'city',
      },
      {
        title: inlineTranslate('Image'),
        sortable: true,
        key: 'logoImageUrl',
      },
      {
        title: 'Actions',
        key: 'actions',
      },
    ]
  })

  const handleCompanyClicked = async (e: any, company: Company) => {
    await authStore.setCompanyId(company.id)
    companyStore.updateCompany(company)
    await appStore.setInCompanyView(true)

    await router.push({
      name: 'super-company-cid',
      params: { cid: company.id },
    })
  }

  return {
    state: {
      allCompaniesHeader,
      allCompanies,
      paginatedCompanies,
      allCompaniesForFair,
      loadingAllCompanies,
      loadingManageFair,
      loadingPaginatedCompanies,
      loadingCompaniesForFairList,
      saveCompanyLoading,
      updateCompanyLoading,
    },
    actions: {
      updateCompanyDynamicLink,
      createNewCompanyByAdmin,
      handleUpdateCompany,
      handleCompanyClicked,
      handleManageFair,
      createNewCompany,
      removeCompanyById,
      loadAllCompanies,
      loadPaginatedCompanies,
      updatePaginationParams,
      loadCompaniesForFairList,
    },
  }
}
