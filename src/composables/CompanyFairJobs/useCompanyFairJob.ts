import { useCompanyFairJobGraph } from '@/api/graphHooks/useCompanyFairJobGraph'
import { useFairGraph } from '@/api/graphHooks/useFairGraph'
import { useCompanyStore } from '@/stores/companyStore'
import { useAppStore } from '@/stores/appStore'
import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

export const useCompanyFairJob = () => {
  const {
    state: {
      loadingCompanyCreateFairJob,
      loadingCompanyDeleteFairJob,
      loadingCompanyUpdateFairJob,
    },
    actions: {
      createCompanyFairJob,
      updateCompanyFairJob,
      deleteCompanyFairJob,
    },
  } = useCompanyFairJobGraph()

  const {
    state: { fairParticipationByFairAndCompany },
  } = useCompanyContactPersonGraphData()

  const companyStore = useCompanyStore()
  const appStore = useAppStore()

  const handleCreateCompanyFairJob = async (inputData: any) => {
    if (!inputData.fairJobId) {
      console.error('Invalid inputData for createCompanyFairJob')
      return
    }
    const newCompanyFairJobData = {
      input: {
        companyFairParticipationId: fairParticipationByFairAndCompany.value?.id,
        fairJobId: inputData.fairJobId,
      },
    }

    const newCompanyFairJob = await createCompanyFairJob(newCompanyFairJobData)
    console.log('newCompanyFairJob::', newCompanyFairJob)
    return newCompanyFairJob
  }
  const handleUpdateCompanyFairJob = async () => {
    try {
      if (!companyStore.companyFairJobEditDialogData?.description) {
        throw new Error('Description is required')
      }

      const inputData = {
        input: {
          id: companyStore.companyFairJobEditDialogData?.id,
          description: companyStore.companyFairJobEditDialogData?.description,
        },
      }

      const updatedCompanyFairJob = await updateCompanyFairJob(inputData)
      console.log({ updatedCompanyFairJob })
      appStore.showSnack('Fair job updated successfully')
      return updatedCompanyFairJob
    } catch (error) {
      console.error('Error updating company fair job:', error)
      appStore.showSnack('Failed to update fair job')
      throw error
    }
  }

  const handleDeleteCompanyFairJob = async (id: string) => {
    return await deleteCompanyFairJob({ id })
  }

  return {
    state: {
      loadingCompanyCreateFairJob,
      loadingCompanyDeleteFairJob,
      loadingCompanyUpdateFairJob,
    },
    actions: {
      handleDeleteCompanyFairJob,
      handleCreateCompanyFairJob,
      handleUpdateCompanyFairJob,
    },
  }
}
