import { useStaleImagesGraph } from '@/api/graphHooks/useStaleImagesGraph'
import { CreateStaleImageInput } from '@/gql/graphql'
import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'

export const useStaleImage = () => {
  const {
    state: { loadingCreateStaleImage, loadingRemoveMultipleStaleImgs },
    actions: { createStaleImage, removeMultipleStaleImgs },
  } = useStaleImagesGraph()

  const companyFormStore = useCompanyFormStore()
  const jobAdFormStore = useJobAdFormStore()

  const handleCreateStaleImage = async (
    imgUrl: CreateStaleImageInput,
  ): Promise<string> => {
    const saveStaleImage = await createStaleImage({
      createStaleImageInput: imgUrl,
    })
    return saveStaleImage?.data?.createStaleImage?.id
  }

  const handleRemoveMultipleStaleImages = async () => {
    const ids: string[] = [
      ...companyFormStore.staleImgIds,
      ...jobAdFormStore.staleImgIds,
    ]
    if (ids.length === 0) return console.log('No stale images to remove')
    const removeStaleImages = await removeMultipleStaleImgs({
      ids,
    })
    return removeStaleImages?.data?.removeMultipleStaleImages
  }

  return {
    state: {
      loadingCreateStaleImage,
    },
    actions: {
      handleCreateStaleImage,
      handleRemoveMultipleStaleImages,
    },
  }
}
