import { useFairGraph } from '@/api/graphHooks/useFairGraph'
import { useCompanyFairContactPersonGraph } from '@/api/graphHooks/useCompanyFairContactPersonGraph'
import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

export const useCompanyFairContactPerson = () => {
  const {
    state: {
      loadingCompanyCreateFairContactPerson,
      loadingCompanyDeleteFairContactPersons,
    },
    actions: { createCompanyFairContactPerson, deleteCompanyFairContactPerson },
  } = useCompanyFairContactPersonGraph()

  const {
    state: { fairParticipationByFairAndCompany },
  } = useCompanyContactPersonGraphData()

  const handleCreateCompanyFairContactPerson = async (inputData: any) => {
    if (!inputData.id) {
      console.error('Invalid inputData for createCompanyFairContactPerson')
      return
    }
    const newCompanyFairContactPersonData = {
      input: {
        companyFairParticipationId: fairParticipationByFairAndCompany.value?.id,
        contactPersonId: inputData.id,
      },
    }

    return await createCompanyFairContactPerson(newCompanyFairContactPersonData)
  }

  const handleDeleteCompanyFairContactPerson = async (id: string) => {
    return await deleteCompanyFairContactPerson({ id })
  }

  return {
    state: {
      loadingCompanyCreateFairContactPerson,
      loadingCompanyDeleteFairContactPersons,
    },
    actions: {
      handleCreateCompanyFairContactPerson,
      handleDeleteCompanyFairContactPerson,
    },
  }
}
