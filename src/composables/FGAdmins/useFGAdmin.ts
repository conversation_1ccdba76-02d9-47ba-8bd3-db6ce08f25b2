import { useAppStore } from '@/stores/appStore'
import { useFGAdminGraph } from '@/api/graphHooks/useFGAdminGraph'

export const useFGAdmin = () => {
  const {
    state: { verifyFGAdminLoading },
    actions: { verifyFGAdmin },
  } = useFGAdminGraph()

  const appStore = useAppStore()

  const isFGAdmin = async (email: string) => {
    try {
      const fgAdmin = await verifyFGAdmin({
        email,
      })

      if (fgAdmin) {
        return true
      }
    } catch (error) {
      console.error(error)
      appStore.showSnack(
        `Entschuldigung, Benutzer mit der E-Mail-Adresse “${email}” wurde nicht gefunden. 😊`,
      )
      return false
    }
  }

  return {
    state: {
      verifyFGAdminLoading,
    },
    actions: {
      isFGAdmin,
    },
  }
}
