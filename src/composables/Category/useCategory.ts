import { useCategoryGraph } from '@/api/graphHooks/useCategoryGraph'
import { useJobAdsStore } from '@/stores/jobAdsStore'

export const useCategory = () => {
  const {
    state: { categoryList, loadingCategoryList },
    actions: { refetchCategory, loadCategories },
  } = useCategoryGraph()

  const jobAdsStore = useJobAdsStore()

  const setStoreCategory = async () => {
    await loadCategories()
    // await jobAdsStore.setCategory(categoryList.value)
  }

  return {
    state: {
      categoryList,
      loadingCategoryList,
    },
    actions: {
      setStoreCategory,
      loadCategories,
    },
  }
}
