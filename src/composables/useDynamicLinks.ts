import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { useDynamicLinkGraph } from '@/api/graphHooks/useDynamicLinkGraph'

/**
 * Returns the dynamic link of the currently signed in users company.
 * Checks first, if link is already created and stored in DB, otherwise creates one on the fly.
 */

export const useDynamicLinks = () => {
  const {
    state: { companyLinkLoading, jobAdLinkLoading },
    actions: { getCompanyDynamicLink, getJobAdDynamicLink },
  } = useDynamicLinkGraph()

  const getDynamicLinkForActiveCompany = async (): Promise<string> => {
    const authStore = useAuthStore()
    const companyStore = useCompanyStore()
    const companyId = authStore.companyId
    const dynamicLink = await getCompanyDynamicLink({
      createDynamicLinkInput: {
        companyId: companyId,
      },
    })

    companyStore.updateDynamicLink(
      dynamicLink?.data?.createCompanyDynamicLink?.url,
    )

    return dynamicLink?.data?.createCompanyDynamicLink?.url
  }

  const getDynamicLinkForJobAd = async (
    jobAdId: string | undefined,
  ): Promise<string> => {
    if (!jobAdId) {
      return ''
    }
    const dynamicLink = await getJobAdDynamicLink({
      createDynamicLinkInput: {
        jobAdvertId: jobAdId,
      },
    })

    return dynamicLink?.data?.createJobAdDynamicLink?.url
  }

  return {
    state: {
      companyLinkLoading,
      jobAdLinkLoading,
    },
    actions: {
      getDynamicLinkForActiveCompany,
      getDynamicLinkForJobAd,
    },
  }
}
