import { ref, computed } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { useAuthStore } from '@/stores/authStore'

const GET_COMPANY_SUBSCRIPTIONS = gql`
  query GetCompanySubscriptions($companyId: String!) {
    subscriptionsByCompanyId(companyId: $companyId) {
      id
      subscriptionType
      isActive
      status
      expiresAt
      currentPeriodEnd
      stripePriceId
    }
  }
`

export const useUnlimitedSubscriptionStatus = () => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()
  const hasActiveUnlimitedSubscription = ref(false)
  const unlimitedSubscriptionDetails = ref<any>(null)

  // Query to get company subscriptions
  const { result, loading, error, refetch } = useQuery(
    GET_COMPANY_SUBSCRIPTIONS,
    () => ({
      companyId: authStore.companyId,
    }),
    {
      enabled: computed(() => !!authStore.companyId),
      fetchPolicy: 'cache-and-network',
    },
  )

  // Check if company has active unlimited subscription
  const checkUnlimitedStatus = computed(() => {
    if (!result.value?.subscriptionsByCompanyId) {
      return false
    }

    const subscriptions = result.value.subscriptionsByCompanyId
    const unlimitedSub = subscriptions.find(
      (sub: any) =>
        sub.subscriptionType === 'COMPANY_UNLIMITED' &&
        sub.isActive === true &&
        sub.status === 'active',
    )

    if (unlimitedSub) {
      hasActiveUnlimitedSubscription.value = true
      unlimitedSubscriptionDetails.value = unlimitedSub

      const expiresAt = new Date(unlimitedSub.expiresAt)
      const now = new Date()

      return expiresAt > now
    }

    hasActiveUnlimitedSubscription.value = false
    unlimitedSubscriptionDetails.value = null
    return false
  })

  // Get remaining days for unlimited subscription
  const remainingDays = computed(() => {
    if (!unlimitedSubscriptionDetails.value?.expiresAt) {
      return 0
    }

    const expiresAt = new Date(unlimitedSubscriptionDetails.value.expiresAt)
    const now = new Date()
    const diffTime = Math.abs(expiresAt.getTime() - now.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  })

  // Check if unlimited subscription is about to expire (less than 7 days)
  const isExpiringSort = computed(() => {
    return remainingDays.value > 0 && remainingDays.value <= 7
  })

  return {
    hasActiveUnlimitedSubscription: checkUnlimitedStatus,
    unlimitedSubscriptionDetails,
    remainingDays,
    isExpiringSort,
    loading,
    error,
    refetch,
  }
}
