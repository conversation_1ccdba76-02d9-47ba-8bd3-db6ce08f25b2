import { useSubscriptionGraph } from '@/api/graphHooks/useSubscriptionGraph'
import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
import { useJobAdvertGraph } from '@/api/graphHooks/useJobAdvertGraph'
import {
  confirmSubscriptionCancel,
  subscriptionCancelledAlert,
  confirmSubscriptionRenew,
  subscriptionRenewAlert,
} from '@/composables/useSweetAlert'
import {
  PROD_STRIPE_PRODUCT_PRICE_ID,
  TEST_STRIPE_PRODUCT_PRICE_ID,
} from '@/constants/app'
import { StripeSubscription } from '@/gql/graphql'
import router from '@/router'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
import { useRoute } from 'vue-router'
import { useCompanyDetailsGraphData } from '@/api/graphHooks/company/useCompanyDetailsGraphData'

export const useStripeSubscription = () => {
  const route = useRoute()
  const jobAdId = computed(() => {
    return route.query?.ad
  })
  const jobAdTitle = computed(() => {
    return route.query?.title
  })

  const appStore = useAppStore()

  const {
    state: {
      loadingSubscribeToPremium,
      loadingCheckoutSession,
      subscriptionList,
      paymentMethods,
      stripeCustomer,
      loadingFetchCoupon,
      loadingPaymentMethods,
      loadingSubscription,
      loadingPortalSession,
      loadingUpdateStripeCustomer,
      loadingCreateStripeCustomer,
      loadingSetupCheckout,
      loadingCustomer,
      loadingFetchPromoCode,
    },
    actions: {
      subscribeToPremium,
      refetchCustomer,
      fetchCoupon,
      fetchPromoCode,
      createPortalSession,
      createCheckoutSession,
      createSetupCheckout,
      generateSubInvoice,
      refetchSubscription,
      refetchPaymentMethods,
      createStripeCustomer,
      updateStripeCustomer,
      cancelSubscription,
      resumeSubscription,
      generateInvoice,
      loadSubscription,
      loadCustomer,
      loadPaymentMethods,
    },
  } = useSubscriptionGraph()

  const {
    state: { companyDetails },
    actions: { refetchCompany },
  } = useCompanyDetailsGraphData()

  const {
    actions: { refetchJobAds },
  } = useJobAdverts()

  const authStore = useAuthStore()

  const subStore = useSubscriptionStore()

  const companyStore = useCompanyStore()

  const customerFormStore = useCustomerFormStore()

  const {
    actions: { loadPaginatedJobAds, loadJobAdsByCompany },
  } = useJobAdvertGraph()

  const promoCode = computed(() => {
    return subStore.promoCode
  })

  const selectedJobAds = computed(() => {
    if (subStore.getJobAdverts) {
      return subStore.getJobAdverts
    } else {
      return []
    }
  })

  const stripeCustomerId = computed(() => {
    return (
      companyStore?.getCompany?.stripeCustomerId ||
      companyStore?.company?.stripeCustomerId ||
      companyDetails.value.stripeCustomerId
    )
  })

  const getPortalSession = async () => {
    await refetchCompany()

    const sessionUrlData = await createPortalSession({
      createCheckoutSessionInput: {
        customerId: stripeCustomerId.value,
      },
    })

    window.location.href = sessionUrlData?.data?.createPortalSession?.url
  }

  const getSetupCheckoutUrl = async (type: string) => {
    if (!stripeCustomerId.value) {
      appStore.showSnack('No customer found. Please add a customer first.')
      return
    }

    const sessionUrlData = await createSetupCheckout({
      companyId: authStore.companyId,
      customer:
        stripeCustomerId.value ||
        companyDetails.value.stripeCustomerId ||
        undefined,
      type: type,
    })

    return sessionUrlData?.data?.createSetupCheckoutDetails?.url
  }

  const handleSubscribeToPremium = async () => {
    try {
      const jobAdWithTitle = localStorage.getItem('jobAdWithTitle')
      const parts = jobAdWithTitle?.split('|')
      const storedTitle = parts?.[1]
      const storedId = parts?.[0]

      // Check local storage for checkout info
      const checkoutObj = localStorage.getItem('checkout')
      const obj = checkoutObj ? JSON.parse(checkoutObj) : null
      const customerId = localStorage.getItem('stripeCustomerId')
      appStore.showAppLoader()

      const subResponse = await subscribeToPremium({
        paymentMethodId: subStore.getSelectedPaymentMethod?.id || '',
        createStripeSubscriptionInput: {
          jobAdvertId: jobAdId.value || obj?.jobAdId || storedId,
          companyId: authStore.companyId,
          couponId: subStore.getCouponDetails?.id,
          promoCode: subStore.getPromoCodeDetails?.id,
          jobAdverts:
            selectedJobAds.value.length > 1
              ? selectedJobAds.value
              : obj?.jobAdverts,
          advertTitle: jobAdTitle.value || obj?.jobAdTitle || storedTitle,
          customerId:
            stripeCustomerId.value ||
            companyDetails.value.stripeCustomerId ||
            customerId,
          priceId: TEST_STRIPE_PRODUCT_PRICE_ID,
        },
      })

      await loadJobAdsByCompany()
      await refetchJobAds()

      if (subResponse?.data?.subscribeToPremium?.status === 'succeeded') {
        appStore.hideAppLoader()
        appStore.showSnack('Erfolgreich gebucht!')
        await router.push({
          name: 'company-subscription-landing',
          query: { success: 'true' },
        })
      }

      if (subResponse?.data?.subscribeToPremium?.status === 'processing') {
        appStore.hideAppLoader()
        appStore.showSnack('Subscription processing!')
        await router.push({
          name: 'company-subscription-landing',
          query: { success: 'true' },
        })
      }

      subStore.removePromoCode()

      return subResponse?.data?.subscribeToPremium
    } catch (error: any) {
      appStore.hideAppLoader()
      appStore.showSnack(`Subscription failed. ${error.message}.`)
      throw error
    }
  }

  const handleGenerateSubInvoice = async () => {
    const jobAdWithTitle = localStorage.getItem('jobAdWithTitle')
    const parts = jobAdWithTitle?.split('|')
    const storedTitle = parts?.[1]
    const storedId = parts?.[0]

    // Check local storage for checkout info
    const checkoutObj = localStorage.getItem('checkout')
    const obj = checkoutObj ? JSON.parse(checkoutObj) : null
    const customerId = localStorage.getItem('stripeCustomerId')
    appStore.showAppLoader()

    try {
      const subResponse = await generateSubInvoice({
        createStripeSubscriptionInput: {
          jobAdvertId: jobAdId.value || obj?.jobAdId || storedId,
          companyId: authStore.companyId,
          couponId: subStore.getCouponDetails?.id,
          promoCode: subStore.getPromoCodeDetails?.id,
          jobAdverts:
            selectedJobAds.value.length > 1
              ? selectedJobAds.value
              : obj?.jobAdverts,
          advertTitle: jobAdTitle.value || obj?.jobAdTitle || storedTitle,
          customerId:
            stripeCustomerId.value ||
            companyDetails.value.stripeCustomerId ||
            customerId,
          priceId: PROD_STRIPE_PRODUCT_PRICE_ID,
        },
        customerDetails: {
          email: customerFormStore.email,
          name: customerFormStore.name,
          address: {
            line1: customerFormStore.addressLine1,
            line2: customerFormStore.addressLine2,
            city: customerFormStore.city,
            country: customerFormStore?.country,
            postal_code: customerFormStore.postalCode,
          },
        },
      })

      await loadJobAdsByCompany()
      await refetchJobAds()

      appStore.hideAppLoader()
      appStore.showSnack(
        `Die Rechnung wurde erstellt und an ${customerFormStore.email} gesendet `,
      )

      await router.push({
        name: 'company-subscription-landing',
        query: { success: 'true' },
      })

      return subResponse?.data?.generateSubInvoice
    } catch (error: any) {
      appStore.hideAppLoader()
      appStore.showSnack(`Fehler beim Erstellen der Rechnung: ${error.message}`)
      console.error('Error generating invoice:', error)
      return null
    }
  }

  const handleCreateStripeCustomer = async (customer: {
    name: string
    email: string
  }) => {
    try {
      const customerUpdated = await createStripeCustomer({
        companyId: authStore.companyId,
        createStripeCustomerInput: {
          email: customer.email,
          name: customer?.name,
          address: null,
        },
      })

      if (customerUpdated?.data?.createStripeCustomer) {
        companyStore.updateStripeCustomerId(
          customerUpdated?.data?.createStripeCustomer?.id,
        )

        // Manually trigger data loading after mutation completes
        await loadCustomer()
      }
      appStore.showSnack(`Customer Created`)
      localStorage.setItem(
        'stripeCustomerId',
        customerUpdated?.data?.createStripeCustomer?.id,
      )

      return customerUpdated?.data?.createStripeCustomer
    } catch (e) {
      appStore.showSnack(`Failed to create customer: ${e}`)
    }
  }

  const handleUpdateStripeCustomer = async (customer: {
    name: string
    email: string
    id: string
  }) => {
    try {
      const customerUpdated = await updateStripeCustomer({
        customerId: customer?.id,
        createStripeCustomerInput: {
          email: customer.email,
          name: customer?.name,
        },
      })

      if (customerUpdated?.data?.updateStripeCustomer) {
        // Manually trigger data loading after mutation completes
        await loadCustomer()
      }

      appStore.showSnack(`Customer Updated`)
    } catch (e) {
      appStore.showSnack(`Failed to update customer: ${e}`)
    }
  }

  const handleCancelSubscription = async (subId: string, dark: boolean) => {
    const result = await confirmSubscriptionCancel(dark)
    if (result.isConfirmed) {
      const cancelled = await cancelSubscription({
        subscriptionIdInput: { subscriptionId: subId },
      })

      if (cancelled?.data?.cancelSubscription?.cancel_at_period_end) {
        subscriptionCancelledAlert(dark)

        // Manually trigger data loading after mutation completes
        await loadSubscription()

        const {
          actions: { loadPaginatedJobAds, loadJobAdsByCompany },
        } = useJobAdvertGraph()

        // Load job ads data
        await loadPaginatedJobAds()
        await loadJobAdsByCompany()
        await refetchJobAds()
      }
    } else {
      appStore.showSnack(`Cancel action aborted!`)
    }
  }

  const handleResumeSubscription = async (subId: string, dark: boolean) => {
    const result = await confirmSubscriptionRenew(dark)
    if (result.isConfirmed) {
      const resumed = await resumeSubscription({
        subscriptionIdInput: { subscriptionId: subId },
      })

      if (resumed?.data?.resumeSubscription?.cancel_at_period_end === false) {
        subscriptionRenewAlert(dark)

        // Manually trigger data loading after mutation completes
        await loadSubscription()

        // Load job ads data
        await loadPaginatedJobAds()
        await loadJobAdsByCompany()
        await refetchJobAds()
      }
    } else {
      appStore.showSnack(`Resume action aborted!`)
    }
  }

  const handleGenerateInvoice = async (invoiceId: string) => {
    const invoice = await generateInvoice({
      invoiceIdInput: { invoiceId: invoiceId },
    })

    window.location.href =
      invoice?.data?.retrieveSubscriptionInvoice?.hosted_invoice_url
  }

  // const handleFetchCoupon = async () => {
  //   if (!promoCode.value) {
  //     return
  //   }
  //   const no_jobs = subStore?.jobAdverts?.length || 0
  //
  //   try {
  //     const couponInfo = await fetchCoupon({
  //       promoCode: promoCode.value,
  //     })
  //
  //     if (couponInfo?.data?.retrieveStripeCoupon?.max_redemptions < no_jobs) {
  //       appStore.showSnack(
  //         `Der Gutschein kann nur auf ${couponInfo?.data?.retrieveStripeCoupon?.max_redemptions} Stellenanzeigen angewendet werden.`,
  //       )
  //       return
  //     }
  //     if (!couponInfo?.data?.retrieveStripeCoupon.valid) {
  //       appStore.showSnack('Der Gutschein wurde bereits eingelöst')
  //       subStore.updateCouponDetails({
  //         ...couponInfo?.data?.retrieveStripeCoupon,
  //         percent_off: 0,
  //         valid: false,
  //         redeemed: true,
  //       })
  //       return
  //     }
  //
  //     if (couponInfo?.data?.retrieveStripeCoupon) {
  //       subStore.updateCouponDetails(couponInfo?.data?.retrieveStripeCoupon)
  //     }
  //   } catch (e) {
  //     appStore.showSnack('Ungültiger Code')
  //     subStore.removePromoCode()
  //   }
  // }

  const handleFetchCoupon = async () => {
    if (!promoCode.value) {
      return
    }

    const no_jobs = subStore?.jobAdverts?.length || 0

    try {
      const couponInfo = await fetchCoupon({
        promoCode: promoCode.value,
      })

      const coupon = couponInfo?.data?.retrieveStripeCoupon

      if (!coupon) {
        appStore.showSnack('Ungültiger Code')
        subStore.removePromoCode()
        return
      }

      if (
        coupon.max_redemptions !== null &&
        coupon.max_redemptions !== undefined
      ) {
        if (coupon.max_redemptions < no_jobs) {
          appStore.showSnack(
            `Der Gutschein kann nur auf ${coupon.max_redemptions} Stellenanzeigen angewendet werden.`,
          )
          return
        }
      }

      if (!coupon.valid) {
        appStore.showSnack('Der Gutschein wurde bereits eingelöst')
        subStore.updateCouponDetails({
          ...coupon,
          percent_off: 0,
          valid: false,
          redeemed: true,
        })
        return
      }

      subStore.updateCouponDetails(coupon)
    } catch (e) {
      appStore.showSnack('Ungültiger Code')
      subStore.removePromoCode()
    }
  }

  const handleFetchPromoCode = async () => {
    if (!promoCode.value) {
      return
    }

    const no_jobs = subStore?.jobAdverts?.length || 0

    try {
      const promoCodeInfo = await fetchPromoCode({
        promoCode: promoCode.value,
      })

      const promoCodeDetails = promoCodeInfo?.data?.retrieveStripePromoCode

      const coupon = promoCodeInfo?.data?.retrieveStripePromoCode?.coupon

      if (!coupon) {
        appStore.showSnack('Ungültiger Code')
        subStore.removePromoCode()
        return
      }

      if (
        promoCodeDetails.max_redemptions !== null &&
        promoCodeDetails.max_redemptions !== undefined
      ) {
        if (promoCodeDetails.max_redemptions < no_jobs) {
          appStore.showSnack(
            `Der Gutschein kann nur auf ${promoCodeDetails.max_redemptions} Stellenanzeigen angewendet werden.`,
          )
          return
        }
      }

      if (!coupon.valid) {
        appStore.showSnack('Der Gutschein wurde bereits eingelöst')
        subStore.updateCouponDetails({
          ...coupon,
          percent_off: 0,
          valid: false,
          redeemed: true,
        })
        return
      }

      subStore.updateCouponDetails(coupon)
      subStore.updatePromoCodeDetails(promoCodeDetails)
    } catch (e) {
      appStore.showSnack('Ungültiger Code')
      subStore.removePromoCode()
    }
  }

  const copyInvoiceLink = async (invoiceId: string) => {
    try {
      const invoice = await generateInvoice({
        invoiceIdInput: { invoiceId: invoiceId },
      })
      await navigator.clipboard.writeText(
        invoice?.data?.retrieveSubscriptionInvoice?.hosted_invoice_url,
      )
      console.log('Text copied to clipboard')
      appStore.showSnack('Text copied to clipboard')
    } catch (e) {
      console.error('Failed to copy: ', e)
      appStore.showSnack('Failed to copy to clipboard')
    }
  }

  const handleDownloadInvoice = async (invoiceId: string) => {
    console.log({ invoiceId })
    const invoice = await generateInvoice({
      invoiceIdInput: { invoiceId: invoiceId },
    })
    window.location.href =
      invoice?.data?.retrieveSubscriptionInvoice.invoice_pdf
  }

  const handleRowClicked = async (e: any, subscription: StripeSubscription) => {
    console.log({ stripeSubs: subscription })
  }

  return {
    state: {
      subscriptionList,
      stripeCustomer,
      paymentMethods,
      loadingCustomer,
      loadingPaymentMethods,
      loadingSubscription,
      loadingFetchCoupon,
      loadingSubscribeToPremium,
      loadingCheckoutSession,
      loadingPortalSession,
      loadingSetupCheckout,
      loadingUpdateStripeCustomer,
      loadingCreateStripeCustomer,
    },
    actions: {
      handleUpdateStripeCustomer,
      handleCreateStripeCustomer,
      handleDownloadInvoice,
      handleResumeSubscription,
      handleCancelSubscription,
      handleSubscribeToPremium,
      handleGenerateSubInvoice,
      handleGenerateInvoice,
      getSetupCheckoutUrl,
      getPortalSession,
      copyInvoiceLink,
      handleFetchCoupon,
      handleFetchPromoCode,
      refetchSubscription,
      refetchPaymentMethods,
      refetchCustomer,
      handleRowClicked,
      loadSubscription,
      loadCustomer,
      loadPaymentMethods,
    },
  }
}
