import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useCompanyStore } from '@/stores/companyStore'
import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
import { useCustomerFormStore } from '@/stores/customer-form/customerForm'
import { useStripeSubscription } from './useStripeSubscription'
import { 
  CREATE_UNLIMITED_SUBSCRIPTION,
  GENERATE_UNLIMITED_SUBSCRIPTION_INVOICE,
  ACTIVATE_ALL_COMPANY_JOB_ADVERTS,
  SYNC_UNLIMITED_SUBSCRIPTION
} from '@/api/graphql/mutations/unlimitedSubscriptionMutation'
import { useMutation } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { provideApolloClient } from '@vue/apollo-composable'

export const useUnlimitedSubscription = () => {
  provideApolloClient(apolloClient)
  
  const router = useRouter()
  const appStore = useAppStore()
  const authStore = useAuthStore()
  const companyStore = useCompanyStore()
  const subscriptionStore = useSubscriptionStore()
  const customerFormStore = useCustomerFormStore()
  
  // Get the base stripe subscription methods
  const {
    state: {
      stripeCustomer,
      paymentMethods,
      loadingPaymentMethods,
    },
    actions: {
      loadCustomer,
      loadPaymentMethods,
      handleFetchPromoCode,
    }
  } = useStripeSubscription()

  // Mutations for unlimited subscription
  const { mutate: createUnlimitedSubscription, loading: loadingCreateUnlimited } = 
    useMutation(CREATE_UNLIMITED_SUBSCRIPTION)
    
  const { mutate: generateUnlimitedInvoice, loading: loadingGenerateInvoice } = 
    useMutation(GENERATE_UNLIMITED_SUBSCRIPTION_INVOICE)
    
  const { mutate: activateAllJobAdverts, loading: loadingActivateJobs } = 
    useMutation(ACTIVATE_ALL_COMPANY_JOB_ADVERTS)
    
  const { mutate: syncUnlimitedSubscription, loading: loadingSyncSubscription } = 
    useMutation(SYNC_UNLIMITED_SUBSCRIPTION)

  // Get the active pricing plan from subscription config
  const activePricingPlan = computed(() => {
    const plans = subscriptionStore.availablePricingPlans
    if (!plans || plans.length === 0) return null
    
    // Get the plan ID from route query or use first available
    const route = router.currentRoute.value
    const planId = route.query.planId as string
    
    if (planId) {
      return plans.find((plan: any) => plan.id === planId) || plans[0]
    }
    
    return plans[0]
  })

  // Subscribe to unlimited plan with card payment
  const handleSubscribeToUnlimited = async () => {
    try {
      const plan = activePricingPlan.value
      if (!plan) {
        appStore.showSnack('No pricing plan available', 'error')
        return
      }

      const selectedPm = subscriptionStore.getSelectedPaymentMethod
      if (!selectedPm?.id) {
        appStore.showSnack('Please select a payment method', 'error')
        return
      }

      appStore.showAppLoader()

      const response = await createUnlimitedSubscription({
        companyId: authStore.companyId,
        priceId: plan.stripePriceId || plan.priceId,
        customerId: stripeCustomer.value?.id || companyStore?.getCompany?.stripeCustomerId,
        durationDays: plan.durationDays,
        monthlyPrice: plan.price,
        paymentMethodId: selectedPm.id,
        couponId: subscriptionStore.getCouponDetails?.id,
        promoCode: subscriptionStore.promoCode,
        currency: plan.currency || 'EUR'
      })

      if (response?.data?.createUnlimitedSubscription?.id) {
        // Activate all existing job adverts for this company
        await activateAllJobAdverts({ 
          companyId: authStore.companyId 
        })

        appStore.hideAppLoader()
        appStore.showSnack('Unlimited subscription activated successfully!')
        
        // Clear promo code if used
        subscriptionStore.removePromoCode()
        
        // Navigate to success page
        await router.push({
          name: 'company-subscription-success',
          query: { 
            success: 'true',
            type: 'unlimited'
          }
        })
      }

      return response?.data?.createUnlimitedSubscription
    } catch (error: any) {
      appStore.hideAppLoader()
      appStore.showSnack(`Subscription failed: ${error.message}`, 'error')
      throw error
    }
  }

  // Generate invoice for unlimited subscription (pay by invoice)
  const handleGenerateUnlimitedInvoice = async () => {
    try {
      const plan = activePricingPlan.value
      if (!plan) {
        appStore.showSnack('No pricing plan available', 'error')
        return
      }

      if (!customerFormStore.isCustomerFormValid) {
        appStore.showSnack('Please fill in all billing details', 'error')
        return
      }

      appStore.showAppLoader()

      const response = await generateUnlimitedInvoice({
        companyId: authStore.companyId,
        priceId: plan.stripePriceId || plan.priceId,
        customerId: stripeCustomer.value?.id || companyStore?.getCompany?.stripeCustomerId,
        durationDays: plan.durationDays,
        monthlyPrice: plan.price,
        customerDetails: {
          email: customerFormStore.email,
          name: customerFormStore.name,
          address: {
            line1: customerFormStore.addressLine1,
            line2: customerFormStore.addressLine2,
            city: customerFormStore.city,
            country: customerFormStore.country,
            postal_code: customerFormStore.postalCode,
          }
        },
        couponId: subscriptionStore.getCouponDetails?.id,
        promoCode: subscriptionStore.promoCode,
        currency: plan.currency || 'EUR'
      })

      if (response?.data?.generateUnlimitedSubscriptionInvoice?.id) {
        appStore.hideAppLoader()
        appStore.showSnack(
          `Invoice sent to ${customerFormStore.email}. Your subscription will be activated upon payment.`
        )
        
        // Clear promo code if used
        subscriptionStore.removePromoCode()
        
        // Navigate to success page
        await router.push({
          name: 'company-subscription-success',
          query: { 
            success: 'true',
            type: 'unlimited',
            invoice: 'sent'
          }
        })
      }

      return response?.data?.generateUnlimitedSubscriptionInvoice
    } catch (error: any) {
      appStore.hideAppLoader()
      appStore.showSnack(`Failed to generate invoice: ${error.message}`, 'error')
      throw error
    }
  }

  // Sync unlimited subscription status and activate job adverts if active
  const handleSyncUnlimitedSubscription = async () => {
    try {
      const response = await syncUnlimitedSubscription({
        companyId: authStore.companyId
      })

      // The mutation returns a boolean indicating if the subscription is active
      const isActive = response?.data?.syncUnlimitedSubscription
      
      if (isActive) {
        appStore.showSnack('Unlimited subscription is active.')
      } else {
        appStore.showSnack('No active unlimited subscription found.', 'info')
      }

      return isActive
    } catch (error: any) {
      console.error('Failed to sync subscription:', error)
      return false
    }
  }

  return {
    state: {
      activePricingPlan,
      stripeCustomer,
      paymentMethods,
      loadingCreateUnlimited,
      loadingGenerateInvoice,
      loadingActivateJobs,
      loadingSyncSubscription,
      loadingPaymentMethods,
    },
    actions: {
      handleSubscribeToUnlimited,
      handleGenerateUnlimitedInvoice,
      handleSyncUnlimitedSubscription,
      loadCustomer,
      loadPaymentMethods,
      handleFetchPromoCode,
    }
  }
}
