import { inlineTranslate } from '@/utils/utils'
import { computed } from 'vue'

export const SubscriptionHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Advert'),
      sortKey: 'jobAdvert.title',
      key: 'jobAdvert.title',
    },
    {
      title: inlineTranslate('Amount'),
      key: 'amountTotal',
    },
    {
      title: inlineTranslate('Status'),
      key: 'status',
    },
    {
      title: inlineTranslate('Actions'),
      key: 'actions',
    },
  ]
})
