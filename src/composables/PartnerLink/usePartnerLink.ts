import { apolloClient } from '@/api/middleware/apolloClient'
import { useQuery, useMutation, provideApolloClient } from '@vue/apollo-composable'
import { GET_ALL_PARTNER_LINKS } from '@/api/graphql/queries/partnerLinkQueries'
import {
    CREATE_PARTNER_LINK,
    UPDATE_PARTNER_LINK,
    DELETE_PARTNER_LINK
} from '@/api/graphql/mutations/partnerLinkMutations'
import { useAppStore } from '@/stores/appStore'

export const usePartnerLink = () => {
    provideApolloClient(apolloClient)

    const appStore = useAppStore()

    // Query to get all partner links
    const {
        result: partnerLinksResult,
        loading: loadingPartnerLinks,
        refetch: refetchPartnerLinks,
    } = useQuery(GET_ALL_PARTNER_LINKS, {}, {
        prefetch: false,
    })

    const partnerLinkList = computed(() => partnerLinksResult.value?.partnerLinks || [])

    // Mutation to create partner link
    const {
        mutate: createPartnerLinkMutation,
        loading: loadingCreate,
    } = useMutation(CREATE_PARTNER_LINK)

    // Mutation to update partner link
    const {
        mutate: updatePartnerLinkMutation,
        loading: loadingUpdate,
    } = useMutation(UPDATE_PARTNER_LINK)

    // Mutation to delete partner link
    const {
        mutate: deletePartnerLinkMutation,
        loading: loadingDelete,
    } = useMutation(DELETE_PARTNER_LINK)

    const loadPartnerLinks = async () => {
        try {
            await refetchPartnerLinks()
        } catch (error) {
            console.error('Error loading partner links:', error)
            appStore.showSnack('Error loading partner links')
        }
    }

    const createPartnerLink = async (input: { name: string; url: string }) => {
        try {
            const result = await createPartnerLinkMutation({ input })
            await refetchPartnerLinks()
            appStore.showSnack('Partner link created successfully')
            return result
        } catch (error) {
            console.error('Error creating partner link:', error)
            appStore.showSnack('Error creating partner link')
            throw error
        }
    }

    const updatePartnerLink = async (input: { id: string; name?: string; url?: string }) => {
        try {
            const result = await updatePartnerLinkMutation({ input })
            await refetchPartnerLinks()
            appStore.showSnack('Partner link updated successfully')
            return result
        } catch (error) {
            console.error('Error updating partner link:', error)
            appStore.showSnack('Error updating partner link')
            throw error
        }
    }

    const deletePartnerLink = async (id: string) => {
        try {
            const result = await deletePartnerLinkMutation({ id })
            await refetchPartnerLinks()
            appStore.showSnack('Partner link deleted successfully')
            return result
        } catch (error) {
            console.error('Error deleting partner link:', error)
            appStore.showSnack('Error deleting partner link')
            throw error
        }
    }

    return {
        state: {
            partnerLinkList,
            loadingPartnerLinks,
            loadingCreate,
            loadingUpdate,
            loadingDelete,
        },
        actions: {
            loadPartnerLinks,
            createPartnerLink,
            updatePartnerLink,
            deletePartnerLink,
        },
    }
} 