export default interface Applicant {
  availableFrom: Date
  birthday: Date
  city: string
  description: string | undefined
  documents: string[] | undefined
  environment:
    | {
        id: number
        value: string
      }
    | undefined
  firstName: string
  graduation: string | undefined
  id: string
  lastActive: Date
  lastName: string
  profileImageUrl: string
  personality:
    | {
        id: number
        value: string
      }
    | undefined
  profileImage: string
  schoolName: string
  strengths: string[] | undefined
  subjects: string[] | undefined
  weaknesses: string[] | undefined
}
