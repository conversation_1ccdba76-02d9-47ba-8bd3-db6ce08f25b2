import type { GeoPoint } from '@firebase/firestore'
import type ImageCropperResult from '../image-cropper-result'

type AddEditJobAd = AddEditJobAdPraktikum | AddEditJobAdAusbildung

interface AddEditJobAdPraktikum {
  id?: string
  title: string
  jobAdType: 'praktikum'
  address: string
  city: string
  coordinates: GeoPoint
  district: string
  startDate: Date
  activeFromDate: Date
  selectedCategories: string[]
  selectedUsers: string[]
  headerResult?: ImageCropperResult
  description: string
  detailDescription: string
  uploadedImages: string[]
  imagesToDelete: string[]
}

interface AddEditJobAdAusbildung {
  id?: string
  title: string
  jobAdType: 'ausbildung'
  address: string
  city: string
  coordinates: GeoPoint
  district: string
  workHours: string
  holidayDays: string
  educationDuration: number
  gehalt: string
  gehalt2?: string
  gehalt3?: string
  startDate: Date
  activeFromDate: Date
  selectedCategories: string[]
  selectedUsers: string[]
  headerResult?: ImageCropperResult
  description: string
  detailDescription: string
  uploadedImages: string[]
  imagesToDelete: string[]
}

export default AddEditJobAd
export type {
  AddEditJobAdAusbildung,
  AddEditJobAdPraktikum,
}
