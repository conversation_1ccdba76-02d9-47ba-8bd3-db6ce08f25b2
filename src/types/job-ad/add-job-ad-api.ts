import type ImageCropperResult from '../image-cropper-result'

type GeoPoint = {
  latitude: number
  longitude: number
}

interface G {
  geopoint: GeoPoint
  geohash: string
}

type AddJobAdApi = {
  approved: boolean
  categories: string[]
  jobAdType: 'praktikum' | 'ausbildung'
  companyId: string
  responsibleUsers: string[]
  deleted: boolean
  declined: boolean
  createdAt: Date
  companyName: string
  startedAt: Date
  headerResult: ImageCropperResult
  coordinates: GeoPoint
  dynamicLink: string
  startDate: Date
  bookmarks: number
  headerImage: string
  district: string
  address: string
  imageUrl: string
  title: string
  description: string
  expired: boolean
  activeFromDate: Date
  bookmarked: number
  logoUrl?: string
  logoImage?: string
  unreadCounter: number
  pause: boolean
  applicants: number
  matches: number
  detailDescription: string
  impressions: number
  duration: string
  city: string
  declineReason: string
  id: string
  gehalt?: string
  gehalt2?: string
  gehalt3?: string
  workHours: string
  holidayDays: string
  educationDuration?: number
  lat: GeoPoint['latitude']
  long: GeoPoint['longitude']
  selectedUsers: string[]
  selectedCategories: string[]
  applicantCount: number
  bookmarkCount: number
  isDeclined: boolean
  isDeleted: boolean
  g: G
  endDate: string
  status: string
  type: string
}

export default AddJobAdApi
