import type { GeoPoint } from '@google-cloud/firestore'

type JobAd = {
  approved: boolean
  categories: string[]
  jobAdType: 'praktikum' | 'ausbildung'
  companyId: string
  responsibleUsersIds: string[]
  deleted: boolean
  declined: boolean
  isDraft?: boolean
  createdAt: Date
  companyName: string
  startedAt: Date
  coordinates: GeoPoint
  dynamicLink: string
  startDate: Date
  bookmarks: number
  district: string
  address: string
  imageUrl: string
  title: string
  description: string
  expired: boolean
  activeFromDate: Date
  bookmarked: number
  logoImage?: string
  unreadCounter: number
  pause: boolean
  applicants: number
  matches: number
  headerImage: string
  detailDescription: string
  impressions: number
  duration: number
  city: string
  declineReason: string
  id: string
  gehalt?: string
  gehalt2?: string
  gehalt3?: string
  workHours?: string
  holidayDays?: string
  educationDuration?: number
}

export default JobAd
