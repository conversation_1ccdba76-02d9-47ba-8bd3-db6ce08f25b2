export interface PaymentCardContent {
  brand: string
  last4: string
  id: string
  country: string
  name: string
  value: string
  exp_month: number
  exp_year: number
  isDefault: boolean
  icon?: string
  images?: string
}

export interface GridColumn {
  cols?: string
  sm?: string
  md?: string
  lg?: string
  xl?: string
  xxl?: string
}

interface Payment {
  name: any
  id: any
  value: any
  isDefault: boolean
  type: string
}

interface CardPayment extends Payment {
  brand: any
  last4: any
  country: any
  exp_month: any
  exp_year: any
}

interface SepaDebitPayment extends Payment {
  bank_code: any
  country: string
  last4: any
  branch_code: any
  fingerprint: any
}
