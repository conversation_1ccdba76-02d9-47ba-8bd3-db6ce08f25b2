interface LikeMatch {
  id: string
  state: string
  applicantId: string
  applicantImageUrl: string
  birthday: Date
  chatId?: string
  city?: string
  deleted: boolean
  deletedFromApplicant: boolean
  graduation?: string
  isBookmark: boolean
  isDislike: boolean
  isLike: boolean
  isMatch: boolean
  isNew: boolean
  jobAdId: string
  likeDate: Date
  name: string
  status: string
  title: string
}

export default LikeMatch
