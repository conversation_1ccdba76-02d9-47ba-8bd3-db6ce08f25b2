import type ImageCropperResult from '../image-cropper-result'

interface AddEditCompany {
  avatarResult?: ImageCropperResult
  logoResult?: ImageCropperResult
  headerResult?: ImageCropperResult
  address: string
  companyName: string
  city: string
  lat: number
  long: number
  detailContent: string
  foundingYear: number
  mitarbeiter: number
  uploadedImages: string[]
  imagesToDelete: string[]
}

export default AddEditCompany
