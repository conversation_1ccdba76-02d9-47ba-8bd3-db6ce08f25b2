// Write your overrides

html {
  font-size: 1.05rem;
}

tr.v-data-table__tr {
  cursor: pointer;
}

.table-action-button {
  margin-left: -20px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.h-screen {
  min-height: 100vh !important;
  height: unset !important;
}

.custom-card-title {
  font-size: 1.25em;
  font-weight: 600;
  color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
}

.layout-nav-type-vertical .layout-vertical-nav .nav-header {
  padding-left: 0;
}

.v-messages__message {
  line-height: normal;
}

/*
h1, .h1 {
  font-weight: 700!important;
  font-size: 28px;
  color: #000;
  line-height: 34px;
}
*/
.v-btn {
  letter-spacing: 0.0178em!important;
  text-transform: none;
}

.v-btn--variant-plain {
  opacity: 0.8;
}

.v-card-title {
  font-weight: 700;
}

.v-selection-control {
  align-items: start;
}

.v-label {
  white-space: normal;
}

.chip-wrapper {
  padding: 10px 0;
  gap: 10px;
}

.v-table__wrapper {
  border-radius: 0;

  tr.v-data-table__tr:hover td {
    background-color: #ffffff0a !important;
  }
}

// Sweetalert styling

.swal2-popup.dark {
  background: #2f3449;
  color: white;
}

.swal2-popup.light {
  background: white;
  color: black;
}

.swal2-popup {
  .swal2-title {
    font-size: 1.25em;
  }

  .swal2-html-container {
    font-size: 1em;
  }
}

.v-overlay__scrim {
  background: transparent;
  backdrop-filter: blur(10px);
  opacity: 100%;
}

.vld-overlay.is-active {
  z-index: 9999!important;
}


.v-navigation-drawer {
  z-index: 1006!important;
}
.v-navigation-drawer__scrim {
  z-index: 1005!important;
}

tr.v-data-table__tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
