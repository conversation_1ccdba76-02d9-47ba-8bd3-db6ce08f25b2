{
  "compilerOptions": {
    //"allowJs": true,
    "baseUrl": "./",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "isolatedModules": true,
    "strict": true,
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@themeConfig": [
        "themeConfig.ts"
      ],
      "@layouts/*": [
        "src/@layouts/*"
      ],
      "@layouts": [
        "src/@layouts"
      ],
      "@core/*": [
        "src/@core/*"
      ],
      "@core": [
        "src/@core"
      ],
      "@images/*": [
        "src/assets/images/*"
      ],
      "@styles/*": [
        "src/styles/*"
      ],
      "@axios": [
        "src/plugins/axios"
      ],
      "@validators": [
        "src/@core/utils/validators"
      ]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ],
    "skipLibCheck": true,
    "typeRoots": [
      "./node_modules/@types"
    ],
    "types": [
      "vite/client",
      "vite-plugin-pages/client",
      "vite-plugin-vue-layouts/client",
      "google.maps",
      "unplugin-vue-define-options/macros-global",
      "pusher-js"
    ]
  },
  "include": [
    "vite.config.*",
    "env.d.ts",
    "shims.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "themeConfig.ts",
    "decs.d.ts"
  ],
  "exclude": [
    "dist",
    "node_modules",
    "src/@iconify/*"
  ]
}
