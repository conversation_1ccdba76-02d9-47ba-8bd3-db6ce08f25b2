<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bridge Dashboard</title>
  <link rel="stylesheet" type="text/css" href="/loader.css" />
  <script async src="https://js.stripe.com/v3/pricing-table.js"></script>
</head>

<body>
  <div id="app">
    <div id="loading-bg">
      <div class="loading-logo">
        <img src="/bridge-logo.png" alt="Logo" />
      </div>
      <div class="loading">
        <div class="effect-1 effects"></div>
        <div class="effect-2 effects"></div>
        <div class="effect-3 effects"></div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
  <script>
    const loaderColor = localStorage.getItem('Bridge Dashboard-initial-loader-bg') || '#24293d'
    const primaryColor = localStorage.getItem('Bridge Dashboard-loader-color') || '#e32753'

    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)
    if (primaryColor)
      document.documentElement.style.setProperty('--initial-loader-color', primaryColor)

  </script>
</body>

</html>
