# Brainstoarming Bridge Dashboard

Dieses Dokument einfach eine Spielwiese für die Gedanken zu Bridge 

## Datenmodell 

Allgemein muss hierbei beachtet werden, dass wir mit Firestore aktuell eine NoSQL Datenbank haben. <PERSON>er soll<PERSON>, anders als bei <PERSON>QL, nicht vordergründig über die Relationen und Normalisierungen nachgedacht werden. Es gibt keine eingebaute "join" Funk<PERSON>, deshalb organisiert man seine Dokumente normalerweise so, wie sie nachher auch angezeigt werden sollen. Das führt natürlich zu Duplizierung von Daten, was bei NoSQL aber vollkommen normal ist. 

### Company

Aktuell gibt es eine Collection "Company". Jedes Company document enthält genau die Daten, die im CompanyForm auch bearbeitet werden können (plus den geohash, der jedes mal neu berechnet wird, wenn das Dokument bearbeitet wird)

Passt soweit eigentlich

### JobAd 

Hier ist es schon etwas "wilder". Ein JobAd document enthält neben den bearbeitbaren Informationen (Titel, Beschreibung, Gehalt, etc.) auch Metadaten. Dazu gehören: 

- Impressionen, Likes, Bookmarks und Matches 
- Declined, Approved (Muss aber in dem Dokument bleiben wegen der query)
- Später auch Payment daten? 
    - Eventuell "Approved" nicht bearbeitbar machen und nach Payment setzen.
    - Gesonderte Subcollection für Payments?


### Applicant

Hier ist eigentlich alles klar, ähnlich wie bei der company. Im Wesentlichen tauchen hier nur die Felder auf, die auch vom Schüler bearbeitet werden können. Zusätzlich noch "LastActive".


## Payment 

Wie regeln wir das Payment? Hierbei gibt es verschiedene Ansätze:

### Credit-Basiert

Eine Möglichkeit wäre, ähnlich wie bei Adobe, mit einem Credit-Konto zu arbeiten. Unternehmen können sich Credits direkt über das Dashboard kaufen. Um eine Stellenanzeige zu schalten, wird diese nun nicht direkt mit Kreditkarte bezahlt, sondern mit den bereits vorhandenen Coins.

### Direkte Zahlung bei jeder Erstellung 

Zahlung eingebaut in den Erstellungsprozess einer Stellenanzeige. Nach erstmaliger Zahlung könnte man die Zahlungsdaten evtl. auch hinterlegen, sodass sie nicht jedes Mal erneut eingegeben werden müssen. Das ist allerdings vielleicht etwas nervig, wenn mehrere Anzeigen nacheinander erstellt werden sollen (Dauert deutlich länger, auch wenn Zahlungsdaten hinterlegt sind.).
Vielleicht geht Stripe Checkout dann nicht mehr?