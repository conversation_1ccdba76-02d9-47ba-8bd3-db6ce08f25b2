{"extends": ["stylelint-config-standard-scss", "stylelint-config-idiomatic-order"], "plugins": ["stylelint-use-logical-spec"], "overrides": [{"files": ["**/*.scss"], "customSyntax": "postcss-scss"}, {"files": ["**/*.vue"], "customSyntax": "postcss-html"}], "rules": {"max-line-length": [120, {"ignore": "comments"}], "liberty/use-logical-spec": true, "selector-class-pattern": null, "color-function-notation": null}}