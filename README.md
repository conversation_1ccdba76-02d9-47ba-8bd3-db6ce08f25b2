# Bridge Company Dashboard

## Überlegungen 

- "Zu komplizierte" Backend-Funktionalität in Server(API) auslagern 
    - Delete JobAd: Es gibt hier ohnehin eine CF, die getriggert wird -> Direkt über CF

## Custom API Client usage

GitHub Copilot: Here's an example of how you might call the `apiClient` function with all values present:

```typescript
apiClient({
  method: "POST",
  path: "/users",
  urlParams: ['1', '2'],
  body: { name: "<PERSON>", email: "<EMAIL>" },
  queryParams: { sort: "desc" },
  headers: { "Custom-Header": "CustomValue" },
}).then((data) => {
  console.log(data);
}).catch((error) => {
  console.error(error);
});
```

In this example:

- `method` is `"POST"`, which means the request will be a POST request.
- `path` is `"/users"`, which will be appended to the base URL.
- `urlParams` is `[1,2]`, which means `"/1/2"` will be appended to the URL.
- `body` is `{ name: "<PERSON>", email: "<EMAIL>" }`, which will be sent as the request body.
- `queryParams` is `{ sort: "desc" }`, which will be sent as query parameters (`?sort=desc`).
- `headers` is `{ "Custom-Header": "CustomValue" }`, which will be sent as additional headers.

The resulting URL will be something like `http://url/users/1/2?sort=desc`.

firebase deploy --only hosting:bridge-dev
CUID: 4e6dff65-5699-40d7-a6da-beafc0f6509a  :: UID: "1fafd7bb-01fb-4d56-a32f-ba9e0b024d7a"
LocalCUID: d45e9203-4c58-498b-b534-02adcea98fe1
